[57512:4068:0802/220820.785:INFO:components\enterprise\browser\controller\chrome_browser_cloud_management_controller.cc:202] No machine level policy manager exists.
[1728:49628:0802/220820.976:INFO:third_party\webrtc\rtc_base\cpu_info.cc:57] Available number of cores: 16
[38196:58392:0802/220820.995:INFO:third_party\webrtc\rtc_base\cpu_info.cc:57] Available number of cores: 16
[6464:10888:0802/220821.085:VERBOSE1:components\viz\service\main\viz_main_impl.cc:86] VizNullHypothesis is disabled (not a warning)
[39956:71272:0802/220821.649:INFO:third_party\webrtc\rtc_base\cpu_info.cc:57] Available number of cores: 16
[6864:14304:0802/220821.679:INFO:third_party\webrtc\rtc_base\cpu_info.cc:57] Available number of cores: 16
[57512:4068:0802/220822.388:INFO:CONSOLE:1] "TypeError: Cannot read properties of undefined (reading 'network')", source: https://lf-content-ecology.toutiaostatic.com/obj/tt-content-ecology-fe/pgcfe/mp/web/resource/vendors~a20d0b72_2864a4eef8f54aa1.js (1)
[6464:10888:0802/220822.504:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0C02E0074480000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[57512:4068:0802/220822.608:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0C02E0074480000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (0)
[6464:10888:0802/220823.366:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0C02E0074480000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[57512:4068:0802/220823.419:INFO:CONSOLE:3] "[GroupMarkerNotSet(crbug.com/242999)!:A0C02E0074480000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (3)
[57512:4068:0802/220823.633:INFO:CONSOLE:1] "TypeError: Cannot read properties of undefined (reading 'network')", source: https://lf-content-ecology.toutiaostatic.com/obj/tt-content-ecology-fe/pgcfe/mp/web/resource/vendors~a20d0b72_2864a4eef8f54aa1.js (1)
[6464:10888:0802/220824.126:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0F02E0074480000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[57512:4068:0802/220824.191:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0F02E0074480000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (0)
[6464:10888:0802/220826.369:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0C02E0074480000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[57512:4068:0802/220826.376:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0C02E0074480000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (0)
[6464:10888:0802/220826.386:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0502F0074480000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[57512:4068:0802/220826.429:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0502F0074480000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (0)
[21968:17324:0802/220826.456:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[21968:17324:0802/220826.557:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[21968:17324:0802/220826.649:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
[21968:17324:0802/220826.650:ERROR:net\socket\ssl_client_socket_impl.cc:896] handshake failed; returned -1, SSL error code 1, net_error -101
