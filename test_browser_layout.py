#!/usr/bin/env python3
"""
测试浏览器九宫格布局功能
"""

import os
import sys
import time
from PyQt5.QtWidgets import QApplication

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)


def test_layout_manager():
    """测试布局管理器基本功能"""
    print("🧪 测试浏览器九宫格布局管理器")
    print("=" * 50)
    
    try:
        from app.utils.browser_window_layout_manager import get_browser_layout_manager
        
        # 创建QApplication（布局管理器需要）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 获取布局管理器
        layout_manager = get_browser_layout_manager()
        print(f"✅ 布局管理器创建成功")
        
        # 获取初始状态
        status = layout_manager.get_layout_status()
        print(f"📊 初始状态:")
        print(f"   - 启用状态: {status.get('enabled', False)}")
        print(f"   - 总位置数: {status.get('total_positions', 0)}")
        print(f"   - 可用位置: {status.get('available_positions', 0)}")
        print(f"   - 屏幕尺寸: {status.get('screen_info', {}).get('width', 'N/A')}x{status.get('screen_info', {}).get('height', 'N/A')}")
        
        # 测试位置分配
        print(f"\n🎯 测试位置分配:")
        test_accounts = ["test_account_1", "test_account_2", "test_account_3"]
        
        allocated_positions = []
        for account_id in test_accounts:
            position = layout_manager.allocate_window_position(account_id)
            if position:
                allocated_positions.append((account_id, position))
                print(f"   ✅ 账号 {account_id}: 位置{position['index']} ({position['x']}, {position['y']}) {position['width']}x{position['height']}")
            else:
                print(f"   ❌ 账号 {account_id}: 分配失败")
        
        # 获取更新后的状态
        status = layout_manager.get_layout_status()
        print(f"\n📊 分配后状态:")
        print(f"   - 已占用位置: {status.get('occupied_positions', 0)}")
        print(f"   - 可用位置: {status.get('available_positions', 0)}")
        print(f"   - 活跃窗口: {status.get('active_windows', {})}")
        
        # 测试位置释放
        print(f"\n🔄 测试位置释放:")
        for account_id, position in allocated_positions:
            layout_manager.release_window_position(account_id)
            print(f"   ✅ 已释放账号 {account_id} 的位置")
        
        # 获取释放后的状态
        status = layout_manager.get_layout_status()
        print(f"\n📊 释放后状态:")
        print(f"   - 已占用位置: {status.get('occupied_positions', 0)}")
        print(f"   - 可用位置: {status.get('available_positions', 0)}")
        
        print(f"\n✅ 布局管理器测试完成")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_layout_config_dialog():
    """测试布局配置对话框"""
    print("\n🧪 测试布局配置对话框")
    print("=" * 50)
    
    try:
        from app.dialogs.browser_layout_config_dialog import BrowserLayoutConfigDialog
        
        # 创建QApplication
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建对话框
        dialog = BrowserLayoutConfigDialog()
        print(f"✅ 配置对话框创建成功")
        
        # 显示对话框（非模态，用于测试）
        dialog.show()
        print(f"✅ 配置对话框显示成功")
        
        # 等待一段时间然后关闭
        time.sleep(2)
        dialog.close()
        
        print(f"✅ 配置对话框测试完成")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试配置对话框时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_browser_integration():
    """测试浏览器集成功能"""
    print("\n🧪 测试浏览器集成功能")
    print("=" * 50)
    
    try:
        from app.utils.browser_window_layout_manager import apply_browser_window_layout
        
        print("✅ 浏览器集成函数导入成功")
        
        # 模拟浏览器对象
        class MockDriver:
            def __init__(self):
                self.position = (0, 0)
                self.size = (800, 600)
            
            def set_window_position(self, x, y):
                self.position = (x, y)
                print(f"   🖥️ 模拟设置窗口位置: ({x}, {y})")
            
            def set_window_size(self, width, height):
                self.size = (width, height)
                print(f"   📐 模拟设置窗口尺寸: {width}x{height}")
        
        # 测试应用布局
        mock_driver = MockDriver()
        account_id = "test_integration_account"
        
        result = apply_browser_window_layout(mock_driver, account_id)
        if result:
            print(f"   ✅ 成功应用布局到账号 {account_id}")
            print(f"   📍 最终位置: {mock_driver.position}")
            print(f"   📏 最终尺寸: {mock_driver.size}")
        else:
            print(f"   ⚠️ 未应用布局（可能已禁用）")
        
        print(f"\n✅ 浏览器集成测试完成")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试浏览器集成时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🚀 浏览器九宫格布局功能测试套件")
    print("=" * 60)
    
    # 测试结果
    results = []
    
    # 测试布局管理器
    results.append(("布局管理器", test_layout_manager()))
    
    # 测试配置对话框
    results.append(("配置对话框", test_layout_config_dialog()))
    
    # 测试浏览器集成
    results.append(("浏览器集成", test_browser_integration()))
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print("📋 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n📊 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！九宫格布局功能正常工作")
    else:
        print("⚠️ 部分测试失败，请检查相关功能")
    
    print("\n💡 使用说明:")
    print("1. 在账号管理界面点击'九宫格布局'按钮进行配置")
    print("2. 启用自动排列功能后，单独登录账号时会自动应用九宫格布局")
    print("3. 支持自定义窗口间距和重置布局")
    print("4. 最多支持9个浏览器窗口同时排列")


if __name__ == "__main__":
    main()
