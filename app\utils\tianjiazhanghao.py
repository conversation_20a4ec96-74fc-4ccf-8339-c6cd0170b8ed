#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
头条账号添加工具 - 自动打开浏览器并保存cookie（支持加密）
"""

import os
import json
import time
import traceback
import socket
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import tkinter as tk
from tkinter import messagebox
from app.utils.logger import info, success, error, warning, debug
from app.utils.cookie_encryption import get_cookie_encryptor

def check_network_connection():
    """检查网络连接状态"""
    try:
        # 尝试连接到头条网站
        socket.create_connection(("mp.toutiao.com", 443), timeout=5)
        return True, "网络连接正常"
    except OSError:
        try:
            # 尝试连接到百度
            socket.create_connection(("www.baidu.com", 80), timeout=5)
            return True, "可以连接到百度，但无法连接到头条"
        except OSError:
            return False, "网络连接异常，无法连接到互联网"

def open_browser_and_save_cookie(account_id, remark, cookie_path):
    """
    打开浏览器访问头条媒体平台，等待用户登录，登录成功后保存cookie

    参数:
        account_id (str): 账号ID
        remark (str): 账号备注
        cookie_path (str): cookie保存路径

    返回:
        tuple: (success, message, file_path)
            - success (bool): 是否成功
            - message (str): 成功或失败消息
            - file_path (str): 保存的文件路径
    """
    driver = None
    try:
        info(f"准备为账号 {account_id} 添加Cookie")
        debug(f"Cookie保存路径: {cookie_path}")

        # 检查网络连接
        info("检查网络连接...")
        network_ok, network_msg = check_network_connection()
        if not network_ok:
            error(f"网络连接检查失败: {network_msg}")
            return False, f"添加账号失败: {network_msg}", None
        info(f"网络连接检查结果: {network_msg}")

        # 确保cookie目录存在
        if not os.path.exists(cookie_path):
            info(f"Cookie目录不存在，创建目录: {cookie_path}")
            os.makedirs(cookie_path)

        # 生成cookie文件名
        filename = f"{account_id}.txt"
        if not filename.endswith('.txt'):
            filename += '.txt'

        file_path = os.path.join(cookie_path, filename)
        debug(f"Cookie文件路径: {file_path}")

        # 设置Chrome选项
        chrome_options = Options()
        # 不使用最大化窗口，使用指定窗口大小（半屏）
        # chrome_options.add_argument("--start-maximized")  # 移除最大化窗口
        chrome_options.add_argument("--window-size=960,1080")  # 设置窗口大小为半屏
        chrome_options.add_argument("--disable-infobars")  # 禁用信息栏
        chrome_options.add_argument("--disable-extensions")  # 禁用扩展
        chrome_options.add_argument("--no-proxy-server")  # 禁用代理服务器
        chrome_options.add_argument("--disable-gpu")  # 禁用GPU加速
        chrome_options.add_argument("--no-sandbox")  # 禁用沙盒模式
        chrome_options.add_argument("--disable-dev-shm-usage")  # 禁用/dev/shm使用
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])  # 避免被检测
        chrome_options.add_experimental_option("useAutomationExtension", False)  # 避免被检测

        # 使用ChromeDriver管理器创建浏览器
        try:
            info("尝试使用ChromeDriver管理器创建浏览器...")
            from app.utils.chromedriver_manager import create_chrome_driver
            driver = create_chrome_driver(chrome_options)
            info("已成功初始化Chrome浏览器")
        except Exception as e:
            # 如果ChromeDriver管理器失败，尝试直接创建
            warning(f"ChromeDriver管理器创建失败，尝试直接创建: {str(e)}")
            try:
                driver = webdriver.Chrome(options=chrome_options)
                info("已使用直接方式初始化Chrome浏览器")
            except Exception as e2:
                # 如果仍然失败，抛出异常
                error(f"所有初始化Chrome浏览器的方法都失败: {str(e2)}")
                raise Exception(f"无法启动Chrome浏览器，请确保已安装Chrome浏览器并且版本兼容: {str(e2)}")

        # 设置页面加载超时时间
        driver.set_page_load_timeout(60)  # 增加到60秒

        # 打开头条媒体平台登录页面
        info("正在打开头条媒体平台登录页面...")

        # 最多尝试3次
        max_attempts = 3
        for attempt in range(1, max_attempts + 1):
            try:
                info(f"第 {attempt} 次尝试打开头条媒体平台...")

                # 先尝试打开一个简单的页面，确保浏览器正常工作
                if attempt > 1:
                    info("先尝试打开百度...")
                    driver.get("https://www.baidu.com")
                    time.sleep(2)

                # 然后打开头条媒体平台
                driver.get("https://mp.toutiao.com/profile_v4/index")
                info("成功打开头条媒体平台")
                break
            except Exception as e:
                warning(f"第 {attempt} 次打开头条页面失败: {str(e)}")
                try:
                    # 尝试停止加载
                    driver.execute_script("window.stop();")
                except Exception:
                    pass

                if attempt == max_attempts:
                    error("多次尝试打开头条媒体平台均失败")
                    # 尝试打开备用登录页面
                    try:
                        info("尝试打开备用登录页面...")
                        driver.get("https://sso.toutiao.com")
                        time.sleep(2)
                    except Exception as e2:
                        warning(f"打开备用登录页面也失败: {str(e2)}")
                else:
                    # 等待一段时间后重试
                    retry_wait = 3 * attempt  # 逐渐增加等待时间
                    info(f"等待 {retry_wait} 秒后重试...")
                    time.sleep(retry_wait)

        # 等待用户手动登录 (最多等待5分钟)
        info("等待用户登录，最多等待5分钟...")
        login_success = False
        max_wait_time = 300  # 5分钟
        start_time = time.time()

        # 用于验证登录状态的XPath
        login_success_xpath = "/html/body/div[1]/div/div[1]/div/div/div[1]/div/a/span/span[1]"

        while not login_success and (time.time() - start_time) < max_wait_time:
            # 检查是否登录成功 (可以通过检查URL或特定元素来判断)
            try:
                # 检查URL是否符合登录成功条件
                if "/profile_v4/index" in driver.current_url and not "/login" in driver.current_url:
                    try:
                        # 使用提供的XPath检查是否有用户信息元素
                        WebDriverWait(driver, 2).until(
                            EC.presence_of_element_located((By.XPATH, login_success_xpath))
                        )
                        login_success = True
                        info("检测到用户已登录 (XPath验证)")
                        break
                    except Exception as e:
                        debug(f"通过XPath检测登录元素失败: {str(e)}，尝试备用方法")
                        try:
                            # 备用方法：使用CSS选择器检查
                            element = driver.find_element(By.CSS_SELECTOR, ".user-name, .username")
                            if element:
                                login_success = True
                                info("检测到用户已登录 (CSS选择器验证)")
                                break
                        except Exception:
                            # 检查页面内容
                            if "登录" not in driver.page_source and "验证码" not in driver.page_source:
                                login_success = True
                                info("检测到用户已登录 (页面内容验证)")
                                break
            except Exception as e:
                debug(f"检查登录状态时出错: {str(e)}")

            # 每2秒检查一次
            time.sleep(2)

        if not login_success:
            error(f"账号 {account_id} 登录超时")
            return False, "登录超时，请重试", None

        # 创建一个确认对话框，让用户确认是否保存cookie
        info("显示确认对话框...")
        root = tk.Tk()
        root.withdraw()  # 隐藏主窗口

        # 显示确认对话框
        confirm = messagebox.askyesno(
            "确认保存",
            f"已成功登录账号 {account_id}\n点击'是'保存cookie，点击'否'取消",
            parent=root
        )

        root.destroy()

        if not confirm:
            warning(f"用户取消保存账号 {account_id} 的cookie")
            return False, "用户取消保存", None

        # 获取cookies
        info("获取cookies...")
        cookies = driver.get_cookies()

        # 格式化并保存cookies
        cookie_dict = {}
        for cookie in cookies:
            cookie_dict[cookie['name']] = cookie['value']

        # 添加账号信息
        cookie_data = {
            "accountId": account_id,
            "remark": remark or account_id,
            "cookies": cookie_dict
        }

        # 自动使用AES-256加密保存Cookie
        info(f"开始保存cookies到文件: {file_path}")
        try:
            # 获取Cookie加密器
            encryptor = get_cookie_encryptor()

            # 检查加密功能是否可用
            if encryptor.available:
                # 加密Cookie数据
                encrypted_data = encryptor.encrypt_cookie_data(cookie_data)

                # 创建加密文件结构
                encrypted_file_data = {
                    "encrypted": True,
                    "version": "1.0",
                    "algorithm": "AES-256",
                    "data": encrypted_data,
                    "account_id": account_id,
                    "remark": remark or account_id
                }

                # 保存加密文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(encrypted_file_data, f, ensure_ascii=False, indent=2)

                success(f"账号 {account_id} 添加成功，文件路径: {file_path}")
                debug("Cookie已自动使用AES-256算法加密保存")
                return True, f"账号 {account_id} 添加成功", file_path
            else:
                # 加密功能不可用，使用明文保存
                warning("Cookie加密功能不可用（cryptography库未安装），使用明文保存")
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(cookie_data, f, ensure_ascii=False, indent=2)

                success(f"账号 {account_id} 添加成功，文件路径: {file_path}")
                warning("注意：Cookie以明文格式保存，建议安装cryptography库以启用加密功能")
                return True, f"账号 {account_id} 添加成功", file_path

        except Exception as encrypt_error:
            # 如果加密失败，回退到明文保存
            warning(f"Cookie加密失败，回退到明文保存: {str(encrypt_error)}")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(cookie_data, f, ensure_ascii=False, indent=2)

            success(f"账号 {account_id} 添加成功，文件路径: {file_path}")
            warning("注意：由于加密失败，Cookie以明文格式保存")
            return True, f"账号 {account_id} 添加成功", file_path

    except Exception as e:
        error_msg = f"添加账号时发生错误: {str(e)}"
        error(error_msg)
        error(traceback.format_exc())
        return False, error_msg, None

    finally:
        # 确保关闭浏览器
        if driver:
            try:
                info("关闭浏览器...")
                driver.quit()
            except Exception:
                warning("关闭浏览器时出错")
                pass

if __name__ == "__main__":
    # 测试代码
    account_id = "test_account"
    remark = "测试账号"
    cookie_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "cookies")

    success_flag, message, file_path = open_browser_and_save_cookie(account_id, remark, cookie_path)
    print(f"结果: {success_flag}")
    print(f"消息: {message}")
    print(f"文件路径: {file_path}")