#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
账号管理选项卡模块
"""

# pyright: reportOptionalMemberAccess=false
# pyright: reportAttributeAccessIssue=false

# 添加项目根目录到Python路径（用于直接运行此文件时的导入修复）
import sys
import os
if __name__ == "__main__":
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.dirname(os.path.dirname(current_dir))
    if project_root not in sys.path:
        sys.path.insert(0, project_root)

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout,
                          QTableWidget, QTableWidgetItem, QPushButton,
                          QHeaderView, QLabel, QCheckBox, QFrame,
                          QLineEdit, QSizePolicy, QStyle, QMessageBox,
                          QDialog, QFormLayout, QApplication,
                          QFileDialog, QSpinBox, QGroupBox, QTextEdit)
from PyQt5.QtCore import Qt, QSize, pyqtSignal, QTimer
from PyQt5.QtGui import QColor, QFont, QBrush
import time
from app.utils.tianjiazhanghao import open_browser_and_save_cookie

# 尝试导入chardet模块，如果失败则设为None
CHARDET_AVAILABLE = False
chardet = None

try:
    import chardet  # type: ignore
    CHARDET_AVAILABLE = True
except ImportError:
    # chardet不可用，将使用备用编码检测方案
    pass

def detect_file_encoding(content):
    """检测文件编码，如果chardet不可用则使用备用方案

    Args:
        content (bytes): 文件的二进制内容

    Returns:
        str: 检测到的编码名称
    """
    if CHARDET_AVAILABLE and chardet is not None:
        try:
            result = chardet.detect(content)
            detected_encoding = result.get('encoding')
            confidence = result.get('confidence', 0)

            # 只有当置信度足够高时才使用检测结果
            if detected_encoding and confidence > 0.7:
                return detected_encoding
        except Exception:
            # chardet检测失败，继续使用备用方案
            pass

    # 备用编码检测方案 - 尝试常见的中文编码
    common_encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'big5']

    for encoding in common_encodings:
        try:
            content.decode(encoding)
            return encoding
        except (UnicodeDecodeError, UnicodeError):
            continue

    # 如果所有编码都失败，返回utf-8作为默认值
    return 'utf-8'
from app.utils.dengluzhanghaocookie import login_with_cookie
from app.utils.thread_worker import run_in_thread
from app.utils.logger import info, success, warning, error, debug
import json
from app.utils.piliang_cunggao import run_batch_cunggao
# 公告功能已移至主界面顶部工具栏
from app.dialogs.toutiao_spider_dialog import open_toutiao_spider_dialog
# Cookie加密功能将在需要时动态导入，避免启动时的导入错误

class AccountTab(QWidget):
    # 定义信号
    load_account_clicked = pyqtSignal()
    cookie_count_changed = pyqtSignal(int)  # 添加cookie数量变化信号
    status_update_signal = pyqtSignal(str)  # 状态更新信号
    # 表格刷新完成信号（已禁用，避免段错误）
    # table_refresh_completed = pyqtSignal()

    # 系统日志相关信号
    batch_progress_updated = pyqtSignal(int, int)  # current, total
    batch_log_updated = pyqtSignal(str, str, str)  # source, level, message
    account_progress_updated = pyqtSignal(str, str, int)  # account_id, status, progress

    # 线程清理信号
    cleanup_batch_signal = pyqtSignal()  # 批量存稿清理信号

    def __init__(self):
        super().__init__()
        self.cookie_files = []  # 存储cookie文件列表
        self.cookie_path = ""   # 存储cookie文件路径
        # 线程对象，用于管理线程
        self.active_threads = []  # 存储活动线程
        # 加载状态标志，用于UI优化
        self.is_loading = False
        # 初始化存稿任务跟踪字典 - 用于支持多账号同时存稿
        self.active_cunggao_tasks = {}  # 格式: {account_id: (thread, worker)}

        # 快速启动相关变量
        self.fast_startup_enabled = True  # 启用快速启动模式
        self.background_loading_active = False  # 后台加载状态
        self.loading_progress_dialog = None  # 加载进度对话框
        self.background_loader_thread = None  # 后台加载线程

        self.init_ui()

        # 停止按钮已移至主窗口右上角

        # 公告功能已移至主界面顶部工具栏

        # 快速启动模式：延迟加载账号数据，先显示界面
        if self.fast_startup_enabled:
            # 立即显示空界面，然后在后台加载数据
            QTimer.singleShot(1000, self.start_background_loading)
        else:
            # 传统模式：直接加载保存的账号数据
            QTimer.singleShot(3000, self.load_saved_accounts_data)

        # 连接表格编辑信号（在所有方法定义完成后）
        self.setup_table_signals()

        # 连接清理信号 - 已禁用，避免Qt Fatal错误
        # self.cleanup_batch_signal.connect(self._force_cleanup_batch_objects)

    def setup_table_signals(self):
        """设置表格信号连接"""
        try:
            if hasattr(self, 'table') and self.table:
                # 连接单元格内容变更的信号
                self.table.cellChanged.connect(self.on_cell_changed)

                # 连接单元格双击信号，用于控制编辑权限
                self.table.cellDoubleClicked.connect(self.on_cell_double_clicked_for_edit)

                info("表格编辑信号连接成功")
            else:
                warning("表格尚未创建，无法连接信号")
        except Exception as e:
            error(f"连接表格信号时出错: {str(e)}")

    def init_ui(self):
        main_layout = QVBoxLayout(self)

        # 设置统一按钮样式 - 优化尺寸和间距
        self.button_style = """
            QPushButton {
                border-radius: 4px;
                padding: 5px 10px;
                font-weight: bold;
                border: 1px solid #000000;
                font-size: 12px;
                min-height: 24px;
                max-height: 28px;
                min-width: 60px;
                /* 移除文字阴影 */
            }
            QPushButton:hover {
                border: 1px solid #FFFF00;
            }
        """

        # 公告功能已移至主界面顶部工具栏
        # 头条自媒体标题已删除，搜索框上移

        # 搜索栏和功能按钮 - 优化布局，移除标题后上移
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)  # 减少边距
        search_layout.setSpacing(10)  # 统一间距

        # 搜索图标
        search_icon = QLabel("🔍")
        search_icon.setStyleSheet("""
            QLabel {
                font-size: 16px;
                color: #7f8c8d;
                margin-right: 5px;
            }
        """)
        search_layout.addWidget(search_icon)

        # 使用QTextEdit替代QLineEdit，支持多行输入，实现自动搜索
        self.search_input = QTextEdit()
        self.search_input.setPlaceholderText("实时搜索账号，支持多行批量搜索\n每行一个账号，回车分隔")
        self.search_input.setMaximumHeight(70)  # 稍微减少高度

        self.search_input.setStyleSheet("""
            QTextEdit {
                border: 2px solid #e1e8ed;
                border-radius: 8px;
                padding: 8px 12px;
                background-color: #f8f9fa;
                color: #2c3e50;
                font-size: 12px;
                font-family: 'Microsoft YaHei';
            }
            QTextEdit:focus {
                border: 2px solid #3498db;
                background-color: white;
            }
            QTextEdit::placeholder {
                color: #95a5a6;
                font-style: italic;
            }
        """)

        # 实现自动搜索 - 添加定时器支持
        from PyQt5.QtCore import QTimer
        self.search_timer = QTimer()
        self.search_timer.setSingleShot(True)
        self.search_timer.timeout.connect(self.search_accounts)

        # 连接文本变化事件，实现延迟自动搜索
        self.search_input.textChanged.connect(self.on_search_text_changed)

        # 直接添加搜索框到布局，移除搜索和清除按钮
        search_layout.addWidget(self.search_input)

        # 添加清除按钮（保留一个简洁的清除功能）
        clear_btn = QPushButton("✕")
        clear_btn.setStyleSheet("""
            QPushButton {
                background-color: transparent;
                color: #95a5a6;
                font-size: 12px;
                font-weight: bold;
                border: none;
                border-radius: 10px;
                padding: 3px 6px;
                max-width: 24px;
                max-height: 24px;
            }
            QPushButton:hover {
                background-color: #e9ecef;
                color: #6c757d;
            }
            QPushButton:pressed {
                background-color: #dee2e6;
            }
        """)
        clear_btn.setToolTip("清除搜索内容")
        clear_btn.clicked.connect(self.clear_search)
        search_layout.addWidget(clear_btn)

        search_layout.addStretch()

        # 加载账号按钮
        self.load_account_btn = QPushButton("加载账号")
        self.load_account_btn.setStyleSheet("""
            QPushButton {
                background-color: #27AE60;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border: 1px solid #000000;
                border-radius: 4px;
                padding: 5px 10px;
                min-height: 24px;
                max-height: 28px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #2ECC71;
                border: 1px solid #FFFF00;
            }
        """)
        self.load_account_btn.setIcon(self.style().standardIcon(QStyle.SP_DirOpenIcon))
        self.load_account_btn.setIconSize(QSize(14, 14))
        self.load_account_btn.clicked.connect(self.on_load_account_clicked)
        search_layout.addWidget(self.load_account_btn)

        # 创建右侧功能按钮 - 优化布局间距
        search_layout.addSpacing(8)  # 减小间距

        # 添加账号操作按钮标签
        account_label = QLabel("账号操作:")
        account_label.setStyleSheet("""
            QLabel {
                color: #666666;
                font-size: 12px;
                font-weight: bold;
                padding: 5px 10px;
            }
        """)
        search_layout.addWidget(account_label)

        self.add_btn = QPushButton("添加账号")
        self.add_btn.setStyleSheet("""
            QPushButton {
                background-color: #006400;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border: 1px solid #000000;
                border-radius: 4px;
                padding: 5px 10px;
                min-height: 24px;
                max-height: 28px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #008800;
                border: 1px solid #FFFF00;
            }
        """)
        self.add_btn.setIcon(self.style().standardIcon(QStyle.SP_FileDialogNewFolder))
        self.add_btn.setIconSize(QSize(14, 14))
        self.add_btn.clicked.connect(self.add_account)
        search_layout.addWidget(self.add_btn)



        search_layout.addSpacing(6)  # 优化间距

        self.export_btn = QPushButton("导出Excel")
        self.export_btn.setStyleSheet("""
            QPushButton {
                background-color: #8B4500;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border: 1px solid #000000;
                border-radius: 4px;
                padding: 5px 10px;
                min-height: 24px;
                max-height: 28px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #A25200;
                border: 1px solid #FFFF00;
            }
        """)
        self.export_btn.setIcon(self.style().standardIcon(QStyle.SP_FileDialogNewFolder))
        self.export_btn.setIconSize(QSize(14, 14))
        self.export_btn.clicked.connect(self.export_to_excel)
        search_layout.addWidget(self.export_btn)

        search_layout.addSpacing(6)

        self.batch_btn = QPushButton("批量行编")
        self.batch_btn.setStyleSheet("""
            QPushButton {
                background-color: #006666;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border: 1px solid #000000;
                border-radius: 4px;
                padding: 5px 10px;
                min-height: 24px;
                max-height: 28px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #007777;
                border: 1px solid #FFFF00;
            }
        """)
        self.batch_btn.setIcon(self.style().standardIcon(QStyle.SP_FileDialogListView))
        self.batch_btn.setIconSize(QSize(14, 14))
        self.batch_btn.clicked.connect(self.batch_cunggao)
        search_layout.addWidget(self.batch_btn)

        search_layout.addSpacing(6)

        # 已移除的功能按钮

        search_layout.addSpacing(6)

        # 添加批量采集按钮（已删除单独的数据采集按钮）
        self.batch_collect_btn = QPushButton("数据采集")
        self.batch_collect_btn.setStyleSheet("""
            QPushButton {
                background-color: #B22222;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border: 1px solid #000000;
                border-radius: 4px;
                padding: 5px 10px;
                min-height: 24px;
                max-height: 28px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #DC143C;
                border: 1px solid #FFFF00;
            }
        """)
        self.batch_collect_btn.setIcon(self.style().standardIcon(QStyle.SP_BrowserReload))
        self.batch_collect_btn.setIconSize(QSize(14, 14))
        self.batch_collect_btn.clicked.connect(self.batch_collect_account_data)
        search_layout.addWidget(self.batch_collect_btn)

        search_layout.addSpacing(6)

        # 添加强制刷新数据按钮
        self.force_refresh_btn = QPushButton("🔄 强制刷新")
        self.force_refresh_btn.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border: 1px solid #000000;
                border-radius: 4px;
                padding: 5px 10px;
                min-height: 24px;
                max-height: 28px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #c0392b;
                border: 1px solid #FFFF00;
            }
        """)
        self.force_refresh_btn.setIcon(self.style().standardIcon(QStyle.SP_BrowserReload))
        self.force_refresh_btn.setIconSize(QSize(14, 14))
        self.force_refresh_btn.clicked.connect(self.force_refresh_account_data)
        self.force_refresh_btn.setToolTip("强制重新加载所有账号数据，解决数据显示不全问题")
        search_layout.addWidget(self.force_refresh_btn)

        search_layout.addSpacing(6)

        # 添加九宫格布局配置按钮
        self.layout_config_btn = QPushButton("九宫格布局")
        self.layout_config_btn.setStyleSheet("""
            QPushButton {
                background-color: #8E44AD;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border: 1px solid #000000;
                border-radius: 4px;
                padding: 5px 10px;
                min-height: 24px;
                max-height: 28px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #9B59B6;
                border: 1px solid #FFFF00;
            }
        """)
        self.layout_config_btn.setIcon(self.style().standardIcon(QStyle.SP_ComputerIcon))
        self.layout_config_btn.setIconSize(QSize(14, 14))
        self.layout_config_btn.clicked.connect(self.show_layout_config_dialog)
        self.layout_config_btn.setToolTip("配置浏览器窗口九宫格自动排列布局")
        search_layout.addWidget(self.layout_config_btn)

        search_layout.addSpacing(6)

        # 添加头条爬虫按钮
        self.spider_btn = QPushButton("视频爬虫")
        self.spider_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF8C00;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border: 1px solid #000000;
                border-radius: 4px;
                padding: 5px 10px;
                min-height: 24px;
                max-height: 28px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #FFA500;
                border: 1px solid #FFFF00;
            }
        """)
        self.spider_btn.setIcon(self.style().standardIcon(QStyle.SP_FileDialogDetailedView))
        self.spider_btn.setIconSize(QSize(14, 14))
        self.spider_btn.clicked.connect(self.open_toutiao_spider)
        search_layout.addWidget(self.spider_btn)

        search_layout.addSpacing(6)

        # 添加缓存管理按钮
        self.cache_manage_btn = QPushButton("缓存管理")
        self.cache_manage_btn.setStyleSheet("""
            QPushButton {
                background-color: #9932CC;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border: 1px solid #000000;
                border-radius: 4px;
                padding: 5px 10px;
                min-height: 24px;
                max-height: 28px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #BA55D3;
                border: 1px solid #FFFF00;
            }
        """)
        self.cache_manage_btn.setIcon(self.style().standardIcon(QStyle.SP_ComputerIcon))
        self.cache_manage_btn.setIconSize(QSize(14, 14))
        self.cache_manage_btn.setToolTip("管理浏览器缓存，解决C盘空间不足问题")
        self.cache_manage_btn.clicked.connect(self.show_cache_manager)
        search_layout.addWidget(self.cache_manage_btn)

        # 清空数据按钮已移除

        main_layout.addLayout(search_layout)

        # 添加第二行按钮 - 系统功能按钮（原底部状态栏按钮）
        system_layout = QHBoxLayout()
        system_layout.setSpacing(6)  # 与第一行保持一致的间距

        # 添加系统功能按钮标签
        system_label = QLabel("系统功能:")
        system_label.setStyleSheet("""
            QLabel {
                color: #666666;
                font-size: 12px;
                font-weight: bold;
                padding: 5px 10px;
            }
        """)
        system_layout.addWidget(system_label)

        # 清理C盘缓存按钮
        self.clean_cache_btn = QPushButton("清理C盘缓存")
        self.clean_cache_btn.setStyleSheet("""
            QPushButton {
                background-color: #4a86e8;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border: 1px solid #000000;
                border-radius: 4px;
                padding: 5px 10px;
                min-height: 24px;
                max-height: 28px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #5a96f8;
                border: 1px solid #FFFF00;
            }
        """)
        self.clean_cache_btn.setIcon(self.style().standardIcon(QStyle.SP_TrashIcon))
        self.clean_cache_btn.setIconSize(QSize(14, 14))
        self.clean_cache_btn.setToolTip("清理浏览器缓存，释放C盘空间")
        self.clean_cache_btn.clicked.connect(self.clean_browser_cache)
        system_layout.addWidget(self.clean_cache_btn)

        system_layout.addSpacing(6)

        # 关闭浏览器进程按钮
        self.kill_browser_btn = QPushButton("关闭浏览器进程")
        self.kill_browser_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border: 1px solid #000000;
                border-radius: 4px;
                padding: 5px 10px;
                min-height: 24px;
                max-height: 28px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #f55346;
                border: 1px solid #FFFF00;
            }
        """)
        self.kill_browser_btn.setIcon(self.style().standardIcon(QStyle.SP_BrowserStop))
        self.kill_browser_btn.setIconSize(QSize(14, 14))
        self.kill_browser_btn.setToolTip("强制关闭所有浏览器进程")
        self.kill_browser_btn.clicked.connect(self.kill_browser_processes)
        system_layout.addWidget(self.kill_browser_btn)

        system_layout.addSpacing(6)

        # 账号养号按钮
        self.nurture_btn = QPushButton("账号养号")
        self.nurture_btn.setStyleSheet("""
            QPushButton {
                background-color: #4caf50;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border: 1px solid #000000;
                border-radius: 4px;
                padding: 5px 10px;
                min-height: 24px;
                max-height: 28px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #5cbf60;
                border: 1px solid #FFFF00;
            }
        """)
        self.nurture_btn.setIcon(self.style().standardIcon(QStyle.SP_ComputerIcon))
        self.nurture_btn.setIconSize(QSize(14, 14))
        self.nurture_btn.setToolTip("打开账号养号功能")
        self.nurture_btn.clicked.connect(self.open_account_nurture)
        system_layout.addWidget(self.nurture_btn)

        system_layout.addSpacing(6)

        # 视频处理按钮
        self.video_processor_btn = QPushButton("视频处理")
        self.video_processor_btn.setStyleSheet("""
            QPushButton {
                background-color: #9c27b0;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border: 1px solid #000000;
                border-radius: 4px;
                padding: 5px 10px;
                min-height: 24px;
                max-height: 28px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #ac37c0;
                border: 1px solid #FFFF00;
            }
        """)
        self.video_processor_btn.setIcon(self.style().standardIcon(QStyle.SP_MediaPlay))
        self.video_processor_btn.setIconSize(QSize(14, 14))
        self.video_processor_btn.setToolTip("打开视频处理工具")
        self.video_processor_btn.clicked.connect(self.open_video_processor)
        system_layout.addWidget(self.video_processor_btn)

        system_layout.addSpacing(6)

        # 代理设置按钮
        self.proxy_settings_btn = QPushButton("代理设置")
        self.proxy_settings_btn.setStyleSheet("""
            QPushButton {
                background-color: #4a86e8;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border: 1px solid #000000;
                border-radius: 4px;
                padding: 5px 10px;
                min-height: 24px;
                max-height: 28px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #5a96f8;
                border: 1px solid #FFFF00;
            }
        """)
        self.proxy_settings_btn.setIcon(self.style().standardIcon(QStyle.SP_ComputerIcon))
        self.proxy_settings_btn.setIconSize(QSize(14, 14))
        self.proxy_settings_btn.setToolTip("配置代理设置")
        self.proxy_settings_btn.clicked.connect(self.open_proxy_settings)
        system_layout.addWidget(self.proxy_settings_btn)

        system_layout.addSpacing(6)

        # 视频下载工具按钮
        self.video_downloader_btn = QPushButton("视频下载工具")
        self.video_downloader_btn.setStyleSheet("""
            QPushButton {
                background-color: #ff9800;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border: 1px solid #000000;
                border-radius: 4px;
                padding: 5px 10px;
                min-height: 24px;
                max-height: 28px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #ffa810;
                border: 1px solid #FFFF00;
            }
        """)
        self.video_downloader_btn.setIcon(self.style().standardIcon(QStyle.SP_ArrowDown))
        self.video_downloader_btn.setIconSize(QSize(14, 14))
        self.video_downloader_btn.setToolTip("打开视频下载工具")
        self.video_downloader_btn.clicked.connect(self.open_video_downloader)
        system_layout.addWidget(self.video_downloader_btn)

        system_layout.addSpacing(6)

        # 素材选择按钮
        self.material_select_btn = QPushButton("素材选择")
        self.material_select_btn.setStyleSheet("""
            QPushButton {
                background-color: #4caf50;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border: 1px solid #000000;
                border-radius: 4px;
                padding: 5px 10px;
                min-height: 24px;
                max-height: 28px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #5cbf60;
                border: 1px solid #FFFF00;
            }
        """)
        self.material_select_btn.setIcon(self.style().standardIcon(QStyle.SP_FileDialogDetailedView))
        self.material_select_btn.setIconSize(QSize(14, 14))
        self.material_select_btn.setToolTip("打开素材选择工具")
        self.material_select_btn.clicked.connect(self.open_material_selection)
        system_layout.addWidget(self.material_select_btn)

        system_layout.addSpacing(6)

        # 软件使用教程按钮
        self.tutorial_btn = QPushButton("📖 软件使用教程")
        self.tutorial_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196f3;
                color: #FFFFFF;
                font-size: 12px;
                font-weight: bold;
                border: 1px solid #000000;
                border-radius: 4px;
                padding: 5px 10px;
                min-height: 24px;
                max-height: 28px;
                min-width: 60px;
            }
            QPushButton:hover {
                background-color: #31a6f3;
                border: 1px solid #FFFF00;
            }
        """)
        self.tutorial_btn.setIcon(self.style().standardIcon(QStyle.SP_FileDialogInfoView))
        self.tutorial_btn.setIconSize(QSize(14, 14))
        self.tutorial_btn.setToolTip("打开软件使用教程，了解详细的操作指南")
        self.tutorial_btn.clicked.connect(self.open_software_tutorial)
        system_layout.addWidget(self.tutorial_btn)

        # 添加弹性空间，使按钮左对齐
        system_layout.addStretch()

        main_layout.addLayout(system_layout)

        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(19)  # 包含操作列，共19列
        self.table.setRowCount(0)  # 初始不设置行数，根据实际数据动态设置

        # 美化表格样式
        self.table.setStyleSheet("""
            QTableWidget {
                gridline-color: #DDD;
                selection-background-color: #E3F2FD;
                selection-color: #000000;
                alternate-background-color: #F9F9F9;
                border-radius: 5px;
                border: 1px solid #DDD;
                color: #000000;
                font-size: 12px;
            }
            QTableWidget::item {
                padding: 5px;
                border-bottom: 1px solid #EEE;
            }
            QHeaderView::section {
                background-color: #E0E0E0;
                padding: 5px;
                border: 1px solid #DDD;
                font-weight: bold;
                color: #000000;
                font-size: 12px;
            }

        """)
        self.table.setAlternatingRowColors(True)

        # 设置表格自适应
        self.table.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

        headers = [
            "昵称", "账号", "状态", "信用分", "草稿数", "昨日粉丝",
            "总粉丝", "累计播放", "昨日播放", "累计收益", "昨日收益",
            "七天收益", "待提现", "账号注册天数", "实名状态", "总提现金额",
            "最近提现日期", "最近提现金额", "操作"
        ]

        self.table.setHorizontalHeaderLabels(headers)

        # 设置表格列宽
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)  # 默认所有列自适应

        # 特定列的设置
        for i in [0, 1, 2, 3]:  # 昵称、账号、状态、信用分列固定宽度
            header.setSectionResizeMode(i, QHeaderView.ResizeToContents)

        # 操作列单独设置固定宽度
        header.setSectionResizeMode(18, QHeaderView.Fixed)
        self.table.setColumnWidth(18, 220)  # 设置操作列有足够宽度显示所有按钮

        # 设置行高
        self.table.verticalHeader().setDefaultSectionSize(35)  # 设置默认行高为30像素

        # 设置表格编辑属性
        self.table.setEditTriggers(QTableWidget.DoubleClicked)  # 允许双击编辑

        # 添加表格双击事件处理，支持延迟加载模式
        self.table.cellDoubleClicked.connect(self.on_table_cell_double_clicked)

        # 添加示例数据
        self.load_sample_data()

        main_layout.addWidget(self.table)

        # 让表格充满整个空间
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)

    def get_data_directory(self):
        """获取数据目录路径 - 统一的数据目录获取方法

        Returns:
            str: 数据目录路径
        """
        try:
            # 1. 首先检查是否已经设置了data_dir属性
            if hasattr(self, 'data_dir') and self.data_dir and os.path.exists(self.data_dir):
                return self.data_dir

            # 2. 尝试从主窗口的设置标签页获取数据目录
            main_window = self.window()
            if hasattr(main_window, 'setting_tab') and hasattr(main_window.setting_tab, 'data_path'):
                setting_data_path = main_window.setting_tab.data_path.text().strip()
                if setting_data_path and os.path.exists(setting_data_path):
                    # 更新data_dir属性
                    self.data_dir = setting_data_path
                    return setting_data_path

            # 3. 尝试从设置文件中读取
            try:
                settings_file = "settings.json"
                if os.path.exists(settings_file):
                    with open(settings_file, 'r', encoding='utf-8') as f:
                        settings = json.load(f)
                        if 'data_path' in settings and settings['data_path']:
                            data_path = settings['data_path']
                            if os.path.exists(data_path):
                                self.data_dir = data_path
                                return data_path
            except Exception as e:
                debug(f"从设置文件读取数据目录失败: {str(e)}")

            # 4. 使用默认路径
            default_data_dir = "D:/头条全自动/数据"
            try:
                os.makedirs(default_data_dir, exist_ok=True)
                self.data_dir = default_data_dir
                info(f"使用默认数据目录: {default_data_dir}")
                return default_data_dir
            except Exception as e:
                error(f"创建默认数据目录失败: {str(e)}")

                # 5. 最后的备选方案：使用当前目录下的data目录
                fallback_data_dir = os.path.join(os.getcwd(), "data")
                os.makedirs(fallback_data_dir, exist_ok=True)
                self.data_dir = fallback_data_dir
                warning(f"使用备选数据目录: {fallback_data_dir}")
                return fallback_data_dir

        except Exception as e:
            error(f"获取数据目录时出错: {str(e)}")
            # 返回当前目录下的data作为最后的备选
            fallback_data_dir = os.path.join(os.getcwd(), "data")
            os.makedirs(fallback_data_dir, exist_ok=True)
            return fallback_data_dir

    def start_background_loading(self):
        """启动后台加载账号数据"""
        try:
            if self.background_loading_active:
                info("后台加载已在进行中，跳过重复启动")
                return

            info("启动快速启动模式 - 后台加载账号数据")
            self.background_loading_active = True

            # 显示加载进度对话框
            self.show_loading_progress_dialog()

            # 创建后台加载线程
            from PyQt5.QtCore import QThread, pyqtSignal

            class BackgroundLoader(QThread):
                progress_updated = pyqtSignal(int, str)  # 进度, 状态文本
                loading_completed = pyqtSignal(bool, str)  # 成功, 消息
                batch_data_loaded = pyqtSignal(list)  # 批量数据加载完成

                def __init__(self, account_tab):
                    super().__init__()
                    self.account_tab = account_tab

                def run(self):
                    try:
                        # 第一步：检查是否有保存的账号数据
                        self.progress_updated.emit(10, "检查保存的账号数据...")

                        data_dir = self.account_tab.get_data_directory()
                        json_file_path = os.path.join(data_dir, "accounts_data.json")

                        if os.path.exists(json_file_path):
                            # 加载保存的数据
                            self.progress_updated.emit(30, "加载保存的账号数据...")
                            self.load_saved_data(json_file_path)
                        else:
                            # 检查是否有Cookie文件需要加载
                            self.progress_updated.emit(50, "检查Cookie文件...")
                            self.check_and_load_cookies()

                        self.progress_updated.emit(100, "加载完成")
                        self.loading_completed.emit(True, "账号数据加载完成")

                    except Exception as e:
                        error(f"后台加载账号数据时出错: {str(e)}")
                        self.loading_completed.emit(False, f"加载失败: {str(e)}")

                def load_saved_data(self, json_file_path):
                    """加载保存的数据"""
                    try:
                        import json
                        import time

                        # 检查文件大小
                        file_size = os.path.getsize(json_file_path)
                        file_size_mb = file_size / (1024 * 1024)

                        self.progress_updated.emit(40, f"读取数据文件 ({file_size_mb:.1f}MB)...")

                        # 分批加载大文件
                        if file_size_mb > 50:  # 超过50MB使用分批加载
                            self.load_large_file_in_batches(json_file_path)
                        else:
                            # 直接加载小文件
                            with open(json_file_path, 'r', encoding='utf-8') as f:
                                all_accounts_data = json.load(f)

                            self.progress_updated.emit(80, f"处理 {len(all_accounts_data)} 个账号数据...")

                            # 分批发送数据到UI线程
                            batch_size = 1000
                            account_items = list(all_accounts_data.items())

                            for i in range(0, len(account_items), batch_size):
                                batch = account_items[i:i + batch_size]
                                self.batch_data_loaded.emit(batch)
                                time.sleep(0.1)  # 短暂暂停，避免UI阻塞

                    except Exception as e:
                        error(f"加载保存数据时出错: {str(e)}")
                        raise

                def load_large_file_in_batches(self, json_file_path):
                    """分批加载大文件"""
                    try:
                        import json
                        import time

                        self.progress_updated.emit(50, "分批加载大型数据文件...")

                        with open(json_file_path, 'r', encoding='utf-8') as f:
                            all_accounts_data = json.load(f)

                        total_accounts = len(all_accounts_data)
                        batch_size = 500  # 减小批次大小
                        account_items = list(all_accounts_data.items())

                        for i in range(0, len(account_items), batch_size):
                            batch = account_items[i:i + batch_size]
                            progress = 50 + (i / total_accounts) * 40  # 50-90%的进度
                            self.progress_updated.emit(int(progress), f"加载账号数据 {i+1}-{min(i+batch_size, total_accounts)}/{total_accounts}")

                            self.batch_data_loaded.emit(batch)
                            time.sleep(0.05)  # 短暂暂停

                    except Exception as e:
                        error(f"分批加载大文件时出错: {str(e)}")
                        raise

                def check_and_load_cookies(self):
                    """检查并加载Cookie文件"""
                    try:
                        # 尝试从设置中获取Cookie路径
                        main_window = self.account_tab.window()
                        cookie_path = ""

                        if hasattr(main_window, 'setting_tab') and hasattr(main_window.setting_tab, 'cookie_path'):
                            cookie_path = main_window.setting_tab.cookie_path.text().strip()

                        if cookie_path and os.path.exists(cookie_path):
                            self.progress_updated.emit(70, f"加载Cookie文件: {cookie_path}")
                            # 触发Cookie文件加载（在主线程中执行）
                            QTimer.singleShot(100, lambda: self.account_tab.load_cookie_files(cookie_path, show_message=False))
                        else:
                            self.progress_updated.emit(90, "未找到Cookie文件路径")

                    except Exception as e:
                        error(f"检查Cookie文件时出错: {str(e)}")

            # 创建并启动后台加载线程
            self.background_loader_thread = BackgroundLoader(self)
            self.background_loader_thread.progress_updated.connect(self.update_loading_progress)
            self.background_loader_thread.loading_completed.connect(self.on_background_loading_completed)
            self.background_loader_thread.batch_data_loaded.connect(self.on_batch_data_loaded)
            self.background_loader_thread.start()

        except Exception as e:
            error(f"启动后台加载时出错: {str(e)}")
            self.background_loading_active = False

    def show_loading_progress_dialog(self):
        """显示加载进度对话框"""
        try:
            from PyQt5.QtWidgets import QProgressDialog
            from PyQt5.QtCore import Qt

            self.loading_progress_dialog = QProgressDialog("正在加载账号数据...", "取消", 0, 100, self)
            self.loading_progress_dialog.setWindowTitle("快速启动")
            self.loading_progress_dialog.setWindowModality(Qt.WindowModal)
            self.loading_progress_dialog.setMinimumDuration(0)
            self.loading_progress_dialog.setAutoClose(True)
            self.loading_progress_dialog.setAutoReset(True)

            # 设置对话框样式
            self.loading_progress_dialog.setStyleSheet("""
                QProgressDialog {
                    background-color: #f8f9fa;
                    border: 2px solid #007bff;
                    border-radius: 10px;
                    font-family: 'Microsoft YaHei';
                }
                QProgressBar {
                    border: 2px solid #dee2e6;
                    border-radius: 8px;
                    text-align: center;
                    background-color: #e9ecef;
                    color: #495057;
                    font-weight: bold;
                }
                QProgressBar::chunk {
                    background-color: #007bff;
                    border-radius: 6px;
                }
                QPushButton {
                    background-color: #6c757d;
                    color: white;
                    border: none;
                    border-radius: 6px;
                    padding: 8px 16px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #5a6268;
                }
            """)

            # 连接取消信号
            self.loading_progress_dialog.canceled.connect(self.cancel_background_loading)

            self.loading_progress_dialog.show()

        except Exception as e:
            error(f"显示加载进度对话框时出错: {str(e)}")

    def update_loading_progress(self, progress, status_text):
        """更新加载进度"""
        try:
            if self.loading_progress_dialog:
                self.loading_progress_dialog.setValue(progress)
                self.loading_progress_dialog.setLabelText(status_text)

                # 处理Qt事件，确保界面响应
                from PyQt5.QtWidgets import QApplication
                QApplication.processEvents()

        except Exception as e:
            error(f"更新加载进度时出错: {str(e)}")

    def on_background_loading_completed(self, success, message):
        """后台加载完成处理"""
        try:
            self.background_loading_active = False

            # 关闭进度对话框
            if self.loading_progress_dialog:
                self.loading_progress_dialog.close()
                self.loading_progress_dialog = None

            if success:
                info(f"后台加载完成: {message}")
                self.status_update_signal.emit(f"✅ {message}")
            else:
                error(f"后台加载失败: {message}")
                self.status_update_signal.emit(f"❌ {message}")

        except Exception as e:
            error(f"处理后台加载完成事件时出错: {str(e)}")



    def set_table_row_data(self, row, account_id, account_data):
        """设置表格行数据"""
        try:
            # 昵称列
            username = account_data.get('username', account_id)
            item = QTableWidgetItem(username)
            item.setTextAlignment(Qt.AlignCenter)
            self.table.setItem(row, 0, item)

            # 账号ID列
            item = QTableWidgetItem(account_id)
            item.setTextAlignment(Qt.AlignCenter)
            item.setFlags(item.flags() | Qt.ItemIsEditable)
            self.table.setItem(row, 1, item)

            # 状态列
            status = account_data.get('status', '正常')
            status_item = QTableWidgetItem(status)
            status_item.setTextAlignment(Qt.AlignCenter)
            status_item.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))

            # 根据状态设置颜色
            if status == "正常":
                status_item.setBackground(QColor(200, 255, 200))
                status_item.setForeground(QColor(0, 100, 0))
            elif status in ["采集中", "正在采集"]:
                status_item.setBackground(QColor(200, 200, 255))
                status_item.setForeground(QColor(0, 0, 150))
            elif status in ["采集失败", "登录失败"]:
                status_item.setBackground(QColor(255, 200, 200))
                status_item.setForeground(QColor(180, 0, 0))

            self.table.setItem(row, 2, status_item)

            # 设置其他数据字段
            field_to_column = {
                "credit_score": 3, "draft_count": 4, "yesterday_fans": 5,
                "total_fans": 6, "total_play_count": 7, "yesterday_play": 8,
                "total_income": 9, "yesterday_income": 10, "seven_days_total": 11,
                "withdrawable_amount": 12, "register_days": 13, "verification_status": 14,
                "total_withdraw": 15, "withdraw_date": 16, "recent_withdraw": 17
            }

            for field, column in field_to_column.items():
                if field in account_data and account_data[field]:
                    value = account_data[field]
                    item = QTableWidgetItem(str(value))
                    item.setTextAlignment(Qt.AlignCenter)

                    # 特殊处理实名状态
                    if field == "verification_status":
                        if value in ["已实名", "已认证"]:
                            item.setForeground(QColor(0, 128, 0))
                        else:
                            item.setForeground(QColor(255, 0, 0))

                    self.table.setItem(row, column, item)

            # 添加操作按钮
            self.add_operation_buttons(row)

        except Exception as e:
            error(f"设置表格行数据时出错: {str(e)}")

    def cancel_background_loading(self):
        """取消后台加载"""
        try:
            info("用户取消后台加载")
            self.background_loading_active = False

            if self.background_loader_thread and self.background_loader_thread.isRunning():
                self.background_loader_thread.terminate()
                self.background_loader_thread.wait(3000)  # 等待3秒

            if self.loading_progress_dialog:
                self.loading_progress_dialog.close()
                self.loading_progress_dialog = None

            self.status_update_signal.emit("⚠️ 账号数据加载已取消")

        except Exception as e:
            error(f"取消后台加载时出错: {str(e)}")

    def load_sample_data(self):
        """加载示例数据，仅在没有真实cookie文件时使用"""
        # 如果已有cookie文件，则不显示示例数据
        if self.cookie_files:
            return

        # 不检查是否已经在主界面加载过账号，允许重复加载
        # if hasattr(self, 'accounts_loaded') and self.accounts_loaded:
        #     return

        # 清空表格
        self.table.clearContents()
        self.table.setRowCount(0)  # 不显示任何示例数据，等待真实数据加载

        # 记录日志
        info("跳过示例数据加载，等待真实账号数据")

    # 此方法已被重复定义，移除以避免冲突

    def load_cookie_files_sync(self, path, show_message=False, delay_data_loading=False):
        """同步加载指定路径下的所有cookie文件 - 在软件启动初始化阶段使用

        Args:
            path: cookie文件所在的目录路径
            show_message: 是否显示消息弹窗，默认为False
            delay_data_loading: 是否延迟加载详细数据，仅加载账号基本信息，默认为False（完整加载）

        Returns:
            tuple: (是否成功, 账号列表, 错误信息)
        """
        # 导入垃圾回收模块
        import gc

        # 执行垃圾回收
        gc.collect(2)  # 使用完整的垃圾回收

        # 保存cookie路径
        self.cookie_path = path

        # 如果正在加载，不重复加载
        if hasattr(self, 'is_loading') and self.is_loading:
            warning("账号正在加载中，请等待当前加载完成")
            return False, [], "账号正在加载中，请等待当前加载完成"

        # 设置加载状态为True
        self.is_loading = True

        # 记录加载模式
        self.delay_data_loading = delay_data_loading  # 根据参数设置延迟加载模式
        info(f"账号加载模式: {'延迟加载' if delay_data_loading else '同步加载'}")

        # 检查路径是否存在
        if not path or not os.path.exists(path):
            error(f"Cookie路径不存在: {path}")

            # 尝试使用备用路径
            backup_paths = [
                "E:\\软件共享\\账号",
                "E:\\toutiaoyuanma1\\cookies",
                os.path.join(os.getcwd(), "cookies"),
                os.path.join(os.getcwd(), "accounts")
            ]

            found_backup = False
            for backup_path in backup_paths:
                if os.path.exists(backup_path):
                    info(f"使用备用Cookie路径: {backup_path}")
                    path = backup_path
                    found_backup = True
                    break

            # 如果所有备用路径都不存在，尝试创建默认路径
            if not found_backup:
                try:
                    default_path = os.path.join(os.getcwd(), "cookies")
                    os.makedirs(default_path, exist_ok=True)
                    info(f"创建默认Cookie目录: {default_path}")
                    path = default_path
                except Exception as e:
                    error(f"创建默认Cookie目录失败: {str(e)}")
                    if show_message:
                        QMessageBox.warning(self, "路径错误", f"路径 {path} 不存在，且无法创建默认目录")
                    self.is_loading = False
                    return False, [], f"路径 {path} 不存在，且无法创建默认目录"

        try:
            # 创建账号加载器（如果不存在）
            if not hasattr(self, 'account_loader') or self.account_loader is None:
                from app.utils.account_loader import AccountLoader
                self.account_loader = AccountLoader()
                info("已创建账号加载器")

            # 连接信号
            self.account_loader.signals.progress.connect(self.on_account_load_progress)
            self.account_loader.signals.completed.connect(self.on_account_load_completed)
            self.account_loader.signals.batch_data.connect(self.on_account_batch_data)

            # 数据目录功能已移除
            info("数据目录功能已移除")

            # 同步加载账号 - 不使用延迟加载模式
            info(f"开始同步加载Cookie文件，路径: {path}")

            # 检查路径下的文件数量
            try:
                all_json_files = [f for f in os.listdir(path) if f.endswith(".json")]
                all_txt_files = [f for f in os.listdir(path) if f.endswith(".txt")]
                info(f"路径 {path} 下有 {len(all_json_files)} 个JSON文件和 {len(all_txt_files)} 个TXT文件")
            except Exception as e:
                error(f"检查路径 {path} 下的文件时出错: {str(e)}")

            # 使用同步方式加载账号
            info("调用account_loader.load_accounts_sync方法前")
            success, accounts, error_msg = self.account_loader.load_accounts_sync(path)
            info(f"account_loader.load_accounts_sync方法返回: success={success}, accounts数量={len(accounts)}, error_msg={error_msg}")

            # 添加更多日志输出
            info(f"同步加载账号数据完成，结果: {success}, 账号数量: {len(accounts)}, 错误信息: {error_msg}")

            # 检查账号数据是否加载成功
            if success:
                info(f"账号数据加载成功，共 {len(accounts)} 个账号")

                # 检查表格中是否有数据
                pass
            else:
                error(f"账号数据加载失败: {error_msg}")

            # 手动处理加载完成
            self.on_account_load_completed(success, accounts, error_msg)

            # 返回加载结果
            return success, accounts, error_msg

        except Exception as e:
            error_msg = f"启动账号加载时出错：{str(e)}"
            error(error_msg)
            if show_message:
                QMessageBox.critical(self, "加载失败", error_msg)

            # 重置加载状态
            self.is_loading = False

            # 返回加载失败
            return False, [], error_msg

    def load_cookie_files(self, path, show_message=True, delay_data_loading=False):
        """加载指定路径下的所有txt格式cookie文件 - 默认使用完整加载模式

        Args:
            path: cookie文件所在的目录路径
            show_message: 是否显示消息弹窗，默认显示
            delay_data_loading: 是否延迟加载详细数据，仅加载账号基本信息，默认为False（完整加载）
        """
        # 导入垃圾回收模块
        import gc

        # 执行垃圾回收
        gc.collect(2)  # 使用完整的垃圾回收

        # 尝试导入内存管理器
        try:
            from app.utils.memory_manager import force_gc, get_memory_usage
            has_memory_manager = True
            debug("已导入高级内存管理器")
        except ImportError:
            has_memory_manager = False
            debug("高级内存管理器不可用，将使用基本内存管理")

        # 检查路径是否存在
        if not path or not os.path.exists(path):
            error(f"Cookie路径不存在: {path}")

            # 尝试使用备用路径
            backup_paths = [
                "E:\\软件共享\\账号",
                "E:\\toutiaoyuanma1\\cookies",
                os.path.join(os.getcwd(), "cookies"),
                os.path.join(os.getcwd(), "accounts")
            ]

            found_backup = False
            for backup_path in backup_paths:
                if os.path.exists(backup_path):
                    info(f"使用备用Cookie路径: {backup_path}")
                    path = backup_path
                    found_backup = True
                    break

            # 如果所有备用路径都不存在，尝试创建默认路径
            if not found_backup:
                try:
                    default_path = os.path.join(os.getcwd(), "cookies")
                    os.makedirs(default_path, exist_ok=True)
                    info(f"创建默认Cookie目录: {default_path}")
                    path = default_path
                except Exception as e:
                    error(f"创建默认Cookie目录失败: {str(e)}")
                    if show_message:
                        QMessageBox.warning(self, "路径错误", f"路径 {path} 不存在，且无法创建默认目录")
                    return

        # 保存cookie路径
        self.cookie_path = path

        # 增强的重复加载检查
        if hasattr(self, 'is_loading') and self.is_loading:
            warning("账号正在加载中，请等待当前加载完成")
            if show_message:
                QMessageBox.information(self, "提示", "账号正在加载中，请等待当前加载完成")
            return

        # 检查后台加载状态
        if hasattr(self, 'background_loading_active') and self.background_loading_active:
            warning("后台加载正在进行中，请等待完成")
            if show_message:
                QMessageBox.information(self, "提示", "后台加载正在进行中，请等待完成")
            return

        # 检查账号加载器状态
        if hasattr(self, 'account_loader') and self.account_loader and self.account_loader.is_loading:
            warning("账号加载器正在工作中，请等待完成")
            if show_message:
                QMessageBox.information(self, "提示", "账号加载器正在工作中，请等待完成")
            return

        # 停止之前的加载任务（如果存在）
        self._stop_previous_loading_tasks()

        # 设置加载状态为True
        self.is_loading = True

        # 记录延迟加载模式
        self.delay_data_loading = delay_data_loading
        info(f"账号加载模式: {'延迟加载' if delay_data_loading else '完整加载'}")

        try:
            # 导入异步加载器
            from app.utils.account_loader import AccountLoader

            # 安全创建进度对话框 - 防止重复创建
            if hasattr(self, 'progress_dialog') and self.progress_dialog:
                # 如果已存在进度对话框，先关闭它
                try:
                    self.progress_dialog.accept()
                    self.progress_dialog = None
                    info("已关闭之前的进度对话框")
                except Exception as e:
                    warning(f"关闭之前的进度对话框时出错: {str(e)}")

            # 创建新的进度对话框
            from app.widgets.progress_dialog import ProgressDialog
            self.progress_dialog = ProgressDialog("加载账号", self, cancelable=True)  # 允许取消加载
            self.progress_dialog.update_progress(0, "正在扫描账号文件...", "请稍候...")

            # 安全创建账号加载器 - 防止重复创建和信号连接
            if not hasattr(self, 'account_loader') or self.account_loader is None:
                self.account_loader = AccountLoader()
                # 保持账号加载器的默认批处理大小，支持大规模账号加载
                # self.account_loader.batch_size = 50  # 移除这个限制，使用默认的1000

                # 连接信号
                self.account_loader.signals.progress.connect(self.on_account_load_progress)
                self.account_loader.signals.completed.connect(self.on_account_load_completed)
                self.account_loader.signals.batch_data.connect(self.on_account_batch_data)
                info("已创建新的账号加载器并连接信号")
            else:
                # 如果加载器已存在，确保它处于空闲状态
                if hasattr(self.account_loader, 'stop_loading'):
                    self.account_loader.stop_loading()
                info("使用现有的账号加载器")

            # 连接取消信号 - 使用Qt.UniqueConnection防止重复连接
            try:
                self.progress_dialog.canceled.connect(self.on_account_load_canceled, Qt.UniqueConnection)
            except Exception as e:
                # 如果UniqueConnection不可用，使用普通连接
                self.progress_dialog.canceled.connect(self.on_account_load_canceled)

            # 显示进度对话框
            self.progress_dialog.show()

            # 减少Qt事件处理，避免事件循环混乱
            # QApplication.processEvents()  # 注释掉这行，减少事件处理

            # 清空当前列表
            self.cookie_files = []

            # 安全清空表格
            try:
                self.table.setUpdatesEnabled(False)  # 暂时禁用更新
                self.table.clearContents()
                self.table.setRowCount(0)
                self.table.setUpdatesEnabled(True)   # 重新启用更新
            except Exception as e:
                warning(f"清空表格时出错: {str(e)}")
                # 确保表格更新被重新启用
                try:
                    self.table.setUpdatesEnabled(True)
                except:
                    pass

            # 保存show_message参数，用于加载完成后显示消息
            self.show_load_message = show_message

            # 数据目录功能已移除
            info("数据目录功能已移除")

            # 开始加载账号 - 使用延迟加载模式
            info(f"开始异步加载Cookie文件，路径: {path}，延迟加载模式: {self.delay_data_loading}")
            self.account_loader.load_accounts(path, self.delay_data_loading)

        except Exception as e:
            error_msg = f"启动账号加载时出错：{str(e)}"
            error(error_msg)
            import traceback
            error(traceback.format_exc())

            if show_message:
                QMessageBox.critical(self, "加载失败", error_msg)

            # 安全关闭进度对话框
            try:
                if hasattr(self, 'progress_dialog') and self.progress_dialog:
                    self.progress_dialog.accept()
                    self.progress_dialog = None
            except Exception as cleanup_error:
                warning(f"清理进度对话框时出错: {str(cleanup_error)}")

            # 确保表格更新被重新启用
            try:
                self.table.setUpdatesEnabled(True)
            except:
                pass

            self.is_loading = False

            # 重新启用加载按钮
            self._re_enable_load_button()

    def on_account_load_progress(self, loaded, total, percent):
        """账号加载进度回调

        Args:
            loaded: 已加载数量
            total: 总数量
            percent: 进度百分比
        """
        if hasattr(self, 'progress_dialog') and self.progress_dialog:
            self.progress_dialog.update_progress(
                percent,
                f"正在加载账号文件... {percent}%",
                f"已加载: {loaded}/{total}"
            )

            # 处理Qt事件，确保UI响应
            QApplication.processEvents()

    def on_account_load_canceled(self):
        """账号加载取消回调"""
        info("用户取消了账号加载")

        # 停止账号加载器
        if hasattr(self, 'account_loader') and self.account_loader:
            # 如果加载器有stop方法，调用它
            if hasattr(self.account_loader, 'stop_loading'):
                self.account_loader.stop_loading()

        # 关闭进度对话框
        if hasattr(self, 'progress_dialog') and self.progress_dialog:
            self.progress_dialog.accept()
            self.progress_dialog = None

        # 重置加载状态
        self.is_loading = False

    def on_account_batch_data(self, batch_accounts):
        """批量账号数据回调 - 优化版本，减少UI更新频率

        Args:
            batch_accounts: 批量账号数据
        """
        if not batch_accounts:
            warning("⚠️ 收到空的批量账号数据")
            return

        # 记录开始时间
        import time
        start_time = time.time()

        # 获取当前行数
        current_row_count = self.table.rowCount()

        info(f"🔄 UI收到批量数据：{len(batch_accounts)}个账号，当前表格行数：{current_row_count}")

        # 增加表格行数
        new_row_count = current_row_count + len(batch_accounts)
        self.table.setRowCount(new_row_count)
        info(f"📊 表格行数已更新：{current_row_count} → {new_row_count}")

        # 安全暂时禁用表格更新，提高性能
        try:
            self.table.setUpdatesEnabled(False)
        except Exception as e:
            warning(f"禁用表格更新时出错: {str(e)}")

        # 添加账号到表格
        try:
            for i, account in enumerate(batch_accounts):
                row = current_row_count + i

                # 获取账号信息
                account_id = account.get('account_id', '')
                full_path = account.get('file_path', '')

                # 添加到cookie文件列表
                if full_path not in self.cookie_files:
                    self.cookie_files.append(full_path)

                # 昵称列设置为账号ID
                item = QTableWidgetItem(account_id)
                item.setTextAlignment(Qt.AlignCenter)
                self.table.setItem(row, 0, item)

                # 在第二列(账号列)显示不带后缀的用户名
                item = QTableWidgetItem(account_id)
                item.setTextAlignment(Qt.AlignCenter)
                # 设置账号列为可编辑
                item.setFlags(item.flags() | Qt.ItemIsEditable)
                self.table.setItem(row, 1, item)

                # 设置状态为"待验证"
                status_item = QTableWidgetItem("待验证")
                status_item.setTextAlignment(Qt.AlignCenter)
                status_item.setBackground(QColor(255, 230, 200)) # 浅橙色背景
                status_item.setForeground(QColor(153, 76, 0))    # 深橙色文字
                status_item.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
                self.table.setItem(row, 2, status_item)

                # 添加操作按钮 - 延迟添加按钮，减少UI更新频率
                if i == len(batch_accounts) - 1 or i % 10 == 0:
                    try:
                        self.add_operation_buttons(row)
                    except Exception as e:
                        warning(f"添加操作按钮时出错 (行{row}): {str(e)}")

        except Exception as e:
            error(f"批量添加账号到表格时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())

        # 安全重新启用表格更新
        try:
            self.table.setUpdatesEnabled(True)
        except Exception as e:
            warning(f"重新启用表格更新时出错: {str(e)}")

        # 安全更新UI
        try:
            self.table.viewport().update()
        except Exception as e:
            warning(f"更新表格视图时出错: {str(e)}")

        # 记录处理时间
        elapsed_time = time.time() - start_time
        if len(batch_accounts) > 20:  # 只记录大批量处理的时间
            info(f"批量处理 {len(batch_accounts)} 个账号数据，耗时: {elapsed_time:.2f}秒")

        # 减少Qt事件处理，避免事件循环混乱
        # 只在大批量且处理时间较长时才处理事件
        if len(batch_accounts) > 100 and elapsed_time > 1.0:
            try:
                QApplication.processEvents()
            except Exception as e:
                warning(f"处理Qt事件时出错: {str(e)}")

    def on_account_load_completed(self, success_flag, accounts, error_msg):
        """账号加载完成回调 - 支持延迟加载模式

        Args:
            success_flag: 是否成功
            accounts: 账号列表
            error_msg: 错误信息
        """
        try:
            # 导入垃圾回收模块
            import gc

            # 记录内存使用情况
            try:
                import psutil
                process = psutil.Process()
                memory_info = process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                info(f"账号加载完成回调开始时内存使用: {memory_mb:.2f} MB")
            except ImportError:
                info("无法导入psutil模块，跳过内存使用统计")
            except Exception as e:
                warning(f"获取内存使用信息时出错: {str(e)}")

            if success_flag:
                # 创建账号列表的副本，避免引用原始列表
                try:
                    # 创建浅拷贝，避免引用原始对象
                    self.accounts_list = accounts.copy()
                    info(f"已创建账号列表的浅拷贝，大小: {len(self.accounts_list)}")
                except Exception as e:
                    # 如果拷贝失败，直接使用原始列表
                    warning(f"创建账号列表拷贝时出错: {str(e)}，使用原始列表")
                    self.accounts_list = accounts

                # 发送cookie数量变化信号
                self.cookie_count_changed.emit(len(accounts))

                # 根据账号数量决定加载策略
                account_count = len(accounts)
                info(f"成功加载 {account_count} 个账号文件，延迟加载模式: {self.delay_data_loading}")

                # 安全关闭当前进度对话框
                try:
                    if hasattr(self, 'progress_dialog') and self.progress_dialog:
                        self.progress_dialog.accept()
                        self.progress_dialog = None
                        info("已关闭进度对话框")
                except Exception as e:
                    warning(f"关闭进度对话框时出错: {str(e)}")

                # 显示通知
                if self.delay_data_loading:
                    success_msg = f"成功加载 {account_count} 个账号文件（延迟加载模式）"
                else:
                    success_msg = f"成功加载 {account_count} 个账号文件，正在加载数据..."

                # 显示通知（仅当show_message为True时）
                try:
                    if hasattr(self, 'show_load_message') and self.show_load_message:
                        QMessageBox.information(self, "加载成功", success_msg)
                except Exception as e:
                    warning(f"显示加载成功消息时出错: {str(e)}")

                # 强制执行垃圾回收，确保内存干净
                gc.collect(2)

                # 记录账号列表信息
                info(f"账号列表大小: {len(self.accounts_list)}")
                if self.accounts_list:
                    info(f"第一个账号信息: {self.accounts_list[0]}")

                # 根据延迟加载模式决定是否立即加载数据
                if self.delay_data_loading:
                    # 延迟加载模式下，只更新表格显示账号基本信息
                    info("使用延迟加载模式，准备更新表格基本信息")
                    self.update_account_table_basic(self.accounts_list)
                    info("使用延迟加载模式，已更新表格基本信息")

                    # 检查表格中是否有数据
                    row_count = self.table.rowCount()
                    info(f"延迟加载模式下，表格行数: {row_count}")

                    # 执行垃圾回收
                    gc.collect(2)
                else:
                    # 完整加载模式下，先更新表格基本信息，然后使用QTimer延迟加载详细数据
                    info("使用完整加载模式，准备更新表格基本信息")
                    self.update_account_table_basic(self.accounts_list)
                    info("使用完整加载模式，已更新表格基本信息")

                    # 检查表格中是否有数据
                    row_count = self.table.rowCount()
                    info(f"完整加载模式下，表格行数: {row_count}")

                    # 使用QTimer延迟加载数据，避免UI卡顿
                    # 增加延迟时间，给系统更多时间处理
                    QTimer.singleShot(1000, self.load_account_data)
                    info("使用完整加载模式，将在1秒后加载所有账号数据")

                # 强制刷新表格，确保UI更新
                self.table.viewport().update()
                QApplication.processEvents()  # 处理挂起的事件，确保UI响应

                # 重置加载状态，允许用户进行其他操作
                self.is_loading = False

                # 重新启用加载按钮
                self._re_enable_load_button()

                # 不设置账号已加载标志，允许重复加载
                # self.accounts_loaded = True
                info("不设置账号已加载标志，允许重复加载")

                # 更新七天总收益统计
                # 在完整加载模式下，等待数据加载完成后再更新收益统计
                if not self.delay_data_loading:
                    # 延迟2秒后更新收益统计，确保数据已加载完成
                    QTimer.singleShot(2000, self.update_total_earnings_and_signal)
                    info("已安排在数据加载完成后更新七天总收益统计并发送表格刷新完成信号")
                else:
                    # 在延迟加载模式下，直接更新收益统计
                    self.update_total_earnings_and_signal()

                # 记录内存使用情况
                # 移除内存使用日志
                pass

                return
            else:
                error(f"加载账号失败: {error_msg}")
                if hasattr(self, 'show_load_message') and self.show_load_message:
                    QMessageBox.critical(self, "加载失败", error_msg)
        except Exception as e:
            error(f"处理账号加载完成回调时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())  # 添加详细的错误堆栈
        finally:
            # 关闭进度对话框
            if hasattr(self, 'progress_dialog') and self.progress_dialog:
                self.progress_dialog.accept()
                self.progress_dialog = None

            # 重置加载状态
            self.is_loading = False

            # 重新启用加载按钮
            self._re_enable_load_button()

            # 执行强制垃圾回收
            gc.collect(2)

            # 记录内存使用情况
            # 移除内存使用日志
            pass



    def load_account_data(self):
        """加载账号数据 - 优化版本，使用多线程和更大的批处理大小，提高性能"""
        try:
            # 记录内存使用情况
            try:
                import psutil
                process = psutil.Process()
                memory_info = process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                info(f"加载账号数据开始时内存使用: {memory_mb:.2f} MB")
            except ImportError:
                info("无法导入psutil模块，跳过内存使用统计")
            except Exception as e:
                warning(f"获取内存使用信息时出错: {str(e)}")

            # 如果已经设置了账号已加载标志，移除它
            if hasattr(self, 'accounts_loaded'):
                delattr(self, 'accounts_loaded')
                info("移除账号已加载标志，允许重复加载")

            # 导入垃圾回收模块
            import gc
            import time

            # 执行一次完整垃圾回收，确保开始时内存干净
            gc.collect(2)

            # 记录开始加载数据的时间
            self.data_load_start_time = time.time()
            info(f"开始加载账号数据，时间: {time.strftime('%H:%M:%S')}")

            # 获取所有账号ID - 使用更高效的方式
            account_ids = []
            account_id_to_row = {}  # 账号ID到行索引的映射

            # 获取表格行数一次，避免重复调用
            row_count = self.table.rowCount()

            # 如果表格为空，直接返回
            if row_count == 0:
                info("表格为空，跳过数据加载")
                return

            # 使用动态列表
            account_ids = []
            account_id_to_row = {}
            valid_count = 0

            # 分批处理表格行，避免一次性处理过多数据
            batch_size = 100  # 每批处理100行，提高处理效率
            for batch_start in range(0, row_count, batch_size):
                batch_end = min(batch_start + batch_size, row_count)

                for row in range(batch_start, batch_end):
                    account_item = self.table.item(row, 1)  # 账号列
                    if account_item:
                        account_id = account_item.text()
                        account_ids.append(account_id)  # 使用append而不是索引赋值
                        account_id_to_row[account_id] = row
                        valid_count += 1

                # 每批处理完成后处理事件，但不暂停
                QApplication.processEvents()

            # 调整列表大小为实际有效数量
            if valid_count < row_count:
                account_ids = account_ids[:valid_count]

            # 如果没有账号，直接返回
            if not account_ids:
                info("没有找到账号，跳过数据加载")
                return

            # 记录账号数量
            info(f"找到 {len(account_ids)} 个账号，准备加载数据")

            # 再次执行垃圾回收，确保内存干净
            gc.collect(2)

            # 记录内存使用情况
            try:
                import psutil
                process = psutil.Process()
                memory_info = process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                info(f"准备创建数据加载线程前内存使用: {memory_mb:.2f} MB")
            except Exception:
                pass

            # 创建数据加载线程
            from PyQt5.QtCore import QThread, pyqtSignal

            class DataLoadThread(QThread):
                progress = pyqtSignal(int, int)  # 进度信号(当前索引, 总数)
                batch_data = pyqtSignal(list)  # 批量数据信号(数据列表)
                completed = pyqtSignal()  # 完成信号
                error_signal = pyqtSignal(str)  # 错误信号

                def __init__(self, account_ids, data_path, batch_size=100):  # 使用更大的批处理大小，减少UI更新频率
                    super().__init__()
                    self.account_ids = account_ids
                    self.data_path = data_path
                    self.batch_size = batch_size
                    self.running = True
                    self.start_time = time.time()

                    # 添加UI更新间隔控制（毫秒） - 修复UI更新频率问题
                    self.ui_update_interval = 1000  # 修复为1000毫秒更新一次UI，确保及时显示进度
                    self.last_ui_update_time = 0

                    # 添加内存监控阈值（MB）
                    self.memory_threshold_mb = 2000  # 大幅增加内存使用阈值，减少垃圾回收频率

                    # 设置线程池大小
                    self.max_workers = 6  # 增加线程数，提高并行处理效率

                    # 添加错误计数和重试机制
                    self.error_count = 0
                    self.max_errors = 10  # 最大错误次数
                    self.retry_delay = 1  # 重试延迟(秒)

                    info(f"创建数据加载线程，账号数量: {len(account_ids)}, 批处理大小: {batch_size}")

                    # 记录内存使用情况
                    try:
                        import psutil
                        process = psutil.Process()
                        memory_info = process.memory_info()
                        memory_mb = memory_info.rss / 1024 / 1024
                        info(f"数据加载线程创建时内存使用: {memory_mb:.2f} MB")
                    except Exception:
                        pass

                def stop(self):
                    """停止线程"""
                    self.running = False
                    info("数据加载线程收到停止信号")

                def run(self):
                    try:
                        # 导入垃圾回收模块
                        import gc

                        # 执行一次完整垃圾回收，确保开始时内存干净
                        gc.collect(2)

                        # 记录内存使用情况
                        try:
                            import psutil
                            process = psutil.Process()
                            memory_info = process.memory_info()
                            memory_mb = memory_info.rss / 1024 / 1024
                            info(f"数据加载线程开始运行时内存使用: {memory_mb:.2f} MB")
                        except Exception:
                            pass

                        info(f"数据加载线程开始运行，时间: {time.strftime('%H:%M:%S')}")
                        total = len(self.account_ids)
                        batch_data = []
                        loaded_count = 0
                        error_count = 0

                        # 设置批处理大小，提高处理效率
                        effective_batch_size = self.batch_size  # 使用完整的批处理大小
                        info(f"使用有效批处理大小: {effective_batch_size}")

                        for i, account_id in enumerate(self.account_ids):
                            # 检查是否应该停止
                            if not self.running:
                                info("数据加载线程被中止")
                                break

                            try:
                                # 数据目录功能已移除，创建一个空的数据对象
                                account_data = {"account_id": account_id}

                                # 添加到批量数据
                                batch_data.append((account_id, account_data))
                                loaded_count += 1
                                debug(f"为账号 {account_id} 创建了空数据对象")

                                # 如果达到批处理大小或是最后一个账号，发送批量数据
                                if len(batch_data) >= effective_batch_size or i == total - 1:
                                    if batch_data:  # 确保有数据才发送
                                        try:
                                            # 创建批量数据的副本，避免引用问题
                                            batch_copy = batch_data.copy()
                                            self.batch_data.emit(batch_copy)
                                            debug(f"发送批量数据，大小: {len(batch_data)}")

                                            # 清空批量数据和副本
                                            batch_data = []
                                            del batch_copy

                                            # 极短暂暂停，让出CPU，防止内存溢出
                                            time.sleep(0.05)  # 大幅减少暂停时间，提高加载速度

                                            # 使用时间间隔控制UI更新频率
                                            current_time = int(time.time() * 1000)
                                            if (current_time - self.last_ui_update_time >= self.ui_update_interval):
                                                # 处理Qt事件，确保UI响应
                                                QApplication.processEvents()
                                                self.last_ui_update_time = current_time

                                            # 每批次处理后执行垃圾回收，但大幅减少频率
                                            if i % 500 == 0:  # 每500个账号才执行一次完整垃圾回收
                                                gc.collect(2)
                                            elif i % 200 == 0:  # 每200个账号执行简单的垃圾回收
                                                gc.collect(0)
                                            # 其他情况不执行垃圾回收，提高加载速度
                                        except Exception as e:
                                            warning(f"处理批量数据时出错: {str(e)}")
                                            # 确保批量数据被清空
                                            batch_data = []
                                            # 强制执行垃圾回收
                                            gc.collect(0)  # 使用简单的垃圾回收，减少暂停时间

                                # 发送进度信号 - 使用时间间隔控制，而不是固定的计数间隔
                                current_time = int(time.time() * 1000)  # 当前时间（毫秒）
                                if (i == total - 1) or (current_time - self.last_ui_update_time >= self.ui_update_interval):
                                    progress_percent = int((i + 1) / total * 100)
                                    self.progress.emit(i + 1, total)
                                    debug(f"数据加载进度: {progress_percent}% ({i+1}/{total})")

                                    # 更新上次UI更新时间
                                    self.last_ui_update_time = current_time

                                    # 极短暂暂停，让出CPU
                                    time.sleep(0.01)  # 极大幅度减少暂停时间，提高加载速度

                                    # 处理Qt事件，但极大幅度减少处理频率
                                    if i % 1000 == 0 or i == total - 1:  # 每1000个账号才处理一次事件，极大幅度减少UI压力
                                        QApplication.processEvents()

                            except Exception as e:
                                error(f"加载账号 {account_id} 数据时出错: {str(e)}")
                                import traceback
                                error(traceback.format_exc())  # 添加详细的错误堆栈
                                error_count += 1
                                continue

                        # 记录加载完成的统计信息
                        elapsed_time = time.time() - self.start_time
                        info(f"数据加载完成，耗时: {elapsed_time:.2f}秒，成功: {loaded_count}，失败: {error_count}")

                        # 记录内存使用情况
                        try:
                            import psutil
                            process = psutil.Process()
                            memory_info = process.memory_info()
                            memory_mb = memory_info.rss / 1024 / 1024
                            info(f"数据加载完成时内存使用: {memory_mb:.2f} MB")
                        except Exception:
                            pass

                        # 清理资源
                        del batch_data

                        # 执行最终完整垃圾回收
                        gc.collect(2)

                        # 再次记录内存使用情况
                        try:
                            import psutil
                            process = psutil.Process()
                            memory_info = process.memory_info()
                            memory_mb = memory_info.rss / 1024 / 1024
                            info(f"垃圾回收后内存使用: {memory_mb:.2f} MB")
                        except Exception:
                            pass

                        # 发送完成信号
                        self.completed.emit()

                        info("数据加载线程已完成，所有资源已清理")
                    except Exception as e:
                        # 捕获线程运行过程中的所有异常
                        error(f"数据加载线程运行时发生异常: {str(e)}")
                        import traceback
                        error(traceback.format_exc())  # 添加详细的错误堆栈
                        self.error_signal.emit(f"数据加载失败: {str(e)}")

                        # 记录内存使用情况
                        try:
                            import psutil
                            process = psutil.Process()
                            memory_info = process.memory_info()
                            memory_mb = memory_info.rss / 1024 / 1024
                            info(f"数据加载异常时内存使用: {memory_mb:.2f} MB")
                        except Exception:
                            pass

                        # 清理资源
                        try:
                            del batch_data
                        except Exception:
                            pass

                        # 执行强制垃圾回收
                        try:
                            gc.collect(2)
                            info("已执行强制垃圾回收")
                        except Exception as gc_e:
                            warning(f"执行垃圾回收时出错: {str(gc_e)}")

                        # 确保发送完成信号，避免界面卡死
                        self.completed.emit()

                        info("数据加载线程异常结束，已尝试清理资源")

            # 创建数据目录
            # 首先检查是否已经设置了data_dir属性
            if hasattr(self, 'data_dir') and self.data_dir and os.path.exists(self.data_dir):
                data_dir = self.data_dir
                info(f"使用已设置的数据目录: {data_dir}")
            else:
                # 尝试从主窗口的设置标签页获取数据目录
                main_window = self.window()
                if hasattr(main_window, 'setting_tab') and hasattr(main_window.setting_tab, 'data_path'):
                    setting_data_path = main_window.setting_tab.data_path.text()
                    if setting_data_path and os.path.exists(setting_data_path):
                        data_dir = setting_data_path
                        info(f"使用设置中的数据目录: {data_dir}")
                    else:
                        # 使用默认路径
                        data_dir = os.path.join(os.path.dirname(self.cookie_path), "data")
                        info(f"设置中的数据目录不存在，使用默认数据目录: {data_dir}")
                else:
                    # 使用默认路径
                    data_dir = os.path.join(os.path.dirname(self.cookie_path), "data")
                    info(f"无法获取设置中的数据目录，使用默认数据目录: {data_dir}")

            # 确保数据目录存在
            if not os.path.exists(data_dir):
                try:
                    os.makedirs(data_dir, exist_ok=True)
                    info(f"已创建数据目录: {data_dir}")
                except Exception as e:
                    error(f"创建数据目录失败: {str(e)}")
                    # 尝试使用当前目录下的data目录作为备选
                    data_dir = os.path.join(os.getcwd(), "data")
                    if not os.path.exists(data_dir):
                        os.makedirs(data_dir, exist_ok=True)
                    info(f"使用备选数据目录: {data_dir}")

            # 保存账号ID到行索引的映射，供回调使用
            self.account_id_to_row = account_id_to_row

            # 创建进度对话框
            from app.widgets.progress_dialog import ProgressDialog
            self.data_progress_dialog = ProgressDialog("加载账号数据", self, cancelable=True)  # 允许取消
            self.data_progress_dialog.update_progress(0, "正在加载账号数据...", f"共 {len(account_ids)} 个账号")

            # 连接取消信号
            self.data_progress_dialog.canceled.connect(self.on_data_load_canceled)

            # 显示进度对话框
            self.data_progress_dialog.show()

            # 处理Qt事件，确保对话框显示
            QApplication.processEvents()

            # 创建并启动数据加载线程
            self.data_load_thread = DataLoadThread(account_ids, data_dir)
            self.data_load_thread.progress.connect(self.on_data_load_progress)
            self.data_load_thread.batch_data.connect(self.on_batch_data_loaded)
            self.data_load_thread.completed.connect(self.on_data_load_completed)
            self.data_load_thread.start()

        except Exception as e:
            error(f"启动数据加载线程时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())  # 添加详细的错误堆栈

            # 关闭进度对话框（如果存在）
            if hasattr(self, 'data_progress_dialog') and self.data_progress_dialog:
                self.data_progress_dialog.accept()
                self.data_progress_dialog = None

            # 重置加载状态
            self.is_loading = False

    def on_data_load_canceled(self):
        """数据加载取消回调"""
        if hasattr(self, 'data_load_thread') and self.data_load_thread:
            info("用户取消了数据加载")
            # 停止线程
            if hasattr(self.data_load_thread, 'stop'):
                self.data_load_thread.stop()

            # 等待线程结束
            if self.data_load_thread.isRunning():
                self.data_load_thread.wait(1000)  # 最多等待1秒

            # 如果线程仍在运行，强制终止
            if self.data_load_thread.isRunning():
                self.data_load_thread.terminate()
                warning("强制终止数据加载线程")

        # 关闭进度对话框
        if hasattr(self, 'data_progress_dialog') and self.data_progress_dialog:
            self.data_progress_dialog.accept()
            self.data_progress_dialog = None

    def on_data_load_progress(self, current, total):
        """数据加载进度回调"""
        # 更新进度对话框
        if hasattr(self, 'data_progress_dialog') and self.data_progress_dialog:
            progress_percent = int(current / total * 100)
            self.data_progress_dialog.update_progress(
                progress_percent,
                f"正在加载账号数据... {progress_percent}%",
                f"已加载: {current}/{total}"
            )

            # 处理Qt事件，确保UI响应
            QApplication.processEvents()

    def update_account_table_basic(self, accounts):
        """更新账号表格基本信息 - 延迟加载模式下使用

        Args:
            accounts: 账号列表
        """
        try:
            # 导入垃圾回收模块
            import gc

            # 尝试导入内存管理器
            try:
                from app.utils.memory_manager import force_gc, get_memory_usage
                has_memory_manager = True
                debug("已导入高级内存管理器")
            except ImportError:
                has_memory_manager = False
                debug("高级内存管理器不可用，将使用基本内存管理")

            # 记录开始时间和内存使用情况
            start_time = time.time()
            if has_memory_manager:
                memory_info = get_memory_usage()
                if memory_info:
                    memory_mb, available_mb, total_mb = memory_info
                    info(f"更新表格开始时内存使用: {memory_mb:.2f}MB, 可用: {available_mb:.2f}MB/{total_mb:.2f}MB")

            # 执行垃圾回收，确保开始时内存干净
            if has_memory_manager:
                force_gc()
            else:
                gc.collect(2)

            # 暂时禁用表格更新，提高性能
            self.table.setUpdatesEnabled(False)

            # 暂时禁用表格排序，提高性能
            self.table.setSortingEnabled(False)

            # 清空表格
            self.table.clearContents()

            # 设置表格行数
            total_accounts = len(accounts)
            info(f"准备设置表格行数: {total_accounts}")
            self.table.setRowCount(total_accounts)
            info(f"已设置表格行数: {total_accounts}，当前表格行数: {self.table.rowCount()}")

            # 更新cookie文件列表
            self.cookie_files = []

            # 批量处理账号，减少UI更新频率
            batch_size = 50  # 每批处理50个账号
            for batch_start in range(0, total_accounts, batch_size):
                batch_end = min(batch_start + batch_size, total_accounts)
                debug(f"处理账号批次: {batch_start+1}-{batch_end}/{total_accounts}")

                # 更新表格
                for row in range(batch_start, batch_end):
                    try:
                        account = accounts[row]

                        # 获取账号ID
                        account_id = account.get('account_id', '')
                        file_path = account.get('file_path', '')

                        # 添加到cookie文件列表
                        if file_path and file_path not in self.cookie_files:
                            self.cookie_files.append(file_path)

                        # 创建表格项
                        # 昵称列(0)
                        item = QTableWidgetItem(account_id)  # 使用账号ID作为昵称
                        item.setTextAlignment(Qt.AlignCenter)
                        self.table.setItem(row, 0, item)

                        # 账号列(1)
                        item = QTableWidgetItem(account_id)
                        item.setTextAlignment(Qt.AlignCenter)
                        # 设置账号列为可编辑
                        item.setFlags(item.flags() | Qt.ItemIsEditable)
                        self.table.setItem(row, 1, item)

                        # 状态列(2)
                        item = QTableWidgetItem("待验证")
                        item.setTextAlignment(Qt.AlignCenter)
                        item.setBackground(QColor(255, 230, 200))  # 浅橙色背景
                        item.setForeground(QColor(153, 76, 0))     # 深橙色文字
                        item.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
                        self.table.setItem(row, 2, item)

                        # 其他列设置为空或默认值
                        for col in range(3, 19):  # 更新范围以包含新添加的列
                            item = QTableWidgetItem("--")
                            item.setTextAlignment(Qt.AlignCenter)
                            self.table.setItem(row, col, item)

                        # 添加操作按钮 - 减少频率，只在每批次的最后一个或每10个账号添加一次
                        if (row == batch_end - 1) or (row % 10 == 0):
                            self.add_operation_buttons(row)

                    except Exception as e:
                        error(f"处理账号 {row} 时出错: {str(e)}")
                        import traceback
                        error(traceback.format_exc())
                        continue

                # 每批次处理完成后，添加剩余的操作按钮
                for row in range(batch_start, batch_end):
                    if row % 10 != 0 and row != batch_end - 1:
                        try:
                            self.add_operation_buttons(row)
                        except Exception as e:
                            warning(f"为账号 {row} 添加操作按钮时出错: {str(e)}")

                # 每批次处理完成后，处理Qt事件并短暂让出CPU
                QApplication.processEvents()
                time.sleep(0.05)  # 增加暂停时间，减少CPU占用

                # 检查内存使用情况
                if has_memory_manager:
                    memory_info = get_memory_usage()
                    if memory_info:
                        memory_mb, available_mb, total_mb = memory_info
                        if memory_mb > 1500:  # 如果内存使用超过1.5GB，执行垃圾回收
                            debug(f"内存使用较高 ({memory_mb:.2f}MB)，执行垃圾回收")
                            force_gc()
                else:
                    # 每处理完一批账号，执行简单的垃圾回收
                    gc.collect(0)

            # 重新启用表格更新和排序
            self.table.setSortingEnabled(True)
            self.table.setUpdatesEnabled(True)

            # 执行完整垃圾回收
            if has_memory_manager:
                force_gc()
            else:
                gc.collect(2)

            # 强制刷新表格
            self.table.viewport().update()

            # 记录完成时间和内存使用情况
            elapsed_time = time.time() - start_time
            if has_memory_manager:
                memory_info = get_memory_usage()
                if memory_info:
                    memory_mb, available_mb, total_mb = memory_info
                    info(f"表格更新完成，耗时: {elapsed_time:.2f}秒，内存使用: {memory_mb:.2f}MB")
            else:
                info(f"表格基本信息更新完成，耗时: {elapsed_time:.2f}秒，共 {len(accounts)} 个账号")

        except Exception as e:
            error(f"更新账号表格基本信息时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())

            # 确保表格更新和排序被重新启用
            try:
                self.table.setSortingEnabled(True)
                self.table.setUpdatesEnabled(True)
            except Exception as ui_e:
                error(f"重新启用表格更新和排序时出错: {str(ui_e)}")

            # 执行垃圾回收
            gc.collect(2)

    def on_batch_data_loaded(self, batch_data):
        """批量数据加载回调 - 超级优化版本，防止UI卡顿和内存溢出

        Args:
            batch_data: 批量数据列表，每项为(账号ID, 账号数据)元组
        """
        try:
            # 导入垃圾回收模块
            import gc

            # 如果批量数据为空，直接返回
            if not batch_data:
                return

            # 记录内存使用情况
            try:
                import psutil
                process = psutil.Process()
                memory_info = process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                debug(f"批量数据处理开始时内存使用: {memory_mb:.2f} MB，数据量: {len(batch_data)}")
            except Exception:
                pass

            # 暂时禁用表格更新，避免频繁重绘
            self.table.setUpdatesEnabled(False)

            # 暂时禁用排序，提高性能
            self.table.setSortingEnabled(False)

            # 处理批量数据
            for account_id, account_data in batch_data:
                try:
                    # 查找账号对应的行索引
                    if account_id:
                        # 更新数据
                        self.update_account_data_in_table(account_id, account_data)

                    # 清理数据引用，帮助垃圾回收
                    del account_data
                except Exception as e:
                    # 单个账号处理错误不应该影响整个批次
                    error(f"处理账号 {account_id} 数据时出错: {str(e)}")
                    continue

            # 清理批量数据引用，帮助垃圾回收
            del batch_data

            # 执行垃圾回收
            gc.collect(2)

            # 重新启用表格更新和排序
            self.table.setSortingEnabled(True)
            self.table.setUpdatesEnabled(True)

            # 每处理一批数据后，处理Qt事件，保持UI响应
            QApplication.processEvents()

            # 短暂暂停，让出CPU，防止内存溢出
            time.sleep(0.2)  # 增加暂停时间，给系统更多时间处理

            # 记录内存使用情况
            try:
                import psutil
                process = psutil.Process()
                memory_info = process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                debug(f"批量数据处理完成后内存使用: {memory_mb:.2f} MB")
            except Exception:
                pass

        except Exception as e:
            error(f"处理批量数据时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())  # 添加详细的错误堆栈

            # 执行垃圾回收
            gc.collect(2)

            # 确保表格更新和排序被重新启用
            self.table.setSortingEnabled(True)
            self.table.setUpdatesEnabled(True)

    def on_data_load_completed(self):
        """数据加载完成回调 - 优化版本，减少内存压力"""
        try:
            # 导入垃圾回收模块
            import gc

            info("账号数据加载完成，开始清理资源")

            # 记录内存使用情况
            try:
                import psutil
                process = psutil.Process()
                memory_info = process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                info(f"数据加载完成回调开始时内存使用: {memory_mb:.2f} MB")
            except Exception:
                pass

            # 执行垃圾回收
            gc.collect(2)

            # 短暂暂停，让出CPU
            time.sleep(0.5)

            # 处理Qt事件，确保UI响应
            QApplication.processEvents()

            # 记录内存使用情况
            try:
                import psutil
                process = psutil.Process()
                memory_info = process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                info(f"数据加载完成回调结束时内存使用: {memory_mb:.2f} MB")
            except Exception:
                pass

        except Exception as e:
            error(f"数据加载完成回调时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())

        try:
            # 记录内存使用情况
            try:
                import psutil
                process = psutil.Process()
                memory_info = process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                info(f"数据加载完成回调开始时内存使用: {memory_mb:.2f} MB")
            except ImportError:
                info("无法导入psutil模块，跳过内存使用统计")
            except Exception as mem_e:
                warning(f"获取内存使用信息时出错: {str(mem_e)}")

            # 关闭进度对话框
            if hasattr(self, 'data_progress_dialog') and self.data_progress_dialog:
                try:
                    self.data_progress_dialog.accept()
                    info("已关闭数据加载进度对话框")
                except Exception as dialog_e:
                    warning(f"关闭进度对话框时出错: {str(dialog_e)}")
                finally:
                    self.data_progress_dialog = None

            # 强制刷新表格
            try:
                self.table.viewport().update()
                info("已刷新账号表格视图")
            except Exception as table_e:
                warning(f"刷新表格视图时出错: {str(table_e)}")

            # 清理资源
            if hasattr(self, 'account_id_to_row'):
                try:
                    delattr(self, 'account_id_to_row')
                    info("已清理账号ID映射")
                except Exception as attr_e:
                    warning(f"清理账号ID映射时出错: {str(attr_e)}")

            # 强制进行完整垃圾回收
            try:
                import gc
                gc.collect(2)
                info("已执行强制垃圾回收")

                # 再次记录内存使用情况
                try:
                    import psutil
                    process = psutil.Process()
                    memory_info = process.memory_info()
                    memory_mb = memory_info.rss / 1024 / 1024
                    info(f"垃圾回收后内存使用: {memory_mb:.2f} MB")
                except Exception:
                    pass
            except Exception as gc_e:
                warning(f"执行垃圾回收时出错: {str(gc_e)}")

            # 清理线程资源
            if hasattr(self, 'data_load_thread'):
                try:
                    # 断开信号连接
                    try:
                        self.data_load_thread.progress.disconnect()
                        self.data_load_thread.batch_data.disconnect()
                        self.data_load_thread.completed.disconnect()
                        info("已断开数据加载线程的信号连接")
                    except Exception as signal_e:
                        warning(f"断开信号连接时出错: {str(signal_e)}")

                    # 停止线程
                    if self.data_load_thread.isRunning():
                        info("数据加载线程仍在运行，尝试停止")
                        self.data_load_thread.stop()  # 先尝试优雅停止
                        self.data_load_thread.quit()
                        if not self.data_load_thread.wait(2000):  # 等待最多2秒
                            warning("数据加载线程未能在2秒内停止，强制终止")
                            self.data_load_thread.terminate()
                            self.data_load_thread.wait(1000)  # 再等待1秒
                    info("数据加载线程已停止")

                    # 不调用deleteLater()，避免Qt Fatal错误
                    # self.data_load_thread.deleteLater()
                    self.data_load_thread = None
                    info("已清理数据加载线程对象引用")
                except Exception as thread_e:
                    error(f"清理数据加载线程时出错: {str(thread_e)}")
                    import traceback
                    error(traceback.format_exc())
                    # 确保线程引用被清除
                    self.data_load_thread = None

            # 处理Qt事件，确保UI响应
            QApplication.processEvents()

            # 再次执行垃圾回收
            try:
                import gc
                gc.collect(2)
                info("已执行第二次强制垃圾回收")
            except Exception:
                pass

            info("数据加载完成，所有资源已清理")

        except Exception as e:
            error(f"完成数据加载时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())
        finally:
            # 确保加载状态被重置
            self.is_loading = False

            # 不设置账号已加载标志，允许重复加载
            # self.accounts_loaded = True

            # 如果已经设置了账号已加载标志，移除它
            if hasattr(self, 'accounts_loaded'):
                delattr(self, 'accounts_loaded')
                info("移除账号已加载标志，允许重复加载")

            # 强制刷新表格，确保UI更新
            self.table.viewport().update()
            QApplication.processEvents()  # 处理挂起的事件，确保UI响应

            # 记录最终内存使用情况
            try:
                import psutil
                process = psutil.Process()
                memory_info = process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                info(f"数据加载完成回调结束时内存使用: {memory_mb:.2f} MB")
            except Exception:
                pass

            info("数据加载状态已重置，加载过程完全结束，允许重复加载")

            # 更新七天总收益统计
            # 延迟0.5秒后更新收益统计，确保UI已完全更新
            QTimer.singleShot(500, self.update_total_earnings_and_signal)
            info("已安排在数据加载完成后更新七天总收益统计并发送表格刷新完成信号")

    def update_account_table(self):
        """根据cookie文件更新账号表格"""
        try:
            # 设置加载状态为True，用于UI优化
            self.is_loading = True

            # 获取总文件数
            total_files = len(self.cookie_files)

            # 如果文件数量超过20个，显示进度对话框
            progress_dialog = None
            if total_files > 20:
                from app.widgets.progress_dialog import ProgressDialog
                progress_dialog = ProgressDialog("更新账号表格", self, cancelable=False)
                progress_dialog.update_progress(0, "正在准备更新账号表格...", f"共 {total_files} 个账号")
                progress_dialog.show()

                # 处理Qt事件，确保对话框显示
                QApplication.processEvents()

            # 暂时禁用表格排序，提高性能
            self.table.setSortingEnabled(False)

            # 暂时禁用表格更新，避免频繁重绘
            self.table.setUpdatesEnabled(False)

            # 清空表格
            self.table.clearContents()

            # 设置行数
            self.table.setRowCount(total_files)

            # 添加数据
            for row, cookie_file in enumerate(self.cookie_files):
                try:
                    # 获取文件名（不含路径）
                    file_name = os.path.basename(cookie_file)
                    account_id = os.path.splitext(file_name)[0]  # 移除.txt后缀作为账号ID

                    # 尝试读取cookie文件内容，但不再需要备注信息
                    try:
                        with open(cookie_file, 'r', encoding='utf-8') as f:
                            cookie_data = json.load(f)
                    except Exception as e:
                        # 静默处理读取错误
                        pass

                    # 昵称列设置为账号ID
                    item = QTableWidgetItem(account_id)
                    item.setTextAlignment(Qt.AlignCenter)
                    self.table.setItem(row, 0, item)

                    # 在第二列(账号列)显示不带后缀的用户名
                    item = QTableWidgetItem(account_id)
                    item.setTextAlignment(Qt.AlignCenter)
                    # 设置账号列为可编辑
                    item.setFlags(item.flags() | Qt.ItemIsEditable)
                    self.table.setItem(row, 1, item)

                    # 设置状态为"待验证"
                    status_item = QTableWidgetItem("待验证")
                    status_item.setTextAlignment(Qt.AlignCenter)
                    status_item.setBackground(QColor(255, 230, 200)) # 浅橙色背景
                    status_item.setForeground(QColor(153, 76, 0))    # 深橙色文字

                    status_item.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
                    self.table.setItem(row, 2, status_item)

                    # 添加默认信息
                    for col in [3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18]:
                        item = QTableWidgetItem("--")
                        item.setTextAlignment(Qt.AlignCenter)
                        self.table.setItem(row, col, item)

                    # 添加操作按钮
                    self.add_operation_buttons(row)

                    # 查找并加载账号的保存数据
                    self.update_account_data_in_table(row, account_id)

                    # 更新进度对话框
                    if progress_dialog:
                        progress_percent = int((row + 1) / total_files * 100)
                        progress_dialog.update_progress(
                            progress_percent,
                            f"正在更新账号表格... {progress_percent}%",
                            f"正在处理: {account_id} ({row+1}/{total_files})"
                        )
                    # 每处理10个账号，刷新一次UI并短暂让出CPU
                    elif row % 10 == 0 and row > 0:
                        # 更新进度显示
                        progress_percent = int((row / total_files) * 100)
                        info(f"正在加载账号数据... {progress_percent}% ({row}/{total_files})")

                        # 处理挂起的事件，保持UI响应
                        QApplication.processEvents()

                        # 短暂暂停，让出CPU
                        time.sleep(0.01)

                except Exception as e:
                    error(f"处理账号文件 {cookie_file} 时出错: {str(e)}")
                    continue

            # 更新进度对话框
            if progress_dialog:
                progress_dialog.update_progress(100, "完成账号表格更新", "正在应用更改...")

            # 重新启用表格更新和排序
            self.table.setUpdatesEnabled(True)
            self.table.setSortingEnabled(True)

            # 强制重绘表格
            self.table.repaint()

            # 强制刷新表格视图
            self.table.viewport().update()
            QApplication.processEvents()  # 处理挂起的事件，确保UI响应
            info("账号表格已强制刷新")

            # 关闭进度对话框
            if progress_dialog:
                progress_dialog.accept()

        except Exception as e:
            error(f"更新账号表格时出错: {str(e)}")
        finally:
            # 无论成功还是失败，都将加载状态设置为False
            self.is_loading = False

    def add_operation_buttons(self, row):
        """为表格行添加操作按钮

        Args:
            row: 行索引
        """
        widget = QWidget()
        layout = QHBoxLayout(widget)
        layout.setContentsMargins(2, 2, 2, 2)  # 减小上下边距
        layout.setSpacing(6)  # 调整按钮间的间距

        login_btn = QPushButton("登录")
        login_btn.setStyleSheet("""
            QPushButton {
                background-color: #006400;
                color: #FFFFFF;
                font-size: 11px;
                font-weight: bold;
                border: 1px solid #000000;
                border-radius: 2px;
                padding: 2px 4px;
                min-width: 40px;
                min-height: 18px;
            }
            QPushButton:hover {
                background-color: #008800;
                border: 1px solid #FFFF00;
            }
        """)
        login_btn.setIcon(self.style().standardIcon(QStyle.SP_DialogOkButton))
        login_btn.setIconSize(QSize(10, 10))  # 减小图标大小
        # 使用 functools.partial 替代 lambda，避免闭包问题
        from functools import partial
        login_btn.clicked.connect(partial(self.login_account, row))
        layout.addWidget(login_btn)

        delete_btn = QPushButton("删除")
        delete_btn.setStyleSheet("""
            QPushButton {
                background-color: #8B0000;
                color: #FFFFFF;
                font-size: 11px;
                font-weight: bold;
                border: 1px solid #000000;
                border-radius: 2px;
                padding: 2px 4px;
                min-width: 40px;
                min-height: 18px;
            }
            QPushButton:hover {
                background-color: #A00000;
                border: 1px solid #FFFF00;
            }
        """)
        delete_btn.setIcon(self.style().standardIcon(QStyle.SP_DialogCloseButton))
        delete_btn.setIconSize(QSize(10, 10))  # 减小图标大小
        # 使用 functools.partial 替代 lambda，避免闭包问题
        delete_btn.clicked.connect(partial(self.delete_account, row))
        layout.addWidget(delete_btn)

        edit_btn = QPushButton("存稿")
        edit_btn.setStyleSheet("""
            QPushButton {
                background-color: #4B0082;
                color: #FFFFFF;
                font-size: 11px;
                font-weight: bold;
                border: 1px solid #000000;
                border-radius: 2px;
                padding: 2px 4px;
                min-width: 40px;
                min-height: 18px;
            }
            QPushButton:hover {
                background-color: #5A0095;
                border: 1px solid #FFFF00;
            }
        """)
        edit_btn.setIcon(self.style().standardIcon(QStyle.SP_FileDialogDetailedView))
        edit_btn.setIconSize(QSize(10, 10))  # 减小图标大小
        # 使用 functools.partial 替代 lambda，避免闭包问题
        edit_btn.clicked.connect(partial(self.edit_account, row))
        layout.addWidget(edit_btn)

        # 添加数据采集按钮
        collect_btn = QPushButton("采集")
        collect_btn.setStyleSheet("""
            QPushButton {
                background-color: #008080;
                color: #FFFFFF;
                font-size: 11px;
                font-weight: bold;
                border: 1px solid #000000;
                border-radius: 2px;
                padding: 2px 4px;
                min-width: 40px;
                min-height: 18px;
            }
            QPushButton:hover {
                background-color: #009999;
                border: 1px solid #FFFF00;
            }
        """)
        collect_btn.setIcon(self.style().standardIcon(QStyle.SP_FileDialogInfoView))
        collect_btn.setIconSize(QSize(10, 10))  # 减小图标大小
        # 使用 functools.partial 替代 lambda，避免闭包问题
        collect_btn.clicked.connect(partial(self.collect_account_data, row=row))
        layout.addWidget(collect_btn)

        widget.setLayout(layout)
        self.table.setCellWidget(row, 18, widget)  # 设置为第19列（索引18），对应操作列

    def login_account(self, row):
        """登录指定的账号

        Args:
            row: 行索引
        """
        # 验证行索引有效性
        if row < 0 or row >= len(self.cookie_files):
            warning(f"登录账号时行索引无效: {row}")
            QMessageBox.warning(self, "登录失败", "无效的账号索引")
            return

        cookie_file = self.cookie_files[row]
        account_id = self.table.item(row, 1).text() if self.table.item(row, 1) else "未知"

        debug(f"准备登录账号: {account_id}, Cookie文件: {cookie_file}")

        # 从设置选项卡获取指纹浏览器设置
        fingerprint_settings = self.get_fingerprint_settings()

        # 基本登录设置
        mobile_mode = False
        disable_js = False
        clear_cookies = True

        # 单独登录不使用指纹浏览器（根据用户要求）
        use_fingerprint = False

        info(f"使用登录设置: 指纹浏览器={use_fingerprint}, 手机端模式={mobile_mode}, 禁用JS={disable_js}, 清除Cookie={clear_cookies}")

        # 更新状态
        status_item = QTableWidgetItem("登录中...")
        status_item.setTextAlignment(Qt.AlignCenter)

        # 检查是否处于夜间模式
        is_night_mode = self.window().property("nightMode")

        if is_night_mode:
            status_item.setBackground(QColor(0, 0, 80))      # 深蓝色背景(夜间模式)
            status_item.setForeground(QColor(150, 150, 255)) # 亮蓝色文字(夜间模式)
        else:
            status_item.setBackground(QColor(200, 200, 255)) # 浅蓝色背景(日间模式)
            status_item.setForeground(QColor(0, 0, 150))     # 深蓝色文字(日间模式)

        status_item.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
        self.table.setItem(row, 2, status_item)

        # 直接设置为不导航到上传页面（用户要求移除询问对话框）
        navigate_to_upload = False

        # 使用线程异步执行登录，避免界面卡死
        def login_task():
            try:
                # 返回结果元组(成功标志, 消息)
                # 单独登录账号时可选择是否跳转到视频上传界面
                # 注意：重构后的login_with_cookie函数支持导航到上传页面并处理网络错误
                return login_with_cookie(
                    cookie_file=cookie_file,
                    use_fingerprint=use_fingerprint,
                    fingerprint_settings=fingerprint_settings,
                    mobile_mode=mobile_mode,
                    disable_js=disable_js,
                    clear_cookies=clear_cookies,
                    navigate_to_upload=navigate_to_upload
                )
            except Exception as e:
                error(f"登录账号时出错: {str(e)}")
                import traceback
                error(traceback.format_exc())  # 添加详细的错误堆栈信息，帮助调试
                return False, str(e)

        def on_login_finished(result):
            try:
                success_flag, message = result
                if success_flag:
                    # 登录成功
                    success(f"账号 {account_id} 登录成功")

                    # 更新状态
                    status_item = QTableWidgetItem("已登录")
                    status_item.setTextAlignment(Qt.AlignCenter)

                    # 检查是否处于夜间模式
                    is_night_mode = self.window().property("nightMode")

                    if is_night_mode:
                        status_item.setBackground(QColor(0, 80, 0))      # 深绿色背景(夜间模式)
                        status_item.setForeground(QColor(150, 255, 150)) # 亮绿色文字(夜间模式)
                    else:
                        status_item.setBackground(QColor(200, 255, 200)) # 浅绿色背景(日间模式)
                        status_item.setForeground(QColor(0, 100, 0))     # 深绿色文字(日间模式)

                    status_item.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
                    self.table.setItem(row, 2, status_item)

                    # 不再弹窗通知用户，只记录日志
                    info(f"账号 {account_id} 登录成功: {message}")
                else:
                    # 登录失败
                    warning(f"账号 {account_id} 登录失败: {message}")

                    # 更新状态
                    status_item = QTableWidgetItem("登录失败")
                    status_item.setTextAlignment(Qt.AlignCenter)

                    # 检查是否处于夜间模式
                    is_night_mode = self.window().property("nightMode")

                    if is_night_mode:
                        status_item.setBackground(QColor(80, 0, 0))      # 深红色背景(夜间模式)
                        status_item.setForeground(QColor(255, 150, 150)) # 亮红色文字(夜间模式)
                    else:
                        status_item.setBackground(QColor(255, 200, 200)) # 浅红色背景(日间模式)
                        status_item.setForeground(QColor(180, 0, 0))     # 深红色文字(日间模式)

                    status_item.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
                    self.table.setItem(row, 2, status_item)

                    # 弹窗通知用户
                    QMessageBox.warning(self, "登录失败", f"登录账号 {account_id} 失败: {message}")
            except Exception as e:
                # 防止回调函数中的错误导致应用崩溃
                error(f"处理登录完成回调时发生异常: {str(e)}")
                import traceback
                error(traceback.format_exc())
                try:
                    # 更新状态为错误
                    status_item = QTableWidgetItem("登录异常")
                    status_item.setTextAlignment(Qt.AlignCenter)
                    status_item.setBackground(QColor(255, 150, 150))
                    status_item.setForeground(QColor(180, 0, 0))
                    status_item.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
                    self.table.setItem(row, 2, status_item)

                    # 显示错误对话框
                    QMessageBox.critical(self, "登录异常", f"登录过程中发生异常，请联系开发者。\n错误信息: {str(e)}")
                except Exception:
                    pass  # 如果连显示错误对话框都失败，就不再尝试了

        def on_login_error(error_message):
            try:
                error(f"登录账号 {account_id} 过程中出错: {error_message}")
                import traceback
                error(traceback.format_exc())  # 添加详细的错误堆栈信息，帮助调试

                # 更新状态
                status_item = QTableWidgetItem("登录错误")
                status_item.setTextAlignment(Qt.AlignCenter)

                # 检查是否处于夜间模式
                is_night_mode = self.window().property("nightMode")

                if is_night_mode:
                    status_item.setBackground(QColor(100, 0, 0))     # 深红色背景(夜间模式)
                    status_item.setForeground(QColor(255, 120, 120)) # 亮红色文字(夜间模式)
                else:
                    status_item.setBackground(QColor(255, 150, 150)) # 较深红色背景(日间模式)
                    status_item.setForeground(QColor(180, 0, 0))     # 深红色文字(日间模式)

                status_item.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
                self.table.setItem(row, 2, status_item)

                # 弹窗通知用户
                QMessageBox.critical(self, "登录错误", f"登录账号 {account_id} 时发生错误: {error_message}")
            except Exception as e:
                # 防止回调函数中的错误导致应用崩溃
                error(f"处理登录错误回调时发生异常: {str(e)}")
                import traceback
                error(traceback.format_exc())
                try:
                    QMessageBox.critical(self, "严重错误", f"登录过程中发生严重错误，请联系开发者。\n错误信息: {str(e)}")
                except Exception:
                    pass  # 如果连显示错误对话框都失败，就不再尝试了

        # 创建并启动工作线程
        thread, worker = run_in_thread(login_task, on_login_finished, on_login_error)

        # 保存引用，避免被垃圾回收
        self.active_threads.append((thread, worker))

    def get_fingerprint_settings(self):
        """获取指纹浏览器设置

        Returns:
            dict: 指纹浏览器设置字典
        """
        try:
            # 尝试从主窗口获取设置选项卡
            main_window = self.window()
            if hasattr(main_window, 'setting_tab'):
                return main_window.setting_tab.get_fingerprint_settings()
            else:
                # 如果无法获取设置选项卡，从配置文件读取
                settings_file = "settings.json"
                if os.path.exists(settings_file):
                    with open(settings_file, 'r', encoding='utf-8') as f:
                        settings = json.load(f)
                        return {
                            'use_fingerprint': settings.get('use_fingerprint', False),
                            'enable_random_ua': settings.get('enable_random_ua', True),
                            'device_type': settings.get('device_type', '自动随机'),
                            'browser_type': settings.get('browser_type', '自动随机'),
                            'resolution_type': settings.get('resolution_type', '随机分辨率'),
                            'pixel_ratio': settings.get('pixel_ratio', '随机'),
                            'browser_language': settings.get('browser_language', '中文'),
                            'randomize_canvas': settings.get('randomize_canvas', True),
                            'disable_webrtc': settings.get('disable_webrtc', True),
                            'enable_anti_detection': settings.get('enable_anti_detection', True)
                        }
        except Exception as e:
            warning(f"获取指纹浏览器设置失败: {str(e)}")

        # 返回默认设置
        return {
            'use_fingerprint': False,
            'enable_random_ua': True,
            'device_type': '自动随机',
            'browser_type': '自动随机',
            'resolution_type': '随机分辨率',
            'pixel_ratio': '随机',
            'browser_language': '中文',
            'randomize_canvas': True,
            'disable_webrtc': True,
            'enable_anti_detection': True
        }

    def delete_account(self, row):
        """删除账号

        Args:
            row: 行索引
        """
        # 确保在主线程中执行
        from PyQt5.QtCore import QThread
        if QThread.currentThread() != self.thread():
            warning("删除账号操作必须在主线程中执行")
            return

        # 验证行索引有效性
        if row < 0 or row >= len(self.cookie_files):
            warning(f"删除账号时行索引无效: {row}")
            QMessageBox.warning(self, "删除失败", "无效的账号索引")
            return

        cookie_file = self.cookie_files[row]
        account_id = self.table.item(row, 1).text() if self.table.item(row, 1) else "未知"

        debug(f"准备删除账号: {account_id}, Cookie文件: {cookie_file}")

        # 弹出确认对话框
        debug(f"显示删除确认对话框，账号: {account_id}")
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除账号 {account_id} 吗？\n对应的Cookie文件也将被删除: {cookie_file}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.No:
            debug(f"用户取消删除账号: {account_id}")
            return

        info(f"用户确认删除账号: {account_id}, Cookie文件: {cookie_file}")

        try:
            # 从列表中移除
            debug(f"从内存列表中移除账号: {account_id}")
            self.cookie_files.pop(row)

            # 如果文件存在，则删除文件
            if os.path.exists(cookie_file):
                debug(f"开始删除Cookie文件: {cookie_file}")

                # 检查文件是否被保护（隐藏/只读），如果是则先移除保护属性
                try:
                    import ctypes
                    from ctypes import wintypes

                    # 获取文件属性
                    GetFileAttributes = ctypes.windll.kernel32.GetFileAttributesW
                    GetFileAttributes.argtypes = [wintypes.LPWSTR]
                    GetFileAttributes.restype = wintypes.DWORD

                    attrs = GetFileAttributes(cookie_file)
                    if attrs != 0xFFFFFFFF:  # 有效的属性值
                        FILE_ATTRIBUTE_HIDDEN = 0x02
                        FILE_ATTRIBUTE_READONLY = 0x01

                        # 检查是否有隐藏或只读属性
                        if (attrs & FILE_ATTRIBUTE_HIDDEN) or (attrs & FILE_ATTRIBUTE_READONLY):
                            debug(f"文件被保护，正在移除保护属性: {cookie_file}")

                            # 移除保护属性，设置为普通文件
                            SetFileAttributes = ctypes.windll.kernel32.SetFileAttributesW
                            SetFileAttributes.argtypes = [wintypes.LPWSTR, wintypes.DWORD]
                            SetFileAttributes.restype = wintypes.BOOL

                            FILE_ATTRIBUTE_NORMAL = 0x80
                            if SetFileAttributes(cookie_file, FILE_ATTRIBUTE_NORMAL):
                                debug(f"成功移除文件保护属性: {cookie_file}")
                            else:
                                warning(f"移除文件保护属性失败: {cookie_file}")
                except Exception as attr_err:
                    warning(f"处理文件属性时出错: {str(attr_err)}")

                # 删除文件
                os.remove(cookie_file)
                debug(f"已成功删除Cookie文件: {cookie_file}")
            else:
                warning(f"Cookie文件不存在，无法删除: {cookie_file}")

            # 直接从表格中删除行，而不是重新加载整个表格
            debug(f"从表格中删除行: {row}")
            self.table.removeRow(row)

            # 使用QTimer延迟更新操作按钮，确保在主线程中安全执行
            from PyQt5.QtCore import QTimer
            QTimer.singleShot(100, lambda: self.update_operation_buttons_after_delete(row))

            # 发送cookie数量变化信号
            debug(f"发送cookie数量变化信号: {len(self.cookie_files)}个")
            self.cookie_count_changed.emit(len(self.cookie_files))

            success(f"账号删除成功: {account_id}, Cookie文件: {os.path.basename(cookie_file)}")
            # 使用QTimer延迟显示成功消息，避免阻塞UI
            def show_success_message():
                QMessageBox.information(self, "删除成功", f"账号 {account_id} 已成功删除")
            QTimer.singleShot(200, show_success_message)
        except Exception as e:
            error_msg = f"删除账号 {account_id} 时发生错误: {str(e)}"
            error(error_msg)
            QMessageBox.critical(self, "删除错误", error_msg)

    def update_operation_buttons_after_delete(self, deleted_row):
        """删除行后安全地更新操作按钮

        Args:
            deleted_row: 被删除的行索引
        """
        try:
            # 更新从删除行开始的所有后续行的操作按钮
            for i in range(deleted_row, self.table.rowCount()):
                # 检查行是否有效
                if i < self.table.rowCount():
                    # 重新添加操作按钮，确保行索引正确
                    self.add_operation_buttons(i)

            # 强制刷新表格
            self.table.viewport().update()
            debug(f"已更新删除行 {deleted_row} 后的所有操作按钮")

        except Exception as e:
            warning(f"更新删除后的操作按钮时出错: {str(e)}")

    def edit_account(self, row):
        """对单个账号执行存稿操作

        Args:
            row: 行索引
        """
        # 验证行索引有效性
        if row < 0 or row >= len(self.cookie_files):
            warning(f"执行存稿时行索引无效: {row}")
            QMessageBox.warning(self, "存稿失败", "无效的账号索引")
            return

        # 检查是否有批量存稿任务在运行
        if self._is_batch_task_running():
            warning("批量存稿任务正在运行，无法同时启动单账号存稿任务")
            QMessageBox.warning(self, "操作冲突", "批量存稿任务正在运行，请等待当前任务完成后再试")
            return

        # 检查该账号是否已经在存稿中
        account_id = self.table.item(row, 1).text() if self.table.item(row, 1) else "未知"
        if hasattr(self, 'active_cunggao_tasks') and account_id in self.active_cunggao_tasks:
            warning(f"账号 {account_id} 已有存稿任务在运行")
            QMessageBox.warning(self, "操作冲突", f"账号 {account_id} 已有存稿任务在运行，请等待当前任务完成后再试")
            return

        cookie_file = self.cookie_files[row]
        account_id = self.table.item(row, 1).text() if self.table.item(row, 1) else "未知"

        debug(f"准备为账号执行存稿: {account_id}, Cookie文件: {cookie_file}")

        # 从设置中获取视频路径和封面路径
        video_path = None
        cover_path = None

        try:
            # 获取应用设置
            settings_file = "settings.json"
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    if "video_path" in settings and os.path.exists(settings["video_path"]):
                        video_path = settings["video_path"]
                    if "cover_path" in settings and os.path.exists(settings["cover_path"]):
                        cover_path = settings["cover_path"]
        except Exception as e:
            warning(f"获取视频和封面路径时出错: {str(e)}")

        # 检查视频目录
        if not video_path or not os.path.exists(video_path):
            QMessageBox.warning(self, "存稿失败", "请先在设置中指定有效的视频文件目录")
            return

        # 检查封面目录
        if not cover_path or not os.path.exists(cover_path):
            QMessageBox.warning(self, "存稿失败", "请先在设置中指定有效的封面文件目录")
            return

        # 检查视频文件
        video_files = [f for f in os.listdir(video_path)
                      if f.lower().endswith(('.mp4', '.avi', '.mov', '.wmv', '.flv'))]
        if not video_files:
            QMessageBox.warning(self, "存稿失败", f"视频目录 {video_path} 中没有找到视频文件")
            return

        # 检查封面文件
        cover_files = [f for f in os.listdir(cover_path)
                      if f.lower().endswith(('.jpg', '.jpeg', '.png', '.webp', '.bmp'))]
        if not cover_files:
            QMessageBox.warning(self, "存稿失败", f"封面目录 {cover_path} 中没有找到图片文件")
            return

        # 打开单个账号存稿设置对话框
        settings_dialog = SingleSettingsDialog(self)
        if settings_dialog.exec_() != QDialog.Accepted:
            return

        # 获取设置
        headless_mode, draft_count = settings_dialog.get_settings()

        # 如果选择了调试模式（非无头模式），需要密码验证
        if not headless_mode:
            # 导入密码验证对话框
            from app.dialogs.password_dialog import PasswordDialog

            # 创建并显示密码验证对话框
            password_dialog = PasswordDialog(self, "单独存稿调试模式")
            if password_dialog.exec_() != QDialog.Accepted:
                info("用户取消了密码验证或密码错误，不启动单独存稿")
                return

        # 确认操作
        reply = QMessageBox.question(
            self,
            "确认存稿操作",
            f"确定要为账号 {account_id} 执行存稿操作吗？\n" +
            f"视频目录: {video_path}\n" +
            f"封面目录: {cover_path}\n" +
            f"无头模式: {'启用' if headless_mode else '禁用'}\n" +
            f"存稿次数: {draft_count}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.No:
            return

        # 更新状态
        status_item = QTableWidgetItem("存稿中...")
        status_item.setTextAlignment(Qt.AlignCenter)

        # 检查是否处于夜间模式
        is_night_mode = self.window().property("nightMode")

        if is_night_mode:
            status_item.setBackground(QColor(80, 60, 0))      # 深黄色背景(夜间模式)
            status_item.setForeground(QColor(255, 220, 100))  # 亮黄色文字(夜间模式)
        else:
            status_item.setBackground(QColor(255, 255, 200))  # 浅黄色背景(日间模式)
            status_item.setForeground(QColor(153, 102, 0))    # 棕色文字(日间模式)

        status_item.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
        self.table.setItem(row, 2, status_item)

        # 已移除"本次存稿"列，不再需要重置计数

        # 使用工作线程执行存稿任务，避免界面卡住
        # 创建一个包含单个账号的临时表格
        temp_table = QTableWidget()
        temp_table.setColumnCount(self.table.columnCount())
        temp_table.setRowCount(1)

        # 复制当前行数据到临时表格
        for col in range(self.table.columnCount()):
            item = self.table.item(row, col)
            if item:
                temp_table.setItem(0, col, QTableWidgetItem(item.text()))

        # 使用"cookies"目录作为cookie存储目录
        # 存储原始cookie文件引用
        cookie_dir = os.path.dirname(cookie_file)

        # 获取指纹浏览器设置
        fingerprint_settings = self.get_fingerprint_settings()
        use_fingerprint = fingerprint_settings.get('use_fingerprint', False)

        # 启动批量存稿线程
        try:
            info(f"开始为账号 {account_id} 执行存稿任务")
            thread, worker = run_batch_cunggao(
                table=temp_table,
                cookie_dir=cookie_dir,
                video_dir=video_path,
                cover_dir=cover_path,
                parent=self,
                headless_mode=headless_mode,
                concurrent_count=1,  # 并发数始终为1
                draft_count=draft_count,
                video_allocation=True,  # 视频公平分配
                clean_cache_before_start=False,  # 不清理缓存
                remove_popup=True,  # 单独存稿默认启用弹窗检测
                ban_mode=False,  # 单独存稿默认不启用封号模式
                handle_publish_limit=True,  # 单独存稿默认启用发布限制弹窗处理
                account_tab_ref=self,  # 传递账号标签页引用，用于进度条显示
                use_fingerprint=use_fingerprint,  # 指纹浏览器开关
                fingerprint_settings=fingerprint_settings  # 指纹浏览器设置
            )

            # 连接信号槽
            if hasattr(worker, 'status_signal'):
                worker.status_signal.connect(lambda msg: self.on_status_update(f"[{account_id}] {msg}"))

            if hasattr(worker, 'finished_signal'):
                # 使用lambda捕获当前的thread和worker变量，finished_signal不传递参数
                worker.finished_signal.connect(lambda: self.on_single_cunggao_finished(row, account_id, thread, worker))

            # 连接进度信号到系统日志界面的进度条
            if hasattr(worker, 'progress_signal'):
                worker.progress_signal.connect(lambda current, total: self.update_single_draft_progress(account_id, current, total))

            # 连接账号进度信号到系统日志界面
            if hasattr(worker, 'account_progress_signal'):
                worker.account_progress_signal.connect(self.on_single_draft_account_progress)

            # 将线程和工作对象保存到活动任务字典中
            self.active_cunggao_tasks[account_id] = (thread, worker)

            success(f"成功启动账号 {account_id} 的存稿任务")
        except Exception as e:
            error(f"启动存稿任务失败: {str(e)}")

            # 更新状态
            status_item = QTableWidgetItem("存稿失败")
            status_item.setTextAlignment(Qt.AlignCenter)

            # 检查是否处于夜间模式
            is_night_mode = self.window().property("nightMode")

            if is_night_mode:
                status_item.setBackground(QColor(80, 0, 0))      # 深红色背景(夜间模式)
                status_item.setForeground(QColor(255, 150, 150)) # 亮红色文字(夜间模式)
            else:
                status_item.setBackground(QColor(255, 200, 200)) # 浅红色背景(日间模式)
                status_item.setForeground(QColor(180, 0, 0))     # 深红色文字(日间模式)

            status_item.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
            self.table.setItem(row, 2, status_item)

            QMessageBox.critical(self, "存稿失败", f"启动存稿任务失败: {str(e)}")

    def on_single_cunggao_finished(self, row, account_id, thread=None, worker=None):
        """单个账号存稿完成回调

        Args:
            row: 行索引
            account_id: 账号ID
            thread: 线程对象（可选）
            worker: 工作对象（可选）
        """
        # 更新状态 - 添加绿色实心点
        status_item = QTableWidgetItem("● 存稿完成")
        status_item.setTextAlignment(Qt.AlignCenter)

        # 检查是否处于夜间模式
        is_night_mode = self.window().property("nightMode")

        if is_night_mode:
            status_item.setBackground(QColor(0, 80, 0))      # 深绿色背景(夜间模式)
            status_item.setForeground(QColor(150, 255, 150)) # 亮绿色文字(夜间模式)
        else:
            status_item.setBackground(QColor(200, 255, 200)) # 浅绿色背景(日间模式)
            status_item.setForeground(QColor(0, 100, 0))     # 深绿色文字(日间模式)

        status_item.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
        self.table.setItem(row, 2, status_item)

        # 已移除"本次存稿"列，不再更新存稿次数

        # 显示消息
        info(f"账号 {account_id} 存稿任务已完成")

        # 清理单账号存稿任务资源
        try:
            # 如果没有提供线程和工作对象，尝试从活动任务字典中获取
            if (thread is None or worker is None) and account_id in self.active_cunggao_tasks:
                thread, worker = self.active_cunggao_tasks[account_id]

            # 断开信号连接
            if worker:
                if hasattr(worker, 'status_signal'):
                    try:
                        worker.status_signal.disconnect()
                    except Exception:
                        pass

                if hasattr(worker, 'finished_signal'):
                    try:
                        worker.finished_signal.disconnect()
                    except Exception:
                        pass

                # 不调用deleteLater()，避免Qt Fatal错误
                # worker.deleteLater()

            # 清理线程对象 - 使用更安全的清理方式
            if thread:
                try:
                    if thread.isRunning():
                        # 先尝试优雅停止
                        thread.quit()
                        # 等待线程结束，最多等待5秒
                        if not thread.wait(5000):
                            # 如果5秒内没有结束，强制终止
                            warning(f"单独存稿线程未在5秒内停止，强制终止")
                            thread.terminate()
                            if not thread.wait(3000):
                                error(f"单独存稿线程强制终止失败")
                                # 不删除对象，避免崩溃
                                return
                            else:
                                info(f"单独存稿线程已强制终止")
                        else:
                            info(f"单独存稿线程已优雅停止")

                    # 确保线程完全停止后再删除对象
                    if not thread.isRunning():
                        # 直接删除对象，避免使用QTimer
                        try:
                            # 不调用deleteLater()，避免Qt Fatal错误
                            # if thread and hasattr(thread, 'deleteLater'):
                            #     thread.deleteLater()
                            info(f"单独存稿线程已停止，跳过deleteLater()调用")
                        except Exception as e:
                            debug(f"清理单独存稿线程时出错: {str(e)}")
                    else:
                        warning(f"单独存稿线程仍在运行，跳过删除操作")

                except Exception as e:
                    from app.utils.logger import warning
                    warning(f"清理单独存稿线程时出错: {str(e)}")
                    # 即使出错也要尝试删除，但要更安全
                    try:
                        if not thread.isRunning():
                            # 不调用deleteLater()，避免Qt Fatal错误
                            # thread.deleteLater()
                            info("线程已停止，跳过deleteLater()调用")
                    except Exception:
                        pass

            # 从活动任务字典中移除
            if account_id in self.active_cunggao_tasks:
                del self.active_cunggao_tasks[account_id]

            # 执行垃圾回收
            import gc
            gc.collect()

            info(f"账号 {account_id} 存稿任务资源已清理")
        except Exception as e:
            error(f"清理单账号存稿任务资源时出错: {str(e)}")

    def update_single_draft_progress(self, account_id, current, total):
        """更新单独存稿的总体进度"""
        try:
            # 发送到主窗口的批量存稿进度更新
            main_window = self.window()
            if hasattr(main_window, 'update_batch_progress'):
                main_window.update_batch_progress(current, total)

            # 添加进度日志
            from app.utils.logger import info
            info(f"单独存稿进度更新: {current}/{total}")

        except Exception as e:
            error(f"更新单独存稿进度失败: {str(e)}")

    def on_single_draft_account_progress(self, account_id, progress_type, message, current=0, total=0):
        """处理单独存稿的账号进度更新"""
        try:
            # 发送到主窗口的账号进度更新
            main_window = self.window()
            if hasattr(main_window, 'update_account_progress'):
                # 计算进度百分比
                if total > 0:
                    progress_percent = int((current / total) * 100)
                else:
                    progress_percent = 0

                # 根据进度类型设置状态
                if progress_type == "complete":
                    status = "已完成"
                    progress_percent = 100
                elif progress_type == "progress":
                    status = f"存稿中 ({current}/{total})"
                elif progress_type == "account_fail":
                    status = "失败"
                    progress_percent = 0
                elif progress_type == "error":
                    # 检查是否是禁言错误
                    if "禁言" in message:
                        status = "禁言"
                        # 更新表格状态为禁言
                        self.update_account_status_in_table(account_id, "禁言", "banned")
                    else:
                        status = "失败"
                    progress_percent = 0
                else:
                    status = message

                main_window.update_account_progress(account_id, status, progress_percent)

            # 添加账号进度日志
            from app.utils.logger import info
            info(f"账号 {account_id} 进度更新: {progress_type} - {message}")

        except Exception as e:
            error(f"处理单独存稿账号进度失败: {str(e)}")

    def update_account_status_in_table(self, account_id, status_text, status_type="info"):
        """更新表格中账号的状态显示

        Args:
            account_id: 账号ID
            status_text: 状态文本
            status_type: 状态类型 ("waiting", "processing", "success", "failed", "login_failed", "banned")
        """
        try:
            # 查找账号对应的行
            for row in range(self.table.rowCount()):
                # 检查账号列（第1列，索引1）
                account_item = self.table.item(row, 1)
                if account_item and account_item.text().strip() == account_id:
                    # 根据状态类型设置颜色
                    from PyQt5.QtGui import QColor
                    color_map = {
                        "waiting": (QColor(255, 230, 200), QColor(153, 76, 0)),      # 浅橙色背景，深橙色文字
                        "processing": (QColor(200, 230, 255), QColor(0, 76, 153)),   # 浅蓝色背景，深蓝色文字
                        "success": (QColor(200, 255, 200), QColor(0, 128, 0)),       # 浅绿色背景，深绿色文字
                        "failed": (QColor(255, 200, 200), QColor(153, 0, 0)),        # 浅红色背景，深红色文字
                        "login_failed": (QColor(255, 180, 180), QColor(128, 0, 0)),  # 更深的红色，表示登录失败
                        "banned": (QColor(255, 165, 0), QColor(139, 69, 19))         # 橙色背景，深橙色文字，表示禁言
                    }

                    bg_color, text_color = color_map.get(status_type, color_map["waiting"])

                    # 更新状态列（第2列，索引2）
                    self.update_account_table_status(row, status_text, bg_color, text_color)
                    break

        except Exception as e:
            from app.utils.logger import debug
            debug(f"更新表格状态出错: {str(e)}")

    def on_load_account_clicked(self):
        """加载账号按钮点击处理 - 增强版本，防止重复点击导致闪退"""
        try:
            # 增强的状态检查，防止重复点击
            if hasattr(self, '_button_click_in_progress') and self._button_click_in_progress:
                warning("按钮点击正在处理中，忽略重复点击")
                return

            # 设置按钮点击处理标志
            self._button_click_in_progress = True

            # 检查是否正在加载
            if hasattr(self, 'is_loading') and self.is_loading:
                warning("账号正在加载中，请等待当前加载完成")
                QMessageBox.information(self, "提示", "账号正在加载中，请等待当前加载完成")
                return

            # 检查是否有后台加载正在进行
            if hasattr(self, 'background_loading_active') and self.background_loading_active:
                warning("后台加载正在进行中，请等待完成")
                QMessageBox.information(self, "提示", "后台加载正在进行中，请等待完成")
                return

            # 检查账号加载器是否正在工作
            if hasattr(self, 'account_loader') and self.account_loader and hasattr(self.account_loader, 'is_loading') and self.account_loader.is_loading:
                warning("账号加载器正在工作中，请等待完成")
                QMessageBox.information(self, "提示", "账号加载器正在工作中，请等待完成")
                return

            # 检查进度对话框是否已存在
            if hasattr(self, 'progress_dialog') and self.progress_dialog and self.progress_dialog.isVisible():
                warning("进度对话框已存在，请等待当前操作完成")
                QMessageBox.information(self, "提示", "进度对话框已存在，请等待当前操作完成")
                return

            # 安全禁用加载按钮，防止重复点击
            try:
                if hasattr(self, 'load_account_btn') and self.load_account_btn:
                    self.load_account_btn.setEnabled(False)
                    self.load_account_btn.setText("加载中...")

                    # 设置定时器重新启用按钮（防止按钮永久禁用）
                    QTimer.singleShot(15000, self._re_enable_load_button)  # 增加到15秒
            except Exception as e:
                warning(f"禁用加载按钮时出错: {str(e)}")

            info("用户点击加载账号按钮")

            # 发送加载账号信号
            self.load_account_clicked.emit()

        except Exception as e:
            error(f"处理加载账号按钮点击时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())

            # 确保按钮重新启用
            self._re_enable_load_button()
        finally:
            # 清除按钮点击处理标志
            if hasattr(self, '_button_click_in_progress'):
                self._button_click_in_progress = False

    def _re_enable_load_button(self):
        """重新启用加载账号按钮 - 增强版本"""
        try:
            if hasattr(self, 'load_account_btn') and self.load_account_btn:
                self.load_account_btn.setEnabled(True)
                self.load_account_btn.setText("加载账号")
                info("已重新启用加载账号按钮")

            # 清除按钮点击处理标志
            if hasattr(self, '_button_click_in_progress'):
                self._button_click_in_progress = False

        except Exception as e:
            error(f"重新启用加载按钮时出错: {str(e)}")
            # 即使出错也要清除标志
            if hasattr(self, '_button_click_in_progress'):
                self._button_click_in_progress = False

    def _check_loading_state(self):
        """检查当前加载状态 - 调试用方法"""
        try:
            state_info = {
                'is_loading': getattr(self, 'is_loading', False),
                'background_loading_active': getattr(self, 'background_loading_active', False),
                'button_click_in_progress': getattr(self, '_button_click_in_progress', False),
                'account_loader_exists': hasattr(self, 'account_loader') and self.account_loader is not None,
                'account_loader_loading': False,
                'progress_dialog_exists': hasattr(self, 'progress_dialog') and self.progress_dialog is not None,
                'progress_dialog_visible': False,
                'button_enabled': True
            }

            if hasattr(self, 'account_loader') and self.account_loader:
                state_info['account_loader_loading'] = getattr(self.account_loader, 'is_loading', False)

            if hasattr(self, 'progress_dialog') and self.progress_dialog:
                try:
                    state_info['progress_dialog_visible'] = self.progress_dialog.isVisible()
                except:
                    state_info['progress_dialog_visible'] = False

            if hasattr(self, 'load_account_btn') and self.load_account_btn:
                try:
                    state_info['button_enabled'] = self.load_account_btn.isEnabled()
                except:
                    state_info['button_enabled'] = False

            debug(f"当前加载状态: {state_info}")
            return state_info

        except Exception as e:
            error(f"检查加载状态时出错: {str(e)}")
            return None

    def _stop_previous_loading_tasks(self):
        """停止之前的加载任务，防止冲突 - 增强版本"""
        try:
            info("正在停止之前的加载任务...")

            # 停止账号加载器
            if hasattr(self, 'account_loader') and self.account_loader:
                try:
                    if hasattr(self.account_loader, 'stop_loading'):
                        self.account_loader.stop_loading()
                        info("已停止账号加载器")
                except Exception as e:
                    warning(f"停止账号加载器时出错: {str(e)}")

            # 停止后台加载线程
            if hasattr(self, 'background_loader_thread') and self.background_loader_thread:
                try:
                    if self.background_loader_thread.isRunning():
                        self.background_loader_thread.terminate()
                        self.background_loader_thread.wait(1000)  # 等待最多1秒
                        info("已停止后台加载线程")
                    self.background_loader_thread = None
                except Exception as e:
                    warning(f"停止后台加载线程时出错: {str(e)}")

            # 安全关闭进度对话框
            if hasattr(self, 'progress_dialog') and self.progress_dialog:
                try:
                    self.progress_dialog.accept()
                    self.progress_dialog = None
                    info("已关闭进度对话框")
                except Exception as e:
                    warning(f"关闭进度对话框时出错: {str(e)}")

            # 关闭加载进度对话框
            if hasattr(self, 'loading_progress_dialog') and self.loading_progress_dialog:
                self.loading_progress_dialog.accept()
                self.loading_progress_dialog = None
                info("已关闭加载进度对话框")

            # 重置状态标志
            self.background_loading_active = False

            # 执行垃圾回收
            import gc
            gc.collect()

            info("之前的加载任务已停止")

        except Exception as e:
            error(f"停止之前的加载任务时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())













    def add_account(self):
        """添加新账号"""
        info("开始添加新账号")

        # 确认是否有保存位置
        if not hasattr(self, 'cookie_path') or not self.cookie_path or not os.path.exists(self.cookie_path):
            error("添加账号失败: 未设置Cookie保存路径或路径不存在")
            QMessageBox.warning(self, "添加失败", "请先在设置中指定Cookie文件保存位置")
            return

        # 创建优化后的输入对话框让用户输入账号ID和备注
        account_id_dialog = QDialog(self)
        account_id_dialog.setWindowTitle("添加新头条账号")
        account_id_dialog.setFixedWidth(450)
        # 检查是否处于夜间模式
        is_night_mode = self.window().property("nightMode")

        if is_night_mode:
            account_id_dialog.setStyleSheet("""
                QDialog {
                    background-color: #1a1a1a;
                }
                QLabel {
                    font-size: 13px;
                    font-weight: bold;
                    color: #e0e0e0;
                }
                QLineEdit {
                    padding: 8px;
                    border: 1px solid #383838;
                    border-radius: 4px;
                    background-color: #252525;
                    font-size: 13px;
                    color: #e0e0e0;
                    min-height: 25px;
                }
                QLineEdit:focus {
                    border: 1px solid #2979ff;
                    background-color: #303030;
                }
                QPushButton {
                    background-color: #1976d2;
                    color: white;
                    border-radius: 4px;
                    padding: 8px 15px;
                    font-weight: bold;
                    min-width: 100px;
                    min-height: 30px;
                }
                QPushButton:hover {
                    background-color: #2979ff;
                }
                QPushButton[text="取消"] {
                    background-color: #424242;
                }
                QPushButton[text="取消"]:hover {
                    background-color: #616161;
                }
            """)
        else:
            account_id_dialog.setStyleSheet("""
                QDialog {
                    background-color: #FFFFFF;
                }
                QLabel {
                    font-size: 13px;
                    font-weight: bold;
                    color: #333333;
                }
                QLineEdit {
                    padding: 8px;
                    border: 1px solid #CCCCCC;
                    border-radius: 4px;
                    background-color: #FFFFFF;
                    font-size: 13px;
                    color: #333333;
                    min-height: 25px;
                }
                QLineEdit:focus {
                    border: 1px solid #1E88E5;
                }
                QPushButton {
                    background-color: #2980B9;
                    color: white;
                    border-radius: 4px;
                    padding: 8px 15px;
                    font-weight: bold;
                    min-width: 100px;
                    min-height: 30px;
                }
                QPushButton:hover {
                    background-color: #3498DB;
                }
                QPushButton[text="取消"] {
                    background-color: #95A5A6;
                }
                QPushButton[text="取消"]:hover {
                    background-color: #7F8C8D;
                }
            """)

        # 设置夜间模式属性
        account_id_dialog.setProperty("nightMode", is_night_mode)

        # 使用垂直布局
        main_layout = QVBoxLayout(account_id_dialog)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # 添加描述标签
        description = QLabel("请输入头条账号信息，点击确定后将打开浏览器进行登录")

        # 根据夜间模式设置样式
        if is_night_mode:
            description.setStyleSheet("color: #b0b0b0; font-weight: normal;")
        else:
            description.setStyleSheet("color: #666666; font-weight: normal;")

        main_layout.addWidget(description)

        # 表单布局
        form_layout = QFormLayout()
        form_layout.setContentsMargins(0, 10, 0, 10)
        form_layout.setSpacing(15)
        form_layout.setLabelAlignment(Qt.AlignRight | Qt.AlignVCenter)

        # 账号ID输入框
        account_id_input = QLineEdit()
        account_id_input.setPlaceholderText("请输入账号ID (如: ***********)")
        form_layout.addRow("账号ID:", account_id_input)

        # 备注输入框
        remark_input = QLineEdit()
        remark_input.setPlaceholderText("可选：账号备注 (如: 头条测试号)")
        form_layout.addRow("账号备注:", remark_input)

        main_layout.addLayout(form_layout)

        # 对话框按钮 - 使用自定义水平布局
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 10, 0, 0)
        button_layout.setSpacing(15)

        # 添加弹性空间，使按钮居中对齐
        button_layout.addStretch()

        # 确定和取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.clicked.connect(account_id_dialog.reject)

        ok_btn = QPushButton("确定")
        ok_btn.clicked.connect(account_id_dialog.accept)

        button_layout.addWidget(cancel_btn)
        button_layout.addWidget(ok_btn)
        button_layout.addStretch()

        main_layout.addLayout(button_layout)

        # 显示对话框并获取结果
        if account_id_dialog.exec_() != QDialog.Accepted:
            info("用户取消添加账号")
            return

        # 获取用户输入
        account_id = account_id_input.text().strip()
        remark = remark_input.text().strip()

        # 验证账号ID是否为空
        if not account_id:
            warning("添加账号失败: 账号ID不能为空")
            QMessageBox.warning(self, "添加失败", "账号ID不能为空")
            return

        # 如果没有填写备注，则使用账号ID作为备注
        if not remark:
            remark = account_id

        # 以账号ID作为文件名
        cookie_filename = f"{account_id}.txt"
        cookie_filepath = os.path.join(self.cookie_path, cookie_filename)

        debug(f"将使用文件路径保存Cookie: {cookie_filepath}")

        def on_browser_finished(result):
            success_flag, message, saved_path = result
            if success_flag:
                success(f"成功添加账号，Cookie已保存到: {saved_path}")

                # 立即添加到表格，确保实时显示
                info(f"开始实时添加账号到表格: {saved_path}")

                # 只添加新账号到列表，而不是重新加载所有账号
                if saved_path not in self.cookie_files:
                    info(f"文件不在列表中，添加到表格: {saved_path}")
                    self.cookie_files.append(saved_path)

                    # 增加表格行数
                    current_row_count = self.table.rowCount()
                    self.table.setRowCount(current_row_count + 1)

                    # 添加新账号到表格的最后一行
                    new_row = current_row_count

                    # 获取文件名（不含路径）
                    file_name = os.path.basename(saved_path)
                    account_id = os.path.splitext(file_name)[0]  # 移除.txt后缀作为账号ID

                    # 尝试读取cookie文件内容
                    try:
                        with open(saved_path, 'r', encoding='utf-8') as f:
                            cookie_data = json.load(f)
                            if "remark" in cookie_data and cookie_data["remark"]:
                                remark = cookie_data["remark"]
                            else:
                                remark = account_id
                    except Exception as e:
                        # 静默处理读取错误
                        remark = account_id

                    # 昵称列设置为备注（如果有）或空
                    item = QTableWidgetItem(remark)
                    item.setTextAlignment(Qt.AlignCenter)
                    self.table.setItem(new_row, 0, item)

                    # 在第二列(账号列)显示不带后缀的用户名
                    item = QTableWidgetItem(account_id)
                    item.setTextAlignment(Qt.AlignCenter)
                    # 设置账号列为可编辑
                    item.setFlags(item.flags() | Qt.ItemIsEditable)
                    self.table.setItem(new_row, 1, item)

                    # 设置状态为"待验证"
                    status_item = QTableWidgetItem("待验证")
                    status_item.setTextAlignment(Qt.AlignCenter)
                    status_item.setBackground(QColor(255, 230, 200)) # 浅橙色背景
                    status_item.setForeground(QColor(153, 76, 0))    # 深橙色文字
                    status_item.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
                    self.table.setItem(new_row, 2, status_item)

                    # 添加操作按钮
                    self.add_operation_buttons(new_row)

                    # 查找并加载账号的保存数据
                    self.update_account_data_in_table(new_row, account_id)

                    # 发送cookie数量变化信号
                    self.cookie_count_changed.emit(len(self.cookie_files))

                    # 强制刷新表格 - 确保实时显示
                    self.table.viewport().update()
                    self.table.repaint()
                    QApplication.processEvents()  # 确保UI立即更新

                    # 滚动到新添加的行
                    if self.table.item(new_row, 0):
                        self.table.scrollToItem(self.table.item(new_row, 0))
                        self.table.selectRow(new_row)

                    success(f"✅ 账号已实时添加到表格第 {new_row + 1} 行")

                    # 在后台异步执行自动加密，不影响UI显示
                    try:
                        from app.utils.auto_cookie_encryption import auto_encrypt_new_cookie_file

                        # 从文件路径提取账号ID
                        account_id_from_path = os.path.splitext(os.path.basename(saved_path))[0]

                        # 异步加密
                        encrypt_success, encrypt_message = auto_encrypt_new_cookie_file(
                            saved_path,
                            account_id_from_path,
                            remark
                        )

                        if encrypt_success:
                            debug(f"Cookie文件已自动加密: {saved_path}")
                        else:
                            debug(f"Cookie文件加密失败: {encrypt_message}")

                    except Exception as encrypt_error:
                        warning(f"后台加密Cookie文件时出错: {str(encrypt_error)}")
                else:
                    # 如果文件已存在，只更新对应行的数据
                    info(f"文件已存在于列表中，更新对应行: {saved_path}")
                    for row in range(self.table.rowCount()):
                        account_id_item = self.table.item(row, 1)
                        if account_id_item and account_id_item.text() == os.path.splitext(os.path.basename(saved_path))[0]:
                            self.update_account_data_in_table(row, os.path.splitext(os.path.basename(saved_path))[0])
                            success(f"✅ 已更新现有账号行: {row + 1}")
                            break
            else:
                warning(f"添加账号失败: {message}")
                QMessageBox.warning(self, "添加失败", message)

        def on_browser_error(error_message):
            error(f"添加账号过程中出错: {error_message}")
            QMessageBox.critical(self, "添加错误", f"添加账号过程中出错: {error_message}")

        try:
            info(f"启动浏览器以添加新账号: {account_id}, 备注: {remark}")

            # 简化方案：直接在后台线程中执行，然后通过定时器检查文件
            def simple_add_account():
                """简化的添加账号函数"""
                try:
                    success_flag, message, saved_path = open_browser_and_save_cookie(
                        account_id, remark, self.cookie_path
                    )

                    if success_flag:
                        info(f"✅ 账号添加成功: {saved_path}")
                        # 设置标志，让定时器检查并更新UI
                        self._pending_new_account = {
                            'path': saved_path,
                            'account_id': account_id,
                            'remark': remark,
                            'timestamp': time.time()
                        }
                    else:
                        warning(f"❌ 账号添加失败: {message}")

                except Exception as e:
                    error(f"添加账号时出错: {str(e)}")

            # 启动后台任务
            import threading
            task_thread = threading.Thread(target=simple_add_account, daemon=True)
            task_thread.start()

            # 启动定时器检查新账号
            if not hasattr(self, '_check_timer'):
                from PyQt5.QtCore import QTimer
                self._check_timer = QTimer()
                self._check_timer.timeout.connect(self._check_for_new_account)
                self._check_timer.start(1000)  # 每秒检查一次

        except Exception as e:
            error_msg = f"启动添加账号线程时出错: {str(e)}"
            error(error_msg)
            QMessageBox.critical(self, "线程错误", error_msg)

    def _check_for_new_account(self):
        """检查是否有新账号需要添加到界面"""
        if hasattr(self, '_pending_new_account') and self._pending_new_account:
            account_info = self._pending_new_account
            self._pending_new_account = None  # 清除标志

            saved_path = account_info['path']
            account_id = account_info['account_id']
            remark = account_info['remark']

            info(f"🔄 检测到新账号，开始添加到界面: {account_id}")

            # 检查文件是否存在
            if os.path.exists(saved_path):
                # 自动加密新添加的Cookie文件
                try:
                    from app.utils.auto_cookie_encryption import auto_encrypt_new_cookie_file
                    encrypt_success, encrypt_message = auto_encrypt_new_cookie_file(
                        saved_path, account_id, remark
                    )
                    if encrypt_success:
                        debug(f"Cookie文件已自动加密: {saved_path}")
                except Exception as encrypt_error:
                    warning(f"自动加密Cookie文件时出错: {str(encrypt_error)}")

                # 添加到界面
                if saved_path not in self.cookie_files:
                    self.cookie_files.append(saved_path)

                    # 增加表格行数
                    current_row_count = self.table.rowCount()
                    self.table.setRowCount(current_row_count + 1)
                    new_row = current_row_count

                    # 设置表格内容
                    # 昵称列
                    item = QTableWidgetItem(remark)
                    item.setTextAlignment(Qt.AlignCenter)
                    self.table.setItem(new_row, 0, item)

                    # 账号列
                    item = QTableWidgetItem(account_id)
                    item.setTextAlignment(Qt.AlignCenter)
                    item.setFlags(item.flags() | Qt.ItemIsEditable)
                    self.table.setItem(new_row, 1, item)

                    # 状态列
                    status_item = QTableWidgetItem("待验证")
                    status_item.setTextAlignment(Qt.AlignCenter)
                    status_item.setBackground(QColor(255, 230, 200))
                    status_item.setForeground(QColor(153, 76, 0))
                    status_item.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
                    self.table.setItem(new_row, 2, status_item)

                    # 添加操作按钮
                    self.add_operation_buttons(new_row)

                    # 更新账号数据
                    self.update_account_data_in_table(new_row, account_id)

                    # 发送数量变化信号
                    self.cookie_count_changed.emit(len(self.cookie_files))

                    # 强制刷新表格
                    self.table.viewport().update()
                    self.table.repaint()
                    QApplication.processEvents()

                    # 滚动到新行并选中
                    if self.table.item(new_row, 0):
                        self.table.scrollToItem(self.table.item(new_row, 0))
                        self.table.selectRow(new_row)

                    success(f"✅ 新账号已成功添加到表格第 {new_row + 1} 行: {account_id}")

                    # 停止定时器
                    if hasattr(self, '_check_timer'):
                        self._check_timer.stop()

    def show_layout_config_dialog(self):
        """显示九宫格布局配置对话框"""
        try:
            from app.dialogs.browser_layout_config_dialog import BrowserLayoutConfigDialog

            dialog = BrowserLayoutConfigDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                info("九宫格布局配置已更新")

        except Exception as e:
            error(f"显示九宫格布局配置对话框时出错: {str(e)}")
            QMessageBox.warning(self, "错误", f"显示配置对话框失败: {str(e)}")

    def hideEvent(self, event):
        """当标签页隐藏时调用"""
        # 不再在标签页切换时停止线程，避免意外停止批量存稿任务
        # 只在应用程序关闭时才会停止线程
        super().hideEvent(event)

    def stop_threads(self):
        """终止所有线程"""
        try:
            info("正在停止所有活动线程...")
            self.status_update_signal.emit("🛑 正在停止所有活动线程...")

            # 复制线程列表，因为在循环中可能会修改原列表
            threads = self.active_threads.copy()
            stopped_count = 0

            # 逐个终止活动线程
            for thread, worker in threads:
                try:
                    # 尝试停止工作线程
                    if worker and hasattr(worker, 'abort'):
                        worker.abort()
                    elif worker and hasattr(worker, 'stop') and callable(worker.stop):
                        worker.stop()

                    # 尝试停止线程
                    if thread and thread.isRunning():
                        thread.quit()
                        # 等待线程结束，但最多等待1000毫秒
                        if not thread.wait(1000):
                            # 如果线程仍在运行，强制终止
                            thread.terminate()
                            warning(f"强制终止线程: {thread}")

                        stopped_count += 1
                except Exception as e:
                    error(f"停止线程时出错: {str(e)}")

            # 停止所有单账号存稿线程
            if hasattr(self, 'active_cunggao_tasks') and self.active_cunggao_tasks:
                try:
                    info(f"正在停止 {len(self.active_cunggao_tasks)} 个单账号存稿任务...")

                    # 复制字典键，因为我们会在循环中修改字典
                    account_ids = list(self.active_cunggao_tasks.keys())

                    for account_id in account_ids:
                        try:
                            thread, worker = self.active_cunggao_tasks[account_id]

                            # 停止工作对象
                            if worker and hasattr(worker, 'stop') and callable(worker.stop):
                                worker.stop()

                            # 停止线程 - 使用更安全的机制
                            if thread and thread.isRunning():
                                info(f"正在停止账号 {account_id} 的存稿线程...")
                                thread.quit()

                                # 等待线程优雅退出，最多等待3秒
                                if not thread.wait(3000):
                                    warning(f"账号 {account_id} 的线程未在3秒内停止，尝试强制终止...")
                                    thread.terminate()
                                    # 等待终止完成
                                    if not thread.wait(2000):
                                        error(f"账号 {account_id} 的线程强制终止失败")
                                    else:
                                        info(f"账号 {account_id} 的线程已强制终止")
                                else:
                                    info(f"账号 {account_id} 的线程已优雅停止")

                                stopped_count += 1

                                # 不调用deleteLater()，避免Qt Fatal错误
                                # thread.deleteLater()
                                info(f"账号 {account_id} 线程已停止，跳过deleteLater()调用")

                            # 从字典中移除
                            del self.active_cunggao_tasks[account_id]

                            info(f"账号 {account_id} 的存稿任务已停止")
                        except Exception as e:
                            error(f"停止账号 {account_id} 的存稿任务时出错: {str(e)}")

                    # 清空字典
                    self.active_cunggao_tasks.clear()
                    info("所有单账号存稿任务已停止")
                except Exception as e:
                    error(f"停止单账号存稿任务时出错: {str(e)}")

            # 停止批量存稿线程
            if hasattr(self, 'batch_worker') and self.batch_worker:
                try:
                    info("正在停止批量存稿工作线程...")
                    if hasattr(self.batch_worker, 'stop') and callable(self.batch_worker.stop):
                        self.batch_worker.stop()
                        info("批量存稿工作线程已停止")
                except Exception as e:
                    error(f"停止批量存稿工作线程时出错: {str(e)}")

            if hasattr(self, 'batch_thread') and self.batch_thread:
                try:
                    info("正在停止批量存稿线程...")
                    if self.batch_thread.isRunning():
                        self.batch_thread.quit()
                        if not self.batch_thread.wait(1000):  # 等待1秒
                            self.batch_thread.terminate()
                            warning("强制终止批量存稿线程")
                        stopped_count += 1
                        info("批量存稿线程已停止")
                except Exception as e:
                    error(f"停止批量存稿线程时出错: {str(e)}")

            # 清理引用，避免内存泄漏
            self.cunggao_worker = None
            self.cunggao_thread = None
            self.batch_worker = None
            self.batch_thread = None

            info(f"成功停止了 {stopped_count} 个线程")
            self.status_update_signal.emit(f"✅ 成功停止了 {stopped_count} 个线程")

            # 清空线程列表
            self.active_threads = []
        except Exception as e:
            error(f"停止线程时出错: {str(e)}")
            self.status_update_signal.emit(f"❌ 停止线程时出错: {str(e)}")

    def search_accounts(self):
        """根据输入的搜索条件过滤账号表格，支持批量搜索"""
        # 获取搜索文本，并按行分割
        search_text = self.search_input.toPlainText().strip()

        # 如果搜索文本为空，显示所有账号
        if not search_text:
            for row in range(self.table.rowCount()):
                self.table.setRowHidden(row, False)
            return

        # 将搜索文本按行分割，每行作为一个搜索条件
        search_terms = [term.strip().lower() for term in search_text.split('\n') if term.strip()]

        # 如果没有有效的搜索条件，显示所有账号
        if not search_terms:
            for row in range(self.table.rowCount()):
                self.table.setRowHidden(row, False)
            return

        # 记录匹配的行数
        matched_rows = 0
        total_rows = self.table.rowCount()

        # 遍历所有行，检查是否匹配任一搜索条件
        for row in range(total_rows):
            match_found = False

            # 获取当前行的账号ID
            account_item = self.table.item(row, 1)  # 账号列
            if not account_item:
                self.table.setRowHidden(row, True)
                continue

            account_id = account_item.text().lower()

            # 检查是否匹配任一搜索条件
            for term in search_terms:
                # 检查前两列(昵称、账号)是否包含搜索文本
                for col in range(2):  # 只搜索前两列
                    item = self.table.item(row, col)
                    if item and term in item.text().lower():
                        match_found = True
                        break

                if match_found:
                    break

            # 根据是否匹配来显示或隐藏行
            self.table.setRowHidden(row, not match_found)

            if match_found:
                matched_rows += 1

        # 显示搜索结果信息
        info(f"批量搜索完成: 找到 {matched_rows}/{total_rows} 个匹配账号")

        # 如果有匹配的行，滚动到第一个匹配的行
        if matched_rows > 0:
            for row in range(total_rows):
                if not self.table.isRowHidden(row):
                    self.table.scrollToItem(self.table.item(row, 0))
                    break

    def clear_search(self):
        """清除搜索条件"""
        self.search_input.clear()
        for row in range(self.table.rowCount()):
            self.table.setRowHidden(row, False)

        # 显示清除搜索结果信息
        info(f"已清除搜索条件，显示全部 {self.table.rowCount()} 个账号")

        # 滚动到表格顶部
        if self.table.rowCount() > 0:
            self.table.scrollToItem(self.table.item(0, 0))

    def on_search_text_changed(self):
        """搜索文本变化时的处理 - 实现延迟自动搜索"""
        # 停止之前的定时器
        self.search_timer.stop()
        # 设置500ms延迟后执行搜索（比批量对话框稍长，因为是多行文本）
        self.search_timer.start(500)

    def batch_cunggao(self):
        """打开批量存稿对话框"""
        try:
            # 数据采集功能已移除，不再需要检查

            # 首先进行预防性清理，确保没有残留的对象
            self._force_cleanup_batch_objects()

            # 检查是否已有单账号存稿任务在运行
            if hasattr(self, 'cunggao_thread') and self.cunggao_thread and self.cunggao_thread.isRunning():
                warning("单账号存稿任务正在运行，无法同时启动批量存稿任务")
                QMessageBox.warning(self, "操作冲突", "单账号存稿任务正在运行，请等待当前任务完成后再试")
                return

            # 检查是否已有批量存稿任务在运行
            if self._is_batch_task_running():
                warning("已有批量存稿任务正在运行，无法同时启动多个批量存稿任务")
                QMessageBox.warning(self, "操作冲突", "已有批量存稿任务正在运行，请等待当前任务完成后再试")
                return
            else:
                # 如果没有任务在运行，进行预防性清理
                info("进行预防性清理，确保没有残留对象...")
                self._force_cleanup_batch_objects()

            # 额外检查工作器状态
            if hasattr(self, 'batch_worker') and self.batch_worker:
                # 检查工作器是否仍在运行
                if hasattr(self.batch_worker, 'is_running') and self.batch_worker.is_running:
                    warning("批量存稿工作器仍在运行，无法启动新任务")
                    QMessageBox.warning(self, "操作冲突", "批量存稿工作器仍在运行，请等待当前任务完成后再试")
                    return
                else:
                    # 如果工作器已停止但对象仍存在，强制清理
                    info("检测到已停止的批量存稿工作器，正在清理...")
                    self._force_cleanup_batch_objects()

            # 导入新的批量存稿设置对话框
            from app.dialogs.batch_settings_dialog import BatchSettingsDialog

            # 打开批量存稿设置对话框，传递账号表格和Cookie目录
            dialog = BatchSettingsDialog(self, account_table=self.table, cookie_dir=self.cookie_path)
            if dialog.exec_() == QDialog.Accepted:
                # 获取设置，包括选中的账号列表、封号模式、发布限制弹窗处理、超时设置和重试设置
                headless_mode, concurrent_count, draft_count, video_allocation, clean_cache, remove_popup, ban_mode, handle_publish_limit, selected_accounts, timeout_settings, retry_settings = dialog.get_settings()

                # 检查是否选择了账号
                if not selected_accounts:
                    QMessageBox.warning(self, "未选择账号", "请至少选择一个账号进行批量存稿操作")
                    return

                # 如果选择了调试模式（非无头模式），需要密码验证
                if not headless_mode:
                    # 导入密码验证对话框
                    from app.dialogs.password_dialog import PasswordDialog

                    # 创建并显示密码验证对话框
                    password_dialog = PasswordDialog(self, "批量存稿调试模式")
                    if password_dialog.exec_() != QDialog.Accepted:
                        info("用户取消了密码验证或密码错误，不启动批量存稿")
                        return

                # 启动批量存稿，传递选中的账号列表、封号模式、发布限制弹窗处理、超时设置和重试设置
                self._start_batch_cunggao(headless_mode, concurrent_count, draft_count, video_allocation, clean_cache, remove_popup, ban_mode, handle_publish_limit, selected_accounts, timeout_settings, retry_settings)
                success("批量存稿设置已确认，开始执行任务")

                # 更新按钮状态
                self.update_button_states(task_type='batch_cunggao', is_running=True)
            else:
                info("用户取消了批量存稿操作")
        except Exception as e:
            error(f"打开批量存稿对话框失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开批量存稿对话框失败: {str(e)}")

    # 视频处理功能已移除

    def open_toutiao_spider(self):
        """打开头条视频爬虫对话框"""
        try:
            # 打开头条视频爬虫对话框
            dialog = open_toutiao_spider_dialog(self)

            # 保存引用，避免被垃圾回收
            self.spider_dialog = dialog

            info("已打开头条视频爬虫对话框")
        except Exception as e:
            error(f"打开头条视频爬虫对话框失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开头条视频爬虫对话框失败: {str(e)}")

    def show_cache_manager(self):
        """显示缓存管理对话框"""
        try:
            from app.utils.cache_manager import get_cache_manager
            cache_manager = get_cache_manager()

            # 创建缓存管理对话框
            dialog = QDialog(self)
            dialog.setWindowTitle("浏览器缓存管理")
            dialog.setFixedSize(600, 400)

            layout = QVBoxLayout(dialog)

            # 缓存状态信息
            status_group = QGroupBox("缓存状态")
            status_layout = QVBoxLayout(status_group)

            cache_status = cache_manager.get_cache_status()
            status_text = QTextEdit()
            status_text.setReadOnly(True)
            status_text.setMaximumHeight(150)

            status_info = "当前缓存状态：\n\n"
            for key, value in cache_status.items():
                status_info += f"{key}: {value}\n"

            status_text.setPlainText(status_info)
            status_layout.addWidget(status_text)
            layout.addWidget(status_group)

            # 操作按钮
            button_group = QGroupBox("缓存操作")
            button_layout = QVBoxLayout(button_group)

            # 清理缓存按钮
            clear_cache_btn = QPushButton("清理所有缓存")
            clear_cache_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    border-radius: 5px;
                    padding: 10px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)
            clear_cache_btn.clicked.connect(lambda: self.clear_all_cache(status_text, cache_manager))
            button_layout.addWidget(clear_cache_btn)

            # 定期清理按钮
            periodic_clean_btn = QPushButton("定期清理缓存（推荐）")
            periodic_clean_btn.setStyleSheet("""
                QPushButton {
                    background-color: #f39c12;
                    color: white;
                    border-radius: 5px;
                    padding: 10px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #e67e22;
                }
            """)
            periodic_clean_btn.clicked.connect(lambda: self.periodic_clean_cache(status_text, cache_manager))
            button_layout.addWidget(periodic_clean_btn)

            # 刷新状态按钮
            refresh_btn = QPushButton("刷新状态")
            refresh_btn.setStyleSheet("""
                QPushButton {
                    background-color: #3498db;
                    color: white;
                    border-radius: 5px;
                    padding: 10px;
                    font-weight: bold;
                }
                QPushButton:hover {
                    background-color: #2980b9;
                }
            """)
            refresh_btn.clicked.connect(lambda: self.refresh_cache_status(status_text, cache_manager))
            button_layout.addWidget(refresh_btn)

            layout.addWidget(button_group)

            # 关闭按钮
            close_btn = QPushButton("关闭")
            close_btn.clicked.connect(dialog.accept)
            layout.addWidget(close_btn)

            dialog.exec_()

        except Exception as e:
            from app.utils.logger import error
            error(f"显示缓存管理器失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"缓存管理器启动失败: {str(e)}")

    def clear_all_cache(self, status_text, cache_manager):
        """清理所有缓存"""
        try:
            reply = QMessageBox.question(
                self,
                "确认清理",
                "确定要清理所有浏览器缓存吗？\n这将删除所有缓存文件，可能需要重新登录账号。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                success = cache_manager.clear_all_cache()
                if success:
                    QMessageBox.information(self, "成功", "所有缓存已清理完成！")
                    self.refresh_cache_status(status_text, cache_manager)
                else:
                    QMessageBox.warning(self, "警告", "缓存清理过程中出现问题，请查看日志")
        except Exception as e:
            from app.utils.logger import error
            error(f"清理缓存失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"清理缓存失败: {str(e)}")

    def periodic_clean_cache(self, status_text, cache_manager):
        """定期清理缓存"""
        try:
            cleaned = cache_manager.cleanup_cache(max_size_mb=500)  # 限制500MB
            if cleaned:
                QMessageBox.information(self, "成功", "定期清理完成！已清理超过限制的缓存文件。")
            else:
                QMessageBox.information(self, "信息", "缓存大小在正常范围内，无需清理。")
            self.refresh_cache_status(status_text, cache_manager)
        except Exception as e:
            from app.utils.logger import error
            error(f"定期清理缓存失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"定期清理失败: {str(e)}")

    def refresh_cache_status(self, status_text, cache_manager):
        """刷新缓存状态"""
        try:
            cache_status = cache_manager.get_cache_status()
            status_info = "当前缓存状态：\n\n"
            for key, value in cache_status.items():
                status_info += f"{key}: {value}\n"
            status_text.setPlainText(status_info)
        except Exception as e:
            from app.utils.logger import error
            error(f"刷新缓存状态失败: {str(e)}")
            status_text.setPlainText(f"刷新失败: {str(e)}")

    # 系统功能按钮方法（原底部状态栏功能）
    def clean_browser_cache(self):
        """清理浏览器缓存"""
        try:
            # 调用主窗口的清理缓存方法
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'clean_browser_cache'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'clean_browser_cache'):
                main_window.clean_browser_cache()
            else:
                QMessageBox.information(self, "提示", "清理缓存功能暂时不可用")
        except Exception as e:
            from app.utils.logger import error
            error(f"清理浏览器缓存失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"清理缓存失败: {str(e)}")

    def kill_browser_processes(self):
        """关闭浏览器进程"""
        try:
            # 调用主窗口的关闭浏览器进程方法
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'kill_browser_processes'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'kill_browser_processes'):
                main_window.kill_browser_processes()
            else:
                QMessageBox.information(self, "提示", "关闭浏览器进程功能暂时不可用")
        except Exception as e:
            from app.utils.logger import error
            error(f"关闭浏览器进程失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"关闭浏览器进程失败: {str(e)}")

    def open_account_nurture(self):
        """打开账号养号功能"""
        try:
            # 调用主窗口的账号养号方法
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'open_account_nurture'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'open_account_nurture'):
                main_window.open_account_nurture()
            else:
                QMessageBox.information(self, "提示", "账号养号功能暂时不可用")
        except Exception as e:
            from app.utils.logger import error
            error(f"打开账号养号功能失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开账号养号失败: {str(e)}")

    def open_video_processor(self):
        """打开视频处理功能"""
        try:
            # 调用主窗口的视频处理方法
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'open_video_processor'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'open_video_processor'):
                main_window.open_video_processor()
            else:
                QMessageBox.information(self, "提示", "视频处理功能暂时不可用")
        except Exception as e:
            from app.utils.logger import error
            error(f"打开视频处理功能失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开视频处理失败: {str(e)}")

    def open_proxy_settings(self):
        """打开代理设置"""
        try:
            # 调用主窗口的代理设置方法
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'open_proxy_settings'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'open_proxy_settings'):
                main_window.open_proxy_settings()
            else:
                QMessageBox.information(self, "提示", "代理设置功能暂时不可用")
        except Exception as e:
            from app.utils.logger import error
            error(f"打开代理设置失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开代理设置失败: {str(e)}")

    def open_video_downloader(self):
        """打开视频下载工具"""
        try:
            # 调用主窗口的视频下载工具方法
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'open_video_downloader'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'open_video_downloader'):
                main_window.open_video_downloader()
            else:
                QMessageBox.information(self, "提示", "视频下载工具功能暂时不可用")
        except Exception as e:
            from app.utils.logger import error
            error(f"打开视频下载工具失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开视频下载工具失败: {str(e)}")

    def open_material_selection(self):
        """打开素材选择工具"""
        try:
            # 调用主窗口的素材选择方法
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'open_material_selection'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'open_material_selection'):
                main_window.open_material_selection()
            else:
                QMessageBox.information(self, "提示", "素材选择功能暂时不可用")
        except Exception as e:
            from app.utils.logger import error
            error(f"打开素材选择失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开素材选择失败: {str(e)}")

    def open_software_tutorial(self):
        """打开软件使用教程"""
        try:
            # 调用主窗口的软件教程方法
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'open_software_tutorial'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'open_software_tutorial'):
                main_window.open_software_tutorial()
            else:
                QMessageBox.information(self, "提示", "软件教程功能暂时不可用")
        except Exception as e:
            from app.utils.logger import error
            error(f"打开软件教程失败: {str(e)}")
            QMessageBox.critical(self, "错误", f"打开软件教程失败: {str(e)}")

    def batch_collect_account_data(self):
        """批量采集所有账号数据，不需要选择账号，直接处理所有账号"""
        info("启动批量采集功能，将处理所有账号")

        # 获取表格中的所有行
        total_rows = self.table.rowCount()
        if total_rows == 0:
            QMessageBox.warning(self, "批量采集失败", "账号列表为空，请先加载账号")
            return

        # 创建所有行的索引列表
        rows_to_process = list(range(total_rows))
        info(f"批量采集将处理 {len(rows_to_process)} 个账号")

        # 调用采集方法，传入所有行索引
        self.collect_account_data(rows=rows_to_process)

    def collect_account_data(self, row=None, rows=None):
        """采集账号数据

        Args:
            row: 行索引，如果为None则根据选择采集账号
            rows: 行索引列表，如果提供则直接处理这些行
        """
        # 如果直接提供了行索引列表，则使用它
        if rows is not None:
            info(f"使用直接提供的行索引列表，数量: {len(rows)}")
            rows_to_process = rows
        # 如果指定了单个行索引，则只处理该行
        elif row is not None:
            # 验证行索引有效性
            info(f"指定了特定行: {row}")
            if row < 0 or row >= len(self.cookie_files):
                warning(f"执行数据采集时行索引无效: {row}")
                QMessageBox.warning(self, "采集失败", "无效的账号索引")
                return
            rows_to_process = [row]
        # 否则，根据用户选择决定要处理的行
        else:
            selected_rows = self.table.selectionModel().selectedRows()
            info(f"当前选中的行数: {len(selected_rows)}")

            if not selected_rows:
                # 如果没有选中行，直接采集所有账号（不再询问）
                info("没有选中任何行，默认采集所有账号")
                total_rows = self.table.rowCount()
                info(f"准备采集所有账号，总行数: {total_rows}")
                rows_to_process = list(range(total_rows))
            else:
                # 如果有选中行，询问是否采集所有账号
                info("用户选中了特定行，询问是否改为采集所有账号")
                reply = QMessageBox.question(
                    self,
                    "确认采集",
                    f"您已选中 {len(selected_rows)} 个账号，是否改为采集所有账号数据？",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.Yes  # 默认选择"是"
                )
                if reply == QMessageBox.Yes:
                    total_rows = self.table.rowCount()
                    info(f"用户选择采集所有账号，总行数: {total_rows}")
                    rows_to_process = list(range(total_rows))
                else:
                    info(f"用户选择只采集选中的账号，数量: {len(selected_rows)}")
                    rows_to_process = [index.row() for index in selected_rows]
                    info(f"选中的行索引: {rows_to_process}")

        # 检查是否有账号可供采集
        if not rows_to_process:
            warning("没有账号可供采集")
            QMessageBox.warning(self, "采集失败", "没有账号可供采集")
            return

        info(f"准备处理的行数: {len(rows_to_process)}")
        # 打印前10个行索引，如果超过10个则显示省略号
        if len(rows_to_process) <= 10:
            info(f"行索引: {rows_to_process}")
        else:
            info(f"行索引(前10个): {rows_to_process[:10]}... 等共{len(rows_to_process)}个")

        # 导入设置对话框
        try:
            from app.dialogs.data_collection_settings_dialog import DataCollectionSettingsDialog

            # 显示设置对话框
            settings_dialog = DataCollectionSettingsDialog(self)
            if settings_dialog.exec_() != QDialog.Accepted:
                info("用户取消了数据采集操作")
                return

            # 获取设置
            _, thread_count, clean_cache = settings_dialog.get_settings()
            # 注意：无头模式已在数据采集器中强制启用，这里忽略设置对话框中的值

            # 确保线程数至少为2，以便测试并发功能
            thread_count = max(2, thread_count)

            info(f"数据采集设置: 线程数={thread_count}, 清理缓存={clean_cache}")
        except Exception as e:
            error_msg = f"打开数据采集设置对话框时出错: {str(e)}"
            error(error_msg)
            QMessageBox.critical(self, "设置错误", error_msg)
            return

        # 导入数据采集器
        try:
            from app.utils.toutiao_data_collector import ToutiaoDataCollector

            # 创建数据采集器实例
            info(f"创建数据采集器，线程数: {thread_count}")
            collector = ToutiaoDataCollector(
                self.table,
                cookie_dir=self.cookie_path,
                data_dir=self.data_dir,
                thread_count=thread_count
            )

            # 确保设置了正确的线程数
            collector.set_thread_count(thread_count)
            info(f"已设置数据采集器线程数: {thread_count}")

            # 不再创建进度对话框，改为在后台静默运行
            # 创建一些必要的属性，以便状态更新函数可以正常工作
            self.overall_progress_bar = None
            self.current_account_label = None
            self.status_label = None
            self.stats_label = None
            self.progress_dialog = None

            # 在状态栏显示采集开始的消息
            self.window().statusBar().showMessage("数据采集已在后台启动，请等待完成通知...")

            # 连接信号
            collector.status_update_signal.connect(self.on_data_collection_status_update)
            collector.data_updated_signal.connect(self.on_data_collection_data_updated)
            collector.log_message_signal.connect(self.on_data_collection_log_message)
            collector.error_message_signal.connect(self.on_data_collection_error_message)
            collector.all_threads_finished_signal.connect(self.on_data_collection_all_finished)

            # 保存采集器引用
            self.data_collector = collector

            # 启动数据采集
            info(f"开始启动数据采集，处理 {len(rows_to_process)} 个账号，使用 {thread_count} 个线程")
            collector.start_data_collection(rows_to_process)

            # 不再显示进度对话框，只在状态栏显示信息
            info(f"已启动数据采集，处理 {len(rows_to_process)} 个账号，使用 {thread_count} 个线程")

        except Exception as e:
            error_msg = f"启动数据采集时出错: {str(e)}"
            error(error_msg)

            # 显示错误消息
            QMessageBox.critical(self, "采集错误", error_msg)

    def _stop_data_collection(self, collector):
        """停止数据采集

        Args:
            collector: 数据采集器实例
        """
        try:
            reply = QMessageBox.question(
                self,
                "确认停止",
                "确定要停止数据采集吗？正在进行的任务将被中断。",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                info("用户请求停止数据采集")
                if collector:
                    collector.stop_all_threads()
                    self.status_label.setText("状态: 正在停止...")
        except Exception as e:
            error(f"停止数据采集时出错: {str(e)}")

    def on_data_collection_status_update(self, status_text):
        """处理数据采集状态更新

        Args:
            status_text: 状态文本
        """
        try:
            debug(f"数据采集状态更新: {status_text}")

            # 解析状态文本
            if status_text.startswith("更新浏览器进度:"):
                # 格式: "更新浏览器进度:线程名:账号:进度百分比"
                parts = status_text.split(":", 3)
                if len(parts) >= 4:
                    thread_name = parts[1]
                    account_name = parts[2]
                    progress = float(parts[3])
                    debug(f"浏览器进度更新: 线程={thread_name}, 账号={account_name}, 进度={progress}%")

                    # 更新进度对话框中的当前账号信息
                    if hasattr(self, 'current_account_label') and self.current_account_label is not None:
                        self.current_account_label.setText(f"当前账号: {account_name} ({thread_name})")

            elif status_text.startswith("更新总体进度:"):
                # 格式: "更新总体进度:账号:已完成数:总数:状态描述"
                parts = status_text.split(":", 4)
                if len(parts) >= 5:
                    account_name = parts[1]
                    completed = int(parts[2])
                    total = int(parts[3])
                    status_desc = parts[4]
                    debug(f"总体进度更新: 账号={account_name}, 进度={completed}/{total}, 状态={status_desc}")

                    # 更新进度对话框中的状态信息
                    if hasattr(self, 'status_label') and self.status_label is not None:
                        self.status_label.setText(f"状态: {status_desc}")

                    # 更新进度条
                    if hasattr(self, 'overall_progress_bar') and self.overall_progress_bar is not None and total > 0:
                        progress_percent = min(100, int((completed / total) * 100))
                        self.overall_progress_bar.setValue(progress_percent)

            elif status_text.startswith("更新已完成计数:"):
                # 格式: "更新已完成计数:已完成数:总数"
                parts = status_text.split(":", 2)
                if len(parts) >= 3:
                    completed = int(parts[1])
                    total = int(parts[2])
                    debug(f"已完成计数更新: {completed}/{total}")

                    # 更新进度对话框中的统计信息
                    if hasattr(self, 'stats_label') and self.stats_label is not None:
                        self.stats_label.setText(f"统计: {completed}/{total} 完成")

                    # 更新进度条
                    if hasattr(self, 'overall_progress_bar') and self.overall_progress_bar is not None and total > 0:
                        progress_percent = min(100, int((completed / total) * 100))
                        self.overall_progress_bar.setValue(progress_percent)

                        # 更新窗口标题显示进度
                        if hasattr(self, 'progress_dialog') and self.progress_dialog is not None:
                            self.progress_dialog.setWindowTitle(f"数据采集进度 - {progress_percent}%")

            # 处理普通状态消息（以图标开头的消息）
            elif status_text.startswith(("✅", "⚠️", "❌", "ℹ️")):
                # 更新进度对话框中的状态信息
                if hasattr(self, 'status_label') and self.status_label is not None:
                    self.status_label.setText(f"状态: {status_text}")

        except Exception as e:
            error(f"处理数据采集状态更新时出错: {str(e)}")
            # 添加更详细的调试信息
            import traceback
            debug(f"状态更新错误详情: {traceback.format_exc()}")
            debug(f"状态文本: {status_text}")
            debug(f"当前对象属性: current_account_label={hasattr(self, 'current_account_label')}, "
                  f"status_label={hasattr(self, 'status_label')}, "
                  f"overall_progress_bar={hasattr(self, 'overall_progress_bar')}, "
                  f"stats_label={hasattr(self, 'stats_label')}, "
                  f"progress_dialog={hasattr(self, 'progress_dialog')}")

    def on_data_collection_data_updated(self, data_text):
        """处理数据采集数据更新

        Args:
            data_text: 数据文本
        """
        try:
            debug(f"数据采集数据更新: {data_text}")
        except Exception as e:
            error(f"处理数据采集数据更新时出错: {str(e)}")

    def on_data_collection_log_message(self, message, level):
        """处理数据采集日志消息

        Args:
            message: 日志消息
            level: 日志级别
        """
        try:
            if level == "INFO":
                info(f"数据采集: {message}")
            elif level == "WARNING":
                warning(f"数据采集: {message}")
            elif level == "ERROR":
                error(f"数据采集: {message}")
            elif level == "SUCCESS":
                success(f"数据采集: {message}")
            else:
                debug(f"数据采集: {message}")
        except Exception as e:
            error(f"处理数据采集日志消息时出错: {str(e)}")

    def on_data_collection_error_message(self, title, message):
        """处理数据采集错误消息

        Args:
            title: 错误标题
            message: 错误消息
        """
        try:
            error(f"数据采集错误: {title} - {message}")
            # 不显示弹窗，避免干扰用户
        except Exception as e:
            error(f"处理数据采集错误消息时出错: {str(e)}")

    def on_data_collection_all_finished(self):
        """处理数据采集全部完成事件"""
        try:
            info("数据采集全部完成")

            # 获取成功和失败的账号数量
            success_count = 0
            failed_count = 0
            if hasattr(self, 'data_collector'):
                success_count = getattr(self.data_collector, 'success_accounts_count', 0)
                failed_count = getattr(self.data_collector, 'failed_accounts_count', 0)

                # 获取失败的账号列表
                failed_accounts = getattr(self.data_collector, 'failed_accounts', set())
                failed_accounts_str = ", ".join(failed_accounts) if failed_accounts else "无"

            # 不再更新进度对话框，因为它已经被移除
            # 在状态栏显示完成消息
            completion_message = f"数据采集已完成！成功: {success_count} 账号，失败: {failed_count} 账号"
            if failed_count > 0:
                completion_message += f"，失败账号: {failed_accounts_str}"

            # 更新状态栏
            self.window().statusBar().showMessage(completion_message, 10000)  # 显示10秒

            # 显示完成消息
            QMessageBox.information(
                self,
                "采集完成",
                f"数据采集已完成！\n\n成功: {success_count} 账号\n失败: {failed_count} 账号" +
                (f"\n\n失败账号: {failed_accounts_str}" if failed_count > 0 else "")
            )

            # 数据采集完成后，自动统计七天收益和昨日收益
            info("开始自动统计七天收益和昨日收益...")
            try:
                self.update_total_earnings()
                info("✅ 收益统计已完成并更新到主界面顶部")
            except Exception as earnings_error:
                error(f"自动统计收益时出错: {str(earnings_error)}")

            # 清理资源
            if hasattr(self, 'data_collector'):
                self.data_collector = None

        except Exception as e:
            error(f"处理数据采集全部完成事件时出错: {str(e)}")

    def on_detailed_status_update(self, *args, **kwargs):
        """处理详细状态更新信号 - 已移除"""
        pass

    def on_data_collection_finished(self):
        """数据采集完成的处理 - 已移除"""
        pass

    def load_saved_accounts_data(self):
        """加载保存的账号数据，在启动时自动调用 - 优化支持100000个账号（增强版本，防止闪退）"""
        try:
            # 检查是否已经加载过，避免重复加载（除非是强制刷新）
            if hasattr(self, 'data_loaded_once') and self.data_loaded_once:
                # 如果不是强制刷新，则跳过
                if not (hasattr(self, 'is_force_refreshing') and self.is_force_refreshing):
                    info("账号数据已加载过，跳过重复加载")
                    return

            # 检查表格是否已初始化
            if not hasattr(self, 'table') or not self.table:
                warning("表格未初始化，无法加载数据")
                return

            import time
            import gc

            # 记录开始时间
            start_time = time.time()

            # 设置已加载标志
            self.data_loaded_once = True

            # 使用统一的数据目录获取方法
            data_dir = self.get_data_directory()
            json_file_path = os.path.join(data_dir, "accounts_data.json")
            info(f"使用数据目录: {data_dir}")

            # 检查文件是否存在
            if not os.path.exists(json_file_path):
                info(f"未找到保存的账号数据文件: {json_file_path}")
                return

            # 检查文件大小
            file_size = os.path.getsize(json_file_path)
            file_size_mb = file_size / (1024 * 1024)
            info(f"账号数据文件大小: {file_size_mb:.2f} MB")

            # 如果文件很大，使用流式加载
            if file_size_mb > 100:  # 超过100MB使用流式加载
                info("检测到大型数据文件，启用流式加载模式")
                self.load_large_accounts_data(json_file_path)
                return

            # 加载JSON数据
            with open(json_file_path, 'r', encoding='utf-8') as f:
                all_accounts_data = json.load(f)

            account_count = len(all_accounts_data)

            # 检查表格是否已有数据，如果没有则直接从保存的数据创建表格
            if self.table.rowCount() == 0:
                info("表格为空，直接从保存的数据创建表格")
                self.create_table_from_saved_data(all_accounts_data)
                return

            # 更新表格数据
            updated_count = 0
            for row in range(self.table.rowCount()):
                # 获取账号ID
                account_id_item = self.table.item(row, 1)
                if not account_id_item:
                    continue

                account_id = account_id_item.text().strip()
                if not account_id or account_id not in all_accounts_data:
                    continue

                # 获取账号数据
                account_data = all_accounts_data[account_id]

                # 更新表格数据
                field_to_column = {
                    "username": 0,
                    "status": 2,
                    "credit_score": 3,
                    "draft_count": 4,
                    "yesterday_fans": 5,
                    "total_fans": 6,
                    "total_play_count": 7,
                    "yesterday_play": 8,
                    "total_income": 9,
                    "yesterday_income": 10,
                    "seven_days_total": 11,
                    "withdrawable_amount": 12,
                    "register_days": 13,
                    "verification_status": 14,
                    "total_withdraw": 15,
                    "withdraw_date": 16,
                    "recent_withdraw": 17
                }

                for field, column in field_to_column.items():
                    if field in account_data and account_data[field]:
                        # 创建表格项
                        item = QTableWidgetItem(account_data[field])
                        item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)

                        # 设置特殊颜色
                        if field == "verification_status":
                            if account_data[field] == "已实名":
                                item.setForeground(QBrush(QColor(0, 150, 0)))  # 绿色表示已实名
                            else:
                                item.setForeground(QBrush(QColor(200, 0, 0)))  # 红色表示未实名

                        # 更新表格项
                        self.table.setItem(row, column, item)

                updated_count += 1

            # 移除更新计数日志

            # 刷新表格
            self.table.viewport().update()

            # 移除耗时日志

            # 执行垃圾回收
            gc.collect()

        except Exception as e:
            error(f"加载保存的账号数据时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())

    def create_table_from_saved_data(self, all_accounts_data):
        """从保存的数据直接创建表格 - 支持100000个账号"""
        try:
            import time
            import gc

            start_time = time.time()
            account_count = len(all_accounts_data)
            info(f"开始从保存的数据创建表格，账号数量: {account_count}")

            # 暂时禁用表格更新，提高性能
            self.table.setUpdatesEnabled(False)
            self.table.setSortingEnabled(False)

            # 清空表格并设置行数
            self.table.clearContents()
            self.table.setRowCount(account_count)

            # 初始化cookie文件列表
            self.cookie_files = []

            # 批量处理账号数据
            batch_size = 1000  # 每批处理1000个账号
            processed_count = 0

            account_items = list(all_accounts_data.items())

            for i in range(0, len(account_items), batch_size):
                batch_items = account_items[i:i + batch_size]

                for j, (account_id, account_data) in enumerate(batch_items):
                    row = processed_count + j

                    # 添加到cookie文件列表（模拟文件路径）
                    cookie_file_path = f"{account_id}.txt"
                    self.cookie_files.append(cookie_file_path)

                    # 设置账号基本信息
                    # 昵称列
                    username = account_data.get('username', account_id)
                    item = QTableWidgetItem(username)
                    item.setTextAlignment(Qt.AlignCenter)
                    self.table.setItem(row, 0, item)

                    # 账号ID列
                    item = QTableWidgetItem(account_id)
                    item.setTextAlignment(Qt.AlignCenter)
                    # 设置账号列为可编辑
                    item.setFlags(item.flags() | Qt.ItemIsEditable)
                    self.table.setItem(row, 1, item)

                    # 状态列
                    status = account_data.get('status', '正常')
                    status_item = QTableWidgetItem(status)
                    status_item.setTextAlignment(Qt.AlignCenter)
                    status_item.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))

                    # 根据状态设置颜色
                    if status == "正常":
                        status_item.setBackground(QColor(200, 255, 200))  # 浅绿色
                        status_item.setForeground(QColor(0, 100, 0))      # 深绿色文字
                    elif status == "采集中" or status == "正在采集":
                        status_item.setBackground(QColor(200, 200, 255))  # 浅蓝色
                        status_item.setForeground(QColor(0, 0, 150))      # 深蓝色文字
                    elif status in ["采集失败", "登录失败"]:
                        status_item.setBackground(QColor(255, 200, 200))  # 浅红色
                        status_item.setForeground(QColor(180, 0, 0))      # 深红色文字

                    self.table.setItem(row, 2, status_item)

                    # 设置其他数据字段
                    field_to_column = {
                        "credit_score": 3,
                        "draft_count": 4,
                        "yesterday_fans": 5,
                        "total_fans": 6,
                        "total_play_count": 7,
                        "yesterday_play": 8,
                        "total_income": 9,
                        "yesterday_income": 10,
                        "seven_days_total": 11,
                        "withdrawable_amount": 12,
                        "register_days": 13,
                        "verification_status": 14,
                        "total_withdraw": 15,
                        "withdraw_date": 16,
                        "recent_withdraw": 17
                    }

                    for field, column in field_to_column.items():
                        if field in account_data and account_data[field]:
                            value = account_data[field]
                            item = QTableWidgetItem(str(value))
                            item.setTextAlignment(Qt.AlignCenter)

                            # 特殊处理实名状态
                            if field == "verification_status":
                                if value in ["已实名", "已认证"]:
                                    item.setForeground(QColor(0, 128, 0))  # 绿色
                                else:
                                    item.setForeground(QColor(255, 0, 0))  # 红色

                            self.table.setItem(row, column, item)

                    # 添加操作按钮
                    self.add_operation_buttons(row)

                processed_count += len(batch_items)

                # 每处理一批后执行垃圾回收和进度更新
                if processed_count % (batch_size * 5) == 0:
                    progress = (processed_count / account_count) * 100
                    info(f"已创建 {processed_count}/{account_count} 个账号行 ({progress:.1f}%)")
                    gc.collect()

                    # 短暂暂停，让出CPU时间
                    time.sleep(0.01)

            # 重新启用表格更新和排序
            self.table.setUpdatesEnabled(True)
            self.table.setSortingEnabled(True)

            # 刷新表格显示
            self.table.viewport().update()

            # 发送cookie数量变化信号
            self.cookie_count_changed.emit(len(self.cookie_files))

            # 记录完成时间
            end_time = time.time()
            load_time = end_time - start_time
            info(f"从保存数据创建表格完成，共创建 {account_count} 个账号行，耗时: {load_time:.2f}秒")

            # 执行最终垃圾回收
            gc.collect()

        except Exception as e:
            error(f"从保存数据创建表格时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())

    def load_large_accounts_data(self, json_file_path):
        """流式加载大型账号数据文件 - 支持100000个账号"""
        try:
            import json
            import time
            import gc

            info("开始流式加载大型账号数据文件...")
            start_time = time.time()

            # 使用流式JSON解析器
            with open(json_file_path, 'r', encoding='utf-8') as f:
                # 读取整个文件内容
                content = f.read()

            # 解析JSON数据
            all_accounts_data = json.loads(content)

            # 检查表格是否已有数据，如果没有则直接从保存的数据创建表格
            if self.table.rowCount() == 0:
                info("表格为空，使用流式方式从保存的数据创建表格")
                self.create_table_from_saved_data(all_accounts_data)
                return

            # 分批处理大小
            batch_size = 1000
            processed_count = 0
            updated_count = 0
            total_accounts = len(all_accounts_data)

            info(f"开始处理 {total_accounts} 个账号数据...")

            # 分批处理账号数据
            account_items = list(all_accounts_data.items())

            for i in range(0, len(account_items), batch_size):
                batch_items = account_items[i:i + batch_size]

                # 处理当前批次
                for account_id, account_data in batch_items:
                    # 查找对应的表格行
                    for row in range(self.table.rowCount()):
                        account_id_item = self.table.item(row, 1)
                        if account_id_item and account_id_item.text().strip() == account_id:
                            # 更新该行的数据
                            self.update_table_row_data(row, account_data)
                            updated_count += 1
                            break

                    processed_count += 1

                # 每处理一批数据后，更新进度并执行垃圾回收
                if processed_count % (batch_size * 5) == 0:
                    progress = (processed_count / total_accounts) * 100
                    info(f"已处理 {processed_count}/{total_accounts} 个账号 ({progress:.1f}%)")
                    gc.collect()

                    # 短暂暂停，让出CPU时间
                    time.sleep(0.01)

            # 刷新表格显示
            self.table.viewport().update()

            # 记录完成时间
            end_time = time.time()
            load_time = end_time - start_time
            info(f"大型账号数据流式加载完成，共处理 {processed_count} 个账号，更新 {updated_count} 行，耗时: {load_time:.2f}秒")

            # 执行最终垃圾回收
            gc.collect()

        except Exception as e:
            error(f"流式加载大型账号数据时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())

    def force_refresh_account_data(self):
        """强制刷新账号数据 - 确保所有账号数据都被正确加载（增强版本，防止闪退）"""
        try:
            # 检查是否正在进行其他加载操作
            if hasattr(self, 'is_loading') and self.is_loading:
                warning("账号正在加载中，无法进行强制刷新")
                QMessageBox.information(self, "提示", "账号正在加载中，请等待完成后再试")
                return

            # 检查是否正在强制刷新
            if hasattr(self, 'is_force_refreshing') and self.is_force_refreshing:
                warning("强制刷新正在进行中，请等待完成")
                QMessageBox.information(self, "提示", "强制刷新正在进行中，请等待完成")
                return

            # 设置强制刷新状态
            self.is_force_refreshing = True

            # 禁用强制刷新按钮，防止重复点击
            if hasattr(self, 'force_refresh_btn'):
                self.force_refresh_btn.setEnabled(False)
                self.force_refresh_btn.setText("刷新中...")

            info("开始强制刷新账号数据...")

            # 检查表格是否有数据
            if not hasattr(self, 'table') or not self.table or self.table.rowCount() == 0:
                info("表格为空或未初始化，跳过强制刷新")
                self._reset_force_refresh_state()
                return

            # 停止之前的加载任务，防止冲突
            self._stop_previous_loading_tasks()

            # 安全地重新加载保存的账号数据
            try:
                # 临时清除已加载标志，允许重新加载
                if hasattr(self, 'data_loaded_once'):
                    delattr(self, 'data_loaded_once')

                self.load_saved_accounts_data()
                info("保存的账号数据重新加载完成")
            except Exception as load_e:
                warning(f"重新加载保存数据时出错: {str(load_e)}")

            # 检查数据完整性
            empty_count = 0
            try:
                for row in range(self.table.rowCount()):
                    # 检查关键列是否为空
                    username_item = self.table.item(row, 0)
                    status_item = self.table.item(row, 2)

                    if not username_item or not username_item.text().strip():
                        empty_count += 1
                    elif not status_item or status_item.text().strip() in ["", "待验证"]:
                        empty_count += 1

                if empty_count > 0:
                    info(f"检测到 {empty_count} 个账号数据为空，尝试重新加载Cookie文件")
                    # 重新加载Cookie文件，使用完整加载模式
                    if hasattr(self, 'cookie_path') and self.cookie_path and os.path.exists(self.cookie_path):
                        # 使用异步方式重新加载，避免阻塞UI
                        QTimer.singleShot(500, lambda: self._reload_cookie_files_safely())
                    else:
                        warning("Cookie路径无效，无法重新加载文件")
                else:
                    info("所有账号数据完整，无需重新加载Cookie文件")

            except Exception as check_e:
                warning(f"检查数据完整性时出错: {str(check_e)}")

            # 强制刷新UI
            try:
                self.table.viewport().update()
                QApplication.processEvents()
                info("UI强制刷新完成")
            except Exception as ui_e:
                warning(f"UI刷新时出错: {str(ui_e)}")

            info("强制刷新账号数据完成")

        except Exception as e:
            error(f"强制刷新账号数据时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())
        finally:
            # 确保状态被重置
            self._reset_force_refresh_state()

    def _reset_force_refresh_state(self):
        """重置强制刷新状态"""
        try:
            # 重置刷新状态标志
            self.is_force_refreshing = False

            # 重新启用强制刷新按钮
            if hasattr(self, 'force_refresh_btn'):
                self.force_refresh_btn.setEnabled(True)
                self.force_refresh_btn.setText("🔄 强制刷新")

            info("强制刷新状态已重置")
        except Exception as e:
            error(f"重置强制刷新状态时出错: {str(e)}")

    def _reload_cookie_files_safely(self):
        """安全地重新加载Cookie文件"""
        try:
            if hasattr(self, 'cookie_path') and self.cookie_path and os.path.exists(self.cookie_path):
                info("开始安全重新加载Cookie文件...")

                # 检查是否正在加载
                if hasattr(self, 'is_loading') and self.is_loading:
                    warning("正在加载中，延迟重新加载Cookie文件")
                    QTimer.singleShot(2000, self._reload_cookie_files_safely)
                    return

                # 重新加载Cookie文件
                self.load_cookie_files(self.cookie_path, show_message=False, delay_data_loading=False)
                info("Cookie文件安全重新加载完成")
            else:
                warning("Cookie路径无效，无法重新加载")
        except Exception as e:
            error(f"安全重新加载Cookie文件时出错: {str(e)}")
        finally:
            # 确保强制刷新状态被重置
            self._reset_force_refresh_state()

    def update_table_row_data(self, row, account_data):
        """更新表格行数据 - 优化版本"""
        try:
            # 字段映射
            field_to_column = {
                "username": 0,
                "status": 2,
                "credit_score": 3,
                "draft_count": 4,
                "yesterday_fans": 5,
                "total_fans": 6,
                "yesterday_read": 7,
                "total_play_count": 8,
                "yesterday_play_count": 9,
                "total_income": 10,
                "yesterday_income": 11,
                "seven_days_income": 12,
                "pending_withdraw": 13,
                "register_days": 14,
                "is_verified": 15,
                "total_withdraw": 16,
                "last_withdraw_date": 17,
                "last_withdraw_amount": 18
            }

            # 更新各字段数据
            for field, column in field_to_column.items():
                if field in account_data and column < self.table.columnCount():
                    value = account_data[field]
                    if value is not None:
                        # 创建或更新表格项
                        item = self.table.item(row, column)
                        if item is None:
                            item = QTableWidgetItem(str(value))
                            self.table.setItem(row, column, item)
                        else:
                            item.setText(str(value))

        except Exception as e:
            debug(f"更新表格行 {row} 数据时出错: {str(e)}")

    def save_account_data_to_file(self, account_id, account_data):
        """保存单个账号数据到文件

        Args:
            account_id: 账号ID
            account_data: 账号数据字典
        """
        if not account_id:
            error("保存账号数据时账号ID为空")
            return

        if not account_data:
            debug(f"账号 {account_id} 没有数据可保存")
            return

        try:
            # 使用统一的数据目录获取方法
            data_dir = self.get_data_directory()
            os.makedirs(data_dir, exist_ok=True)

            # 保存数据文件
            data_path = os.path.join(data_dir, f"{account_id}.json")
            with open(data_path, 'w', encoding='utf-8') as f:
                json.dump(account_data, f, ensure_ascii=False, indent=4)

            debug(f"已保存账号 {account_id} 的数据到文件: {data_path}")
            return True

        except Exception as e:
            error(f"保存账号 {account_id} 数据到文件时出错: {str(e)}")
            return False

    def save_account_data(self):
        """保存所有账号数据到文件"""
        try:
            # 使用统一的数据目录获取方法
            data_dir = self.get_data_directory()
            os.makedirs(data_dir, exist_ok=True)

            # 遍历所有行，保存数据
            for row in range(self.table.rowCount()):
                try:
                    # 获取账号ID
                    account_id_item = self.table.item(row, 1)
                    if not account_id_item:
                        continue

                    account_id = account_id_item.text()

                    # 创建数据字典
                    account_data = {
                        "account_id": account_id,
                        "username": self.table.item(row, 0).text() if self.table.item(row, 0) else account_id,
                        "status": self.table.item(row, 2).text() if self.table.item(row, 2) else "正常"
                    }

                    # 添加其他数据
                    field_to_column = {
                        "credit_score": 4,
                        "draft_count": 5,
                        "yesterday_fans": 6,
                        "total_fans": 7,
                        "yesterday_read": 8,
                        "total_play_count": 9,
                        "yesterday_play_count": 10,
                        "total_income": 11,
                        "yesterday_income": 12,
                        "seven_days_income": 13,
                        "pending_withdraw": 14,
                        "register_days": 15,
                        "is_verified": 16,
                        "total_withdraw": 17,
                        "last_withdraw_date": 18,
                        "last_withdraw_amount": 19
                    }

                    for field, column in field_to_column.items():
                        item = self.table.item(row, column)
                        if item:
                            # 特殊处理实名状态字段
                            if field == "is_verified":
                                account_data[field] = "已认证" if item.text() == "已认证" else "未认证"
                            else:
                                account_data[field] = item.text()

                    # 保存数据文件
                    self.save_account_data_to_file(account_id, account_data)

                except Exception as e:
                    error(f"保存账号数据时出错: {str(e)}")
                    continue

            self.status_update_signal.emit("✅ 已保存所有账号数据")

        except Exception as e:
            error(f"保存账号数据时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())

    def _start_batch_cunggao(self, headless_mode, concurrent_count, draft_count, video_allocation, clean_cache, remove_popup, ban_mode, handle_publish_limit, selected_accounts, timeout_settings, retry_settings):
        """启动批量存稿任务

        Args:
            headless_mode: 是否启用无头模式
            concurrent_count: 并发数量
            draft_count: 存稿次数
            video_allocation: 是否启用视频公平分配
            clean_cache: 是否清理缓存
            remove_popup: 是否去除活动弹窗
            ban_mode: 是否启用封号模式（点击发布而不是存稿）
            handle_publish_limit: 是否自动处理发布限制弹窗
            selected_accounts: 选中的账号列表
            timeout_settings: 超时设置
            retry_settings: 重试设置
        """
        # 重置批量存稿完成处理标志
        self._batch_completion_processed = False

        info(f"启动批量存稿任务 - 无头模式: {headless_mode}, 并发数: {concurrent_count}, 存稿次数: {draft_count}, 视频公平分配: {video_allocation}, 清理缓存: {clean_cache}, 去除活动弹窗: {remove_popup}, 处理发布限制弹窗: {handle_publish_limit}")

        # 检查是否有正在运行的批量存稿任务
        if self._is_batch_task_running():
            warning("批量存稿失败: 已有正在运行的批量存稿任务")
            QMessageBox.warning(self, "操作失败", "已有批量存稿任务正在运行，请等待当前任务完成后再试")
            return
        else:
            # 如果没有任务在运行，进行预防性清理
            info("进行预防性清理，确保没有残留对象...")

            # 暂时禁用等待机制，避免主线程阻塞
            # self._wait_for_all_threads_completion()
            info("跳过线程等待，直接进行清理")

            self._cleanup_batch_cunggao_objects()

        # 已移除"本次存稿"列，不再需要重置所有账号的存稿次数

        # 确认是否有cookie目录
        if not hasattr(self, 'cookie_path') or not self.cookie_path or not os.path.exists(self.cookie_path):
            error_msg = "批量存稿失败: 未设置Cookie保存路径或路径不存在"
            error(error_msg)
            self.batch_log_updated.emit("批量存稿", "ERROR", error_msg)
            QMessageBox.warning(self, "操作失败", "请先在设置中指定Cookie文件保存位置")
            return

        # 确认账号列表不为空
        if len(self.cookie_files) == 0:
            error_msg = "批量存稿失败: 没有可用的账号"
            warning(error_msg)
            self.batch_log_updated.emit("批量存稿", "WARNING", error_msg)
            QMessageBox.warning(self, "操作失败", "没有找到可用的账号，请先加载账号")
            return

        # 从设置中获取视频路径和封面路径
        video_path = None
        cover_path = None

        try:
            # 获取应用设置
            settings_file = "settings.json"
            if os.path.exists(settings_file):
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    if "video_path" in settings and os.path.exists(settings["video_path"]):
                        video_path = settings["video_path"]
                        info(f"已从设置中获取视频目录: {video_path}")
                    if "cover_path" in settings and os.path.exists(settings["cover_path"]):
                        cover_path = settings["cover_path"]
                        info(f"已从设置中获取封面目录: {cover_path}")
        except Exception as e:
            warning(f"获取视频和封面路径时出错: {str(e)}")

        # 检查视频目录中是否有视频文件
        if video_path and os.path.exists(video_path):
            video_files = [f for f in os.listdir(video_path)
                         if f.lower().endswith(('.mp4', '.avi', '.mov', '.wmv', '.flv'))]
            if video_files:
                info_msg = f"找到 {len(video_files)} 个视频文件: {', '.join(video_files[:3])}..."
                info(info_msg)
                self.batch_log_updated.emit("批量存稿", "INFO", info_msg)
            else:
                error_msg = "批量存稿失败: 视频目录中没有找到视频文件"
                error(error_msg)
                self.batch_log_updated.emit("批量存稿", "ERROR", error_msg)
                QMessageBox.warning(self, "操作失败", f"视频目录 {video_path} 中没有找到视频文件(.mp4, .avi, .mov等)")
                return
        else:
            error_msg = "批量存稿失败: 视频目录未设置或不存在"
            error(error_msg)
            self.batch_log_updated.emit("批量存稿", "ERROR", error_msg)
            QMessageBox.warning(self, "操作失败", "请先在设置中指定有效的视频文件目录")
            return

        # 检查封面目录中是否有图片文件
        if cover_path and os.path.exists(cover_path):
            cover_files = [f for f in os.listdir(cover_path)
                          if f.lower().endswith(('.jpg', '.jpeg', '.png', '.webp', '.bmp'))]
            if cover_files:
                info_msg = f"找到 {len(cover_files)} 个封面文件: {', '.join(cover_files[:3])}..."
                info(info_msg)
                self.batch_log_updated.emit("批量存稿", "INFO", info_msg)
            else:
                error_msg = "批量存稿失败: 封面目录中没有找到图片文件"
                error(error_msg)
                self.batch_log_updated.emit("批量存稿", "ERROR", error_msg)
                QMessageBox.warning(self, "操作失败", f"封面目录 {cover_path} 中没有找到图片文件(.jpg, .png等)")
                return
        else:
            error_msg = "批量存稿失败: 封面目录未设置或不存在"
            error(error_msg)
            self.batch_log_updated.emit("批量存稿", "ERROR", error_msg)
            QMessageBox.warning(self, "操作失败", "请先在设置中指定有效的封面文件目录")
            return

        # 询问用户是否确认开始批量存稿
        selected_count = len(selected_accounts)
        account_names = [acc['account_name'] for acc in selected_accounts[:5]]  # 显示前5个账号名称
        account_display = ", ".join(account_names)
        if selected_count > 5:
            account_display += f" 等{selected_count}个账号"

        reply = QMessageBox.question(
            self,
            "确认操作",
            f"确定要开始批量存稿操作吗？\n" +
            f"选中账号: {account_display}\n" +
            f"账号数量: {selected_count} 个\n" +
            f"视频目录: {video_path} (已找到{len(video_files)}个视频文件)\n" +
            f"封面目录: {cover_path} (已找到{len(cover_files)}个封面文件)\n" +
            f"无头模式: {'启用' if headless_mode else '禁用'}\n" +
            f"并发数量: {concurrent_count}\n" +
            f"存稿次数: {draft_count}\n" +
            f"视频公平分配: {'启用' if video_allocation else '禁用'}\n" +
            f"清理缓存: {'启用' if clean_cache else '禁用'}",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.No:
            return

        try:
            # 获取指纹浏览器设置
            fingerprint_settings = self.get_fingerprint_settings()
            use_fingerprint = fingerprint_settings.get('use_fingerprint', False)

            # 创建并启动工作线程
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'statusBar'):
                main_window = main_window.parent()

            thread, worker = run_batch_cunggao(
                table=self.table,
                cookie_dir=self.cookie_path,
                video_dir=video_path,
                cover_dir=cover_path,
                parent=main_window,
                headless_mode=headless_mode,
                concurrent_count=concurrent_count,
                draft_count=draft_count,
                video_allocation=video_allocation,
                clean_cache_before_start=clean_cache,
                remove_popup=remove_popup,
                ban_mode=ban_mode,  # 添加封号模式参数
                handle_publish_limit=handle_publish_limit,  # 添加发布限制弹窗处理参数
                selected_accounts=selected_accounts,  # 添加选中账号参数 - 这是关键的修复
                account_tab_ref=self,  # 传递账号标签页引用
                use_fingerprint=use_fingerprint,  # 指纹浏览器开关
                fingerprint_settings=fingerprint_settings,  # 指纹浏览器设置
                timeout_settings=timeout_settings,  # 添加超时设置参数
                retry_settings=retry_settings  # 添加重试设置参数
            )

            # 保存线程和工作对象的引用，避免被垃圾回收
            # 同时保存到主窗口，确保UI刷新能找到这些线程
            self.batch_thread = thread
            self.batch_worker = worker

            # 如果找到主窗口，也在主窗口中保存引用
            if main_window:
                main_window._batch_draft_thread = thread
                main_window._batch_draft_worker = worker

                # 刷新主窗口UI刷新定时器，使用更合理的刷新频率
                if hasattr(main_window, 'ui_refresh_timer') and main_window.ui_refresh_timer:
                    main_window.ui_refresh_timer.setInterval(500)  # 设置为500ms刷新频率，减少系统负担
                    main_window.process_pending_events()  # 立即处理事件

            # 连接停止按钮
            if hasattr(self, 'stop_btn'):
                # 断开之前的所有连接
                try:
                    self.stop_btn.clicked.disconnect()
                except Exception:
                    pass
                # 连接新的停止函数
                self.stop_btn.clicked.connect(self.stop_all_tasks)

            # 系统日志功能已移除
            # 连接进度信号
            if hasattr(worker, 'status_signal'):
                worker.status_signal.connect(lambda msg: self.on_batch_status_update(msg))

            if hasattr(worker, 'progress_signal'):
                worker.progress_signal.connect(lambda current, total: self.on_batch_progress_update(current, total))
                # 批量存稿进度条功能已移除

            # 系统日志功能已移除

            if hasattr(worker, 'finished_signal'):
                worker.finished_signal.connect(lambda: self.on_batch_cunggao_finished())
                # 系统日志功能已移除

            # 连接运行时间更新信号
            if hasattr(worker, 'runtime_update_signal'):
                worker.runtime_update_signal.connect(self.on_runtime_update)

            # 批量存稿进度条功能已移除

            # 记录日志
            success("批量存稿任务已启动")

            # 发送详细的批量存稿开始日志到系统日志选项卡
            mode_text = "封号模式" if ban_mode else "存稿模式"
            self.batch_log_updated.emit("批量存稿", "SUCCESS", f"批量存稿任务已启动 ({mode_text})")
            self.batch_log_updated.emit("批量存稿", "INFO", f"选中账号数量: {len(selected_accounts)}")
            self.batch_log_updated.emit("批量存稿", "INFO", f"存稿次数: {draft_count}")
            self.batch_log_updated.emit("批量存稿", "INFO", f"并发线程数: {concurrent_count}")
            self.batch_log_updated.emit("批量存稿", "INFO", f"无头模式: {'是' if headless_mode else '否'}")
            self.batch_log_updated.emit("批量存稿", "INFO", f"操作模式: {mode_text}")

            # 显示重试设置信息
            if retry_settings and retry_settings.get('enable_retry', True):
                max_retry = retry_settings.get('max_retry_count', 2)
                retry_delay = retry_settings.get('retry_delay', 10)
                self.batch_log_updated.emit("批量存稿", "INFO", f"失败重试: 启用 (最大{max_retry}次, 间隔{retry_delay}秒)")
            else:
                self.batch_log_updated.emit("批量存稿", "INFO", f"失败重试: 禁用")

        except Exception as e:
            error_msg = f"启动批量存稿任务失败: {str(e)}"
            error(error_msg)
            # 发送错误日志到系统日志选项卡
            self.batch_log_updated.emit("批量存稿", "ERROR", error_msg)
            QMessageBox.critical(self, "操作失败", error_msg)

    # 公告功能已移至主界面顶部工具栏

    def update_button_states(self, task_type=None, is_running=False):
        """更新按钮状态，实现按钮互锁功能

        Args:
            task_type: 任务类型，可以是 'batch_cunggao'
            is_running: 任务是否正在运行
        """
        try:
            # 如果任务正在运行，禁用相应的按钮
            if is_running and task_type == 'batch_cunggao':
                # 更新批量存稿按钮样式，表示正在运行
                self.batch_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #004444;
                        color: #FFFFFF;
                        font-size: 15px;
                        font-weight: bold;
                        border: 2px solid #FFFF00;
                        border-radius: 6px;
                        padding: 8px 15px;
                    }
                """)
                self.batch_btn.setToolTip("批量存稿任务正在运行")
            else:
                # 恢复批量存稿按钮
                self.batch_btn.setEnabled(True)
                self.batch_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #006666;
                        color: #FFFFFF;
                        font-size: 15px;
                        font-weight: bold;
                        border: 2px solid #000000;
                        border-radius: 6px;
                        padding: 8px 15px;
                    }
                    QPushButton:hover {
                        background-color: #007777;
                        border: 2px solid #FFFF00;
                    }
                """)
                self.batch_btn.setToolTip("启动批量存稿任务")

            # 刷新UI
            QApplication.processEvents()

        except Exception as e:
            error(f"更新按钮状态时出错: {str(e)}")

    # 数据采集功能已移除

    def stop_all_tasks(self):
        """立即停止所有正在运行的任务 - 静态停止模式，无确认对话框"""
        try:
            info("开始静态停止所有任务（无确认对话框）")

            # 防止重复调用
            if hasattr(self, '_is_stopping_tasks') and self._is_stopping_tasks:
                info("任务停止正在进行中，跳过重复调用")
                return

            self._is_stopping_tasks = True

            # 发送状态更新信号
            self.status_update_signal.emit("🛑 正在停止所有任务...")

            # 直接执行停止操作，不显示对话框
            self._execute_static_stop()

        except Exception as e:
            error(f"停止所有任务时出错: {str(e)}")
            # 不显示错误对话框，只记录日志
            self.status_update_signal.emit(f"❌ 停止任务时出错: {str(e)}")
        finally:
            # 确保状态被重置
            self._is_stopping_tasks = False

    def _execute_static_stop(self):
        """执行静态停止操作"""
        try:
            info("开始执行静态停止操作")

            # 1. 停止批量存稿任务
            self._stop_batch_tasks()

            # 2. 停止数据采集任务
            self._stop_data_collection_tasks()

            # 3. 停止活动线程
            self._stop_active_threads()

            # 4. 清理浏览器进程（可选，根据需要）
            # self._cleanup_browser_processes()

            # 5. 清理资源
            self._cleanup_task_resources()

            info("静态停止操作完成")
            self.status_update_signal.emit("✅ 所有任务已停止")

        except Exception as e:
            error(f"执行静态停止操作时出错: {str(e)}")
            self.status_update_signal.emit(f"❌ 停止操作出错: {str(e)}")

    def _stop_batch_tasks(self):
        """停止批量任务"""
        try:
            # 停止批量存稿任务
            if hasattr(self, 'batch_worker') and self.batch_worker:
                if hasattr(self.batch_worker, 'stop'):
                    self.batch_worker.stop()
                    info("批量存稿任务已停止")

            # 停止批量线程
            if hasattr(self, 'batch_thread') and self.batch_thread:
                if self.batch_thread.isRunning():
                    self.batch_thread.quit()
                    if not self.batch_thread.wait(3000):  # 等待3秒
                        self.batch_thread.terminate()
                        self.batch_thread.wait(1000)  # 再等1秒
                    info("批量线程已停止")

        except Exception as e:
            error(f"停止批量任务时出错: {str(e)}")

    def _stop_data_collection_tasks(self):
        """停止数据采集任务"""
        try:
            # 停止数据采集器
            if hasattr(self, 'data_collector') and self.data_collector:
                if hasattr(self.data_collector, 'stop_all_threads'):
                    self.data_collector.stop_all_threads()
                    info("数据采集任务已停止")

        except Exception as e:
            error(f"停止数据采集任务时出错: {str(e)}")

    def _stop_active_threads(self):
        """停止活动线程"""
        try:
            if hasattr(self, 'active_threads') and self.active_threads:
                stopped_count = 0
                for thread, worker in self.active_threads.copy():
                    try:
                        # 尝试优雅停止工作对象
                        if worker:
                            if hasattr(worker, 'stop'):
                                worker.stop()
                            elif hasattr(worker, 'abort'):
                                worker.abort()

                        # 停止线程
                        if thread and thread.isRunning():
                            thread.quit()
                            if not thread.wait(2000):  # 等待2秒
                                thread.terminate()
                                thread.wait(1000)  # 再等1秒
                            stopped_count += 1

                    except Exception as e:
                        error(f"停止单个线程时出错: {str(e)}")

                info(f"已停止 {stopped_count} 个活动线程")
                # 清空活动线程列表
                self.active_threads.clear()

        except Exception as e:
            error(f"停止活动线程时出错: {str(e)}")

    def _cleanup_task_resources(self):
        """清理任务资源"""
        try:
            # 执行垃圾回收
            import gc
            gc.collect()

            # 重置相关状态
            if hasattr(self, 'batch_worker'):
                self.batch_worker = None
            if hasattr(self, 'batch_thread'):
                self.batch_thread = None

            info("任务资源清理完成")

        except Exception as e:
            error(f"清理任务资源时出错: {str(e)}")

    def on_stop_tasks_completed(self, success, results):
        """停止任务完成回调 - 静态模式，无对话框

        Args:
            success: 是否成功完成所有任务
            results: 任务结果字典
        """
        try:
            # 重置停止状态
            self._is_stopping_tasks = False

            if success:
                # 更新状态（简化消息）
                killed_count = results.get("killed_processes", 0) if results else 0
                stopped_threads = results.get("stopped_threads", 0) if results else 0

                # 简化状态消息
                if killed_count > 0 or stopped_threads > 0:
                    self.status_update_signal.emit(f"✅ 所有任务已停止（进程:{killed_count}, 线程:{stopped_threads}）")
                else:
                    self.status_update_signal.emit("✅ 所有任务已停止")

                # 清理批量存稿任务对象
                self._cleanup_batch_cunggao_objects()

                # 不显示成功对话框，只记录日志
                info(f"停止任务完成 - 进程:{killed_count}, 线程:{stopped_threads}")

            else:
                # 获取错误信息
                errors = results.get("errors", []) if results else []
                error_msg = errors[0] if errors else "未知错误"  # 只显示第一个错误

                # 更新状态（简化错误消息）
                self.status_update_signal.emit(f"❌ 停止任务出错: {error_msg}")

                # 尝试清理批量存稿任务对象，即使有错误也尝试清理
                self._cleanup_batch_cunggao_objects()

                # 不显示错误对话框，只记录日志
                error(f"停止任务失败: {error_msg}")

        except Exception as e:
            error(f"处理停止任务完成回调时出错: {str(e)}")
            self.status_update_signal.emit("❌ 停止任务处理出错")
            # 确保状态被重置
            self._is_stopping_tasks = False

    def _wait_for_all_threads_completion(self):
        """等待所有线程完成，避免在清理时出现Qt Fatal错误"""
        try:
            from app.utils.thread_worker import ThreadManager
            import time

            # 检查ThreadManager中是否有活跃线程
            if hasattr(ThreadManager, '_instances') and ThreadManager._instances:
                info(f"发现 {len(ThreadManager._instances)} 个活跃线程，等待完成...")

                max_wait_time = 10  # 最多等待10秒
                wait_interval = 0.5  # 每0.5秒检查一次
                waited_time = 0

                while ThreadManager._instances and waited_time < max_wait_time:
                    running_count = 0
                    for thread, worker in ThreadManager._instances:
                        if thread and thread.isRunning():
                            running_count += 1

                    if running_count == 0:
                        info("所有线程已完成")
                        break

                    info(f"仍有 {running_count} 个线程在运行，继续等待...")
                    time.sleep(wait_interval)
                    waited_time += wait_interval

                if waited_time >= max_wait_time:
                    warning(f"等待线程完成超时({max_wait_time}秒)，继续执行清理")
                else:
                    info(f"所有线程已在 {waited_time:.1f} 秒内完成")
            else:
                info("没有活跃线程需要等待")

        except Exception as e:
            warning(f"等待线程完成时出错: {str(e)}")

    def _cleanup_batch_cunggao_objects(self):
        """清理批量存稿和单账号存稿任务对象，防止再次点击存稿时闪退"""
        try:
            info("开始清理存稿任务对象...")

            # 清理批量存稿工作对象
            if hasattr(self, 'batch_worker') and self.batch_worker:
                try:
                    # 检查对象是否仍然有效
                    if self._is_worker_object_valid(self.batch_worker):
                        # 停止工作器中的定时器
                        if hasattr(self.batch_worker, 'completion_check_timer') and self.batch_worker.completion_check_timer:
                            self.batch_worker.completion_check_timer.stop()
                            info("已停止批量存稿工作器中的完成检查定时器")

                        # 停止工作器运行状态
                        if hasattr(self.batch_worker, 'is_running'):
                            self.batch_worker.is_running = False
                            info("已设置批量存稿工作器停止运行")

                        # 设置取消标志
                        if hasattr(self.batch_worker, 'is_cancelled'):
                            self.batch_worker.is_cancelled = True
                            info("已设置批量存稿工作器取消标志")

                        # 立即调用工作器的停止方法
                        if hasattr(self.batch_worker, 'stop') and callable(self.batch_worker.stop):
                            self.batch_worker.stop()
                            info("已调用批量存稿工作器的停止方法")

                        # 断开所有信号连接
                        if hasattr(self.batch_worker, 'disconnect'):
                            self.batch_worker.disconnect()

                        # 不调用deleteLater()，避免Qt Fatal错误
                        # try:
                        #     self.batch_worker.deleteLater()
                        # except Exception as delete_error:
                        #     debug(f"deleteLater()调用失败: {str(delete_error)}")
                        info("跳过batch_worker的deleteLater()调用")
                    else:
                        warning("批量存稿工作器对象已被删除，跳过清理操作")

                    # 立即设置为None，确保引用被清除
                    self.batch_worker = None
                    info("已清理批量存稿工作对象")
                except RuntimeError as e:
                    if "wrapped C/C++ object" in str(e):
                        warning(f"删除对象时出错: {str(e)}")
                        # 即使对象已被删除，也要清除引用
                        self.batch_worker = None
                        info("已清理批量存稿工作对象引用")
                    else:
                        error(f"清理批量存稿工作对象时出错: {str(e)}")
                except Exception as e:
                    error(f"清理批量存稿工作对象时出错: {str(e)}")
                    # 确保引用被清除
                    self.batch_worker = None

            # 清理批量存稿线程对象 - 使用更安全的方式
            if hasattr(self, 'batch_thread') and self.batch_thread:
                try:
                    # 如果线程仍在运行，尝试停止（避免阻塞主线程）
                    if self.batch_thread.isRunning():
                        info("正在停止批量存稿线程...")
                        self.batch_thread.quit()
                        # 不等待线程停止，避免阻塞主线程
                        info("已发送停止信号，不等待线程停止以避免阻塞")
                    else:
                        info("批量存稿线程已停止")

                    # 不检查线程状态，直接清理引用，避免阻塞和Qt Fatal错误
                    try:
                        if hasattr(self, 'batch_thread') and self.batch_thread:
                            # 不调用deleteLater()，避免Qt Fatal错误
                            # self.batch_thread.deleteLater()
                            info("跳过deleteLater()调用，直接清理引用")

                        self.batch_thread = None
                        info("已清理批量存稿线程引用")
                    except Exception as e:
                        debug(f"清理批量存稿线程引用时出错: {str(e)}")
                        self.batch_thread = None

                except Exception as e:
                    error(f"清理批量存稿线程对象时出错: {str(e)}")

            # 清理所有单账号存稿任务
            if hasattr(self, 'active_cunggao_tasks') and self.active_cunggao_tasks:
                try:
                    info(f"正在清理 {len(self.active_cunggao_tasks)} 个单账号存稿任务...")

                    # 复制字典键，因为我们会在循环中修改字典
                    account_ids = list(self.active_cunggao_tasks.keys())

                    for account_id in account_ids:
                        try:
                            thread, worker = self.active_cunggao_tasks[account_id]

                            # 断开工作对象的信号连接
                            if worker:
                                if hasattr(worker, 'disconnect'):
                                    try:
                                        worker.disconnect()
                                    except Exception:
                                        pass

                                # 不调用deleteLater()，避免Qt Fatal错误
                                # worker.deleteLater()
                                info(f"跳过账号 {account_id} worker的deleteLater()调用")

                            # 清理线程对象（避免阻塞主线程）
                            if thread:
                                if thread.isRunning():
                                    thread.quit()
                                    # 不等待线程停止，避免阻塞主线程
                                    info(f"已发送停止信号给账号 {account_id} 的存稿线程")

                                # 不调用deleteLater()，避免Qt Fatal错误
                                # thread.deleteLater()
                                info(f"跳过账号 {account_id} 线程的deleteLater()调用")

                            # 从字典中移除
                            del self.active_cunggao_tasks[account_id]

                            info(f"账号 {account_id} 的存稿任务资源已清理")
                        except Exception as e:
                            error(f"清理账号 {account_id} 的存稿任务资源时出错: {str(e)}")

                    # 清空字典
                    self.active_cunggao_tasks.clear()
                    info("所有单账号存稿任务资源已清理")
                except Exception as e:
                    error(f"清理单账号存稿任务资源时出错: {str(e)}")

            # 清理主窗口中的引用
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'statusBar'):
                main_window = main_window.parent()

            if main_window:
                if hasattr(main_window, '_batch_draft_worker'):
                    main_window._batch_draft_worker = None
                if hasattr(main_window, '_batch_draft_thread'):
                    main_window._batch_draft_thread = None
                info("已清理主窗口中的批量存稿任务引用")

            # 执行垃圾回收
            import gc
            gc.collect()

            # 系统日志功能已移除

            # 恢复按钮状态
            self.update_button_states(is_running=False)

            # 强制处理Qt事件，确保对象删除完成
            from PyQt5.QtWidgets import QApplication
            QApplication.processEvents()

            # 短暂等待，确保异步删除完成
            import time
            time.sleep(0.1)

            # 再次处理事件
            QApplication.processEvents()

            info("存稿任务对象清理完成")
        except Exception as e:
            error(f"清理存稿任务对象时出错: {str(e)}")

    def _is_worker_object_valid(self, worker_obj):
        """检查工作器对象是否仍然有效"""
        try:
            if worker_obj is None:
                return False
            # 尝试访问一个基本属性来检查对象是否有效
            _ = worker_obj.objectName()
            return True
        except RuntimeError:
            # 对象已被删除
            return False
        except Exception:
            # 其他异常，假设对象仍然有效
            return True



    def stop_data_collection(self):
        """停止数据采集功能已移除"""
        # 清除数据采集器引用
        if hasattr(self, 'data_collector'):
            delattr(self, 'data_collector')

        # 清除定时器
        if hasattr(self, 'data_collection_timer') and self.data_collection_timer:
            self.data_collection_timer.stop()
            self.data_collection_timer = None

        # 清除设置
        if hasattr(self, 'collection_settings'):
            delattr(self, 'collection_settings')

        QMessageBox.information(self, "功能已移除", "数据采集功能已被移除")

    def on_batch_status_update(self, status_message):
        """处理批量存稿状态更新信号"""
        # 记录状态消息到日志
        info(f"批量存稿状态更新: {status_message}")

        # 发送状态更新信号到系统日志选项卡
        if "❌" in status_message or "失败" in status_message or "错误" in status_message:
            self.batch_log_updated.emit("批量存稿", "ERROR", status_message)
        elif "⚠️" in status_message and not any(keyword in status_message for keyword in [
            "第一个按钮点击成功", "第二个按钮点击成功", "检测超时", "未检测到'全部保存成功'",
            "视为存稿完成", "选择视频文件", "找到封面文件"
        ]):
            # 只有真正的警告才标记为WARNING，正常操作提醒不算警告
            self.batch_log_updated.emit("批量存稿", "WARNING", status_message)
        elif "ℹ️" in status_message or any(keyword in status_message for keyword in [
            "第一个按钮点击成功", "第二个按钮点击成功", "检测超时", "未检测到'全部保存成功'",
            "视为存稿完成", "选择视频文件", "找到封面文件"
        ]):
            # 这些是正常操作的提醒信息，标记为INFO
            self.batch_log_updated.emit("批量存稿", "INFO", status_message)
        elif "账号" in status_message and "存稿成功" in status_message:
            # 只有账号存稿成功才使用SUCCESS等级
            self.batch_log_updated.emit("批量存稿", "SUCCESS", status_message)
        elif "✅" in status_message or "成功" in status_message or "完成" in status_message:
            # 其他成功信息使用INFO等级
            self.batch_log_updated.emit("批量存稿", "INFO", status_message)
        else:
            self.batch_log_updated.emit("批量存稿", "INFO", status_message)

        # 发送状态更新信号，用于在日志标签页显示
        if hasattr(self, 'status_update_signal'):
            self.status_update_signal.emit(status_message)

        # 更新状态栏
        main_window = self.parent()
        while main_window and not hasattr(main_window, 'statusBar'):
            main_window = main_window.parent()

        if main_window and hasattr(main_window, 'statusBar'):
            main_window.statusBar().showMessage(status_message, 3000)

    def on_batch_progress_update(self, current, total):
        """处理批量存稿进度更新信号"""
        try:
            # 计算进度百分比
            progress_percent = int(current / total * 100) if total > 0 else 0

            # 更新状态栏显示进度
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'statusBar'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'statusBar'):
                main_window.statusBar().showMessage(f"批量存稿进度: {current}/{total} ({progress_percent}%)", 5000)

            # 发送批量进度更新信号到系统日志选项卡
            self.batch_progress_updated.emit(current, total)

            # 不再发送进度更新日志，保持日志简洁

            # 批量存稿进度条和进度对话框功能已移除

            # 强制处理Qt事件，确保UI立即更新
            from PyQt5.QtWidgets import QApplication
            QApplication.processEvents()

        except Exception as e:
            error(f"处理批量存稿进度更新时出错: {str(e)}")

    # 批量存稿进度条功能已移除

    # 批量存稿进度条功能已移除

    def on_account_progress_update(self, account_id, progress_type, message, current=0, total=100):
        """处理单个账号进度更新信号"""
        # 不再记录详细的账号进度日志，保持日志简洁

        # 发送账号进度更新信号到系统日志选项卡
        progress_percent = int(current / total * 100) if total > 0 else 0
        self.account_progress_updated.emit(account_id, f"{progress_type}: {message}", progress_percent)

        # 不再发送账号进度日志，保持日志简洁

        # 根据进度类型更新表格状态
        try:
            if progress_type == "开始":
                self.update_account_status_by_id(account_id, "等待中", "waiting")
            elif progress_type == "进行中":
                # 从消息中提取进度信息
                if "次存稿" in message:
                    self.update_account_status_by_id(account_id, f"正在存稿", "processing")
                else:
                    self.update_account_status_by_id(account_id, "正在存稿", "processing")
            elif progress_type == "完成":
                self.update_account_status_by_id(account_id, "存稿完成", "success")
            elif progress_type == "失败":
                if "登录" in message:
                    self.update_account_status_by_id(account_id, "登录失败", "login_failed")
                elif "禁言" in message:
                    self.update_account_status_by_id(account_id, "禁言", "banned")
                else:
                    self.update_account_status_by_id(account_id, "存稿失败", "failed")
            elif progress_type == "error":
                if "禁言" in message:
                    self.update_account_status_by_id(account_id, "禁言", "banned")
                else:
                    self.update_account_status_by_id(account_id, "存稿失败", "failed")
        except Exception as e:
            debug(f"更新账号 {account_id} 表格状态时出错: {str(e)}")

        # 系统日志功能已移除

    def on_runtime_update(self, runtime_str):
        """处理运行时间更新信号"""
        try:
            # 找到主窗口并更新运行时间显示
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'statusBar'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'runtime_label'):
                main_window.runtime_label.setText(runtime_str)

        except Exception as e:
            debug(f"更新运行时间显示时出错: {str(e)}")

    def on_batch_cunggao_finished(self):
        """批量存稿任务完成处理"""
        try:
            # 防止重复处理
            if hasattr(self, '_batch_completion_processed') and self._batch_completion_processed:
                debug("批量存稿完成已处理过，跳过重复处理")
                return

            # 标记已处理
            self._batch_completion_processed = True
            info("批量存稿任务已完成")

            # 重置运行时间显示
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'statusBar'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'runtime_label'):
                main_window.runtime_label.setText("00:00:00")

            # 系统日志功能已移除

            # 更新状态栏
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'statusBar'):
                main_window = main_window.parent()

            if main_window and hasattr(main_window, 'statusBar'):
                main_window.statusBar().showMessage("🎉 批量存稿任务已完成", 5000)

            # 批量存稿进度条功能已移除

            # 强制停止工作器（确保状态完全重置）
            if hasattr(self, 'batch_worker') and self.batch_worker:
                try:
                    # 检查对象是否仍然有效
                    if self._is_worker_object_valid(self.batch_worker):
                        # 确保工作器状态被正确设置
                        if hasattr(self.batch_worker, 'is_running'):
                            self.batch_worker.is_running = False
                            info("已强制设置批量存稿工作器停止状态")

                        if hasattr(self.batch_worker, 'is_cancelled'):
                            self.batch_worker.is_cancelled = False  # 正常完成，不是取消
                            info("已重置批量存稿工作器取消状态")

                        # 设置任务完成标志，防止重复处理
                        if hasattr(self.batch_worker, 'task_completion_processed'):
                            self.batch_worker.task_completion_processed = True
                            info("已设置工作器任务完成标志")

                        # 停止完成检查定时器
                        if hasattr(self.batch_worker, 'completion_check_timer') and self.batch_worker.completion_check_timer:
                            try:
                                if self.batch_worker.completion_check_timer.isActive():
                                    self.batch_worker.completion_check_timer.stop()
                                    info("已停止工作器完成检查定时器")
                            except Exception as timer_error:
                                debug(f"停止完成检查定时器时出错: {str(timer_error)}")

                        # 调用工作器的停止方法（如果有的话）
                        if hasattr(self.batch_worker, 'stop') and callable(self.batch_worker.stop):
                            self.batch_worker.stop()
                            info("已调用批量存稿工作器的停止方法")
                    else:
                        warning("批量存稿工作器对象已被删除，跳过停止操作")

                except RuntimeError as worker_error:
                    if "wrapped C/C++ object" in str(worker_error):
                        warning(f"强制停止工作器时出错: {str(worker_error)}")
                    else:
                        error(f"强制停止工作器时出错: {str(worker_error)}")
                except Exception as worker_error:
                    warning(f"强制停止工作器时出错: {str(worker_error)}")

            # 发送状态更新信号
            if hasattr(self, 'status_update_signal'):
                self.status_update_signal.emit("🎉 批量存稿任务已完成")

            # 发送批量存稿完成日志到系统日志选项卡
            self.batch_log_updated.emit("批量存稿", "SUCCESS", "🎉 批量存稿任务已完成")

            # 重置界面状态
            self.reset_batch_ui_state()

            # 额外的状态重置，确保下次可以正常启动
            self._force_reset_batch_state()

            # 使用信号机制确保在主线程中执行清理，避免跨线程定时器问题
            info("批量存稿任务正常完成")
            # 完全禁用清理信号发射，避免Qt Fatal错误
            info("跳过cleanup_batch_signal发射，避免Qt Fatal错误")
            # 直接调用清理方法，只清理引用
            self._force_cleanup_batch_objects()

            # 原始清理信号发射逻辑已被注释，避免Qt Fatal错误
            # if hasattr(self, 'cleanup_batch_signal'):
            #     self.cleanup_batch_signal.emit()
            # else:
            #     # 如果没有信号，直接调用（可能在主线程中）
            #     self._force_cleanup_batch_objects()

        except Exception as e:
            error(f"处理批量存稿完成事件时出错: {str(e)}")

    def reset_batch_ui_state(self):
        """重置批量存稿完成后的界面状态"""
        try:
            # 重置批量存稿按钮状态
            if hasattr(self, 'batch_cunggao_btn'):
                self.batch_cunggao_btn.setEnabled(True)
                self.batch_cunggao_btn.setText("批量存稿")
                self.batch_cunggao_btn.setStyleSheet("""
                    QPushButton {
                        background-color: #4CAF50;
                        color: white;
                        border: none;
                        padding: 8px 16px;
                        border-radius: 4px;
                        font-weight: bold;
                    }
                    QPushButton:hover {
                        background-color: #45a049;
                    }
                """)

            # 重置账号表格中处理中的状态（保留最终结果）
            for row in range(self.table.rowCount()):
                status_item = self.table.item(row, 2)  # 状态列
                if status_item:
                    status_text = status_item.text()
                    # 只重置处理中的状态，保留最终结果
                    if any(keyword in status_text for keyword in ["正在", "等待", "处理中"]):
                        from PyQt5.QtGui import QColor
                        from PyQt5.QtWidgets import QTableWidgetItem
                        from PyQt5.QtCore import Qt

                        default_item = QTableWidgetItem("就绪")
                        default_item.setTextAlignment(Qt.AlignCenter)
                        default_item.setBackground(QColor(240, 240, 240))
                        default_item.setForeground(QColor(100, 100, 100))
                        self.table.setItem(row, 2, default_item)

            # 清理批量存稿相关的临时数据
            self._cleanup_batch_cunggao_objects()

            info("界面状态已重置")

        except Exception as e:
            error(f"重置界面状态时出错: {str(e)}")

    def _force_reset_batch_state(self):
        """强制重置批量存稿状态，确保下次可以正常启动"""
        try:
            info("开始强制重置批量存稿状态...")

            # 强制清除所有批量存稿相关的对象引用
            if hasattr(self, 'batch_worker'):
                self.batch_worker = None
                info("已强制清除batch_worker引用")

            if hasattr(self, 'batch_thread'):
                self.batch_thread = None
                info("已强制清除batch_thread引用")

            # 清除主窗口中的引用
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'statusBar'):
                main_window = main_window.parent()

            if main_window:
                if hasattr(main_window, '_batch_draft_worker'):
                    main_window._batch_draft_worker = None
                    info("已强制清除主窗口中的_batch_draft_worker引用")
                if hasattr(main_window, '_batch_draft_thread'):
                    main_window._batch_draft_thread = None
                    info("已强制清除主窗口中的_batch_draft_thread引用")

            # 强制垃圾回收
            import gc
            gc.collect()

            # 强制处理Qt事件
            from PyQt5.QtWidgets import QApplication
            QApplication.processEvents()

            # 短暂等待
            import time
            time.sleep(0.2)

            # 再次处理事件
            QApplication.processEvents()

            info("批量存稿状态强制重置完成")

        except Exception as e:
            error(f"强制重置批量存稿状态时出错: {str(e)}")

    def _cleanup_remaining_single_draft_threads(self):
        """清理可能残留的单独存稿线程"""
        try:
            info("检查并清理残留的单独存稿线程...")

            # 检查活动存稿任务字典中的线程
            if hasattr(self, 'active_cunggao_tasks') and self.active_cunggao_tasks:
                threads_to_cleanup = []

                for account_id, task_info in list(self.active_cunggao_tasks.items()):
                    if isinstance(task_info, dict) and 'thread' in task_info:
                        thread = task_info['thread']
                        if thread and hasattr(thread, 'isRunning'):
                            if thread.isRunning():
                                warning(f"发现残留的单独存稿线程: {account_id}")
                                threads_to_cleanup.append((account_id, thread))
                            else:
                                # 线程已停止但未清理，直接清理
                                debug(f"清理已停止的单独存稿线程: {account_id}")
                                try:
                                    # 不调用deleteLater()，避免Qt Fatal错误
                                    # thread.deleteLater()
                                    info(f"线程 {account_id} 已停止，跳过deleteLater()调用")
                                except Exception:
                                    pass
                                del self.active_cunggao_tasks[account_id]

                # 安全停止残留的运行中线程
                for account_id, thread in threads_to_cleanup:
                    try:
                        info(f"停止残留的单独存稿线程: {account_id}")
                        thread.quit()
                        if not thread.wait(3000):
                            warning(f"强制终止残留线程: {account_id}")
                            thread.terminate()
                            thread.wait(1000)

                        # 直接清理，避免使用QTimer
                        try:
                            if thread and hasattr(thread, 'deleteLater'):
                                # 不调用deleteLater()，避免Qt Fatal错误
                                # thread.deleteLater()
                                info(f"线程 {account_id} 跳过deleteLater()调用")
                            if account_id in self.active_cunggao_tasks:
                                del self.active_cunggao_tasks[account_id]
                            info(f"已清理残留线程: {account_id}")
                        except Exception as e:
                            debug(f"清理残留线程时出错: {str(e)}")
                            # 强制从字典中移除
                            if account_id in self.active_cunggao_tasks:
                                del self.active_cunggao_tasks[account_id]

                    except Exception as e:
                        error(f"清理残留线程 {account_id} 时出错: {str(e)}")
                        # 强制从字典中移除
                        if account_id in self.active_cunggao_tasks:
                            del self.active_cunggao_tasks[account_id]

            info("残留单独存稿线程清理完成")

        except Exception as e:
            error(f"清理残留单独存稿线程时出错: {str(e)}")

    def _force_cleanup_batch_objects(self):
        """安全清理批量存稿对象引用 - 只清理引用，不操作线程"""
        info("开始安全清理批量存稿对象引用...")

        try:
            # 只清理引用，不进行任何线程操作
            if hasattr(self, 'batch_worker'):
                # 如果worker还存在，先重置其状态
                if self.batch_worker and hasattr(self.batch_worker, 'is_running'):
                    old_state = self.batch_worker.is_running
                    self.batch_worker.is_running = False
                    info(f"已重置batch_worker.is_running状态: {old_state} -> False")

                self.batch_worker = None
                info("已清理batch_worker引用")

            if hasattr(self, 'batch_thread'):
                self.batch_thread = None
                info("已清理batch_thread引用")

            # 清理主窗口中的引用
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'statusBar'):
                main_window = main_window.parent()

            if main_window:
                if hasattr(main_window, '_batch_draft_worker'):
                    # 如果主窗口的worker还存在，先重置其状态
                    if main_window._batch_draft_worker and hasattr(main_window._batch_draft_worker, 'is_running'):
                        old_state = main_window._batch_draft_worker.is_running
                        main_window._batch_draft_worker.is_running = False
                        info(f"已重置主窗口batch_draft_worker.is_running状态: {old_state} -> False")

                    main_window._batch_draft_worker = None
                    info("已清理主窗口batch_draft_worker引用")

                if hasattr(main_window, '_batch_draft_thread'):
                    main_window._batch_draft_thread = None
                    info("已清理主窗口batch_draft_thread引用")

            # 清理活跃任务标记（包括单独存稿和批量存稿）
            if hasattr(self, 'active_cunggao_tasks'):
                task_count = len(self.active_cunggao_tasks)
                if task_count > 0:
                    info(f"发现{task_count}个活跃存稿任务标记，准备清理:")
                    for account_id in list(self.active_cunggao_tasks.keys()):
                        info(f"  - 清理账号 {account_id} 的任务标记")

                self.active_cunggao_tasks.clear()
                info(f"已清理{task_count}个活跃存稿任务标记（包括单独存稿任务）")

            # 重置完成处理标志
            if hasattr(self, '_batch_completion_processed'):
                self._batch_completion_processed = False
                info("已重置批量存稿完成处理标志")

            # 强制重置按钮状态
            if hasattr(self, 'batch_cunggao_btn'):
                self.batch_cunggao_btn.setEnabled(True)
                self.batch_cunggao_btn.setText("批量存稿")
                info("已重置批量存稿按钮状态")

            info("安全清理批量存稿对象引用完成")

        except Exception as e:
            error(f"清理批量存稿对象引用时出错: {str(e)}")
            import traceback
            error(f"清理异常详情: {traceback.format_exc()}")
            # 即使出错也要确保引用被清理
            self.batch_worker = None
            self.batch_thread = None



    def _reset_batch_status_flags(self):
        """重置批量存稿状态标志，确保下次可以正常启动"""
        try:
            # 确保批量存稿按钮状态正确
            if hasattr(self, 'batch_cunggao_btn'):
                self.batch_cunggao_btn.setEnabled(True)
                self.batch_cunggao_btn.setText("批量存稿")
                info("已重置批量存稿按钮状态")

            # 清理活跃任务标记
            if hasattr(self, 'active_cunggao_tasks'):
                self.active_cunggao_tasks.clear()
                info("已清理活跃存稿任务标记")

            # 清理主窗口中的引用
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'statusBar'):
                main_window = main_window.parent()

            if main_window:
                if hasattr(main_window, '_batch_draft_worker'):
                    main_window._batch_draft_worker = None
                    info("已清理主窗口中的_batch_draft_worker引用")
                if hasattr(main_window, '_batch_draft_thread'):
                    main_window._batch_draft_thread = None
                    info("已清理主窗口中的_batch_draft_thread引用")

            info("批量存稿状态标志重置完成")

        except Exception as e:
            error(f"重置批量存稿状态标志时出错: {str(e)}")

    def _is_batch_task_running(self):
        """检查批量存稿任务是否正在运行"""
        try:
            # 检查批量存稿线程是否在运行
            if hasattr(self, 'batch_thread') and self.batch_thread and self.batch_thread.isRunning():
                debug("检测到批量存稿线程正在运行")
                return True

            # 检查批量存稿工作器是否在运行
            if hasattr(self, 'batch_worker') and self.batch_worker:
                if hasattr(self.batch_worker, 'is_running') and self.batch_worker.is_running:
                    debug("检测到批量存稿工作器正在运行")
                    return True

            # 检查主窗口中的批量存稿引用
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'statusBar'):
                main_window = main_window.parent()

            if main_window:
                if hasattr(main_window, '_batch_draft_worker') and main_window._batch_draft_worker:
                    if hasattr(main_window._batch_draft_worker, 'is_running') and main_window._batch_draft_worker.is_running:
                        debug("检测到主窗口中的批量存稿工作器正在运行")
                        return True

                if hasattr(main_window, '_batch_draft_thread') and main_window._batch_draft_thread:
                    if main_window._batch_draft_thread.isRunning():
                        debug("检测到主窗口中的批量存稿线程正在运行")
                        return True

            debug("未检测到正在运行的批量存稿任务")
            return False

        except Exception as e:
            error(f"检查批量存稿任务状态时出错: {str(e)}")
            return False  # 出错时假设没有任务在运行

    def on_status_update(self, status_message):
        """处理状态更新信号"""
        # 记录状态消息到日志
        info(f"状态更新: {status_message}")

        # 发送状态更新信号，用于在日志标签页显示
        if hasattr(self, 'status_update_signal'):
            self.status_update_signal.emit(status_message)

        # 检查是否是草稿数量更新信号
        if status_message.startswith("更新草稿数量:"):
            try:
                # 解析信号内容: "更新草稿数量:账号ID:草稿数量"
                parts = status_message.split(":")
                if len(parts) >= 3:
                    account_id = parts[1]
                    draft_count = int(parts[2])

                    # 更新表格中的草稿数量
                    self.update_draft_count(account_id, draft_count)

                    # 记录日志
                    info(f"已更新账号 {account_id} 的草稿数量: {draft_count}")
                    return
            except Exception as e:
                error(f"处理草稿数量更新信号时出错: {str(e)}")

        # 检查是否是增加存稿次数信号
        elif status_message.startswith("增加存稿次数:"):
            try:
                # 解析信号内容: "增加存稿次数:账号ID"
                parts = status_message.split(":")
                if len(parts) >= 2:
                    account_id = parts[1]

                    # 查找账号对应的行
                    for row in range(self.table.rowCount()):
                        # 检查第3列（索引2）是否为当前账号ID
                        account_id_item = self.table.item(row, 1)  # 账号ID在第2列(索引1)
                        if account_id_item and account_id_item.text() == str(account_id):
                            # 已移除"本次存稿"列，不再更新存稿次数
                            # 记录日志
                            info(f"账号 {account_id} 存稿成功")

                            # 强制刷新表格
                            self.table.viewport().update()

                            # 添加额外的刷新机制，确保UI更新
                            QApplication.processEvents()

                            return

                    # 如果没有找到账号，记录日志
                    warning(f"未找到账号 {account_id} 对应的表格行")
                    return
            except Exception as e:
                error(f"处理增加存稿次数信号时出错: {str(e)}")

        # 如果有状态栏，更新状态栏信息
        if hasattr(self, 'status_label') and self.status_label:
            self.status_label.setText(status_message)

        # 不再直接调用processEvents，避免递归事件处理

    def update_draft_count(self, account_id, draft_count):
        """更新表格中指定账号的草稿数量

        Args:
            account_id: 账号ID
            draft_count: 草稿数量
        """
        try:
            # 查找账号对应的行
            for row in range(self.table.rowCount()):
                # 检查第3列（索引2）是否为当前账号ID
                account_id_item = self.table.item(row, 1)  # 账号ID在第2列(索引1)
                if account_id_item and account_id_item.text() == str(account_id):
                    # 更新草稿数量列（第5列，索引为4）
                    item = QTableWidgetItem(str(draft_count))
                    item.setTextAlignment(Qt.AlignCenter)  # 居中对齐
                    self.table.setItem(row, 4, item)

                    # 已移除"本次存稿"列，不再更新存稿次数

                    # 记录日志
                    info(f"账号 {account_id} 草稿数量已更新: {draft_count}")

                    # 同时更新数据文件中的草稿数量
                    self.update_draft_count_in_data_file(account_id, draft_count)

                    # 强制刷新表格
                    self.table.viewport().update()
                    return True

            # 如果没有找到账号，记录日志
            warning(f"未找到账号 {account_id} 对应的表格行")
            return False
        except Exception as e:
            error(f"更新草稿数量时出错: {str(e)}")
            return False

    def update_draft_count_in_data_file(self, account_id, draft_count):
        """更新数据文件中的草稿数量

        Args:
            account_id: 账号ID
            draft_count: 草稿数量
        """
        try:
            # 构建数据文件路径
            # 首先检查是否已经设置了data_dir属性
            if hasattr(self, 'data_dir') and self.data_dir and os.path.exists(self.data_dir):
                data_path = os.path.join(self.data_dir, f"{account_id}.json")
                info(f"使用已设置的数据目录保存草稿数量: {data_path}")
            else:
                # 尝试从主窗口的设置标签页获取数据目录
                main_window = self.window()
                if hasattr(main_window, 'setting_tab') and hasattr(main_window.setting_tab, 'data_path'):
                    setting_data_path = main_window.setting_tab.data_path.text()
                    if setting_data_path and os.path.exists(setting_data_path):
                        data_path = os.path.join(setting_data_path, f"{account_id}.json")
                        info(f"使用设置中的数据目录保存草稿数量: {data_path}")
                    else:
                        # 使用默认路径
                        if hasattr(self, 'cookie_path') and self.cookie_path:
                            # 如果cookie_path是目录，直接使用其父目录下的data
                            if os.path.isdir(self.cookie_path):
                                data_path = os.path.join(os.path.dirname(self.cookie_path), "data", f"{account_id}.json")
                            # 如果cookie_path是文件，使用其所在目录的父目录下的data
                            elif os.path.isfile(self.cookie_path):
                                data_path = os.path.join(os.path.dirname(os.path.dirname(self.cookie_path)), "data", f"{account_id}.json")
                            else:
                                # 如果cookie_path既不是文件也不是目录，使用当前目录下的data
                                data_path = os.path.join(os.getcwd(), "data", f"{account_id}.json")
                        else:
                            # 如果没有cookie_path，使用当前目录下的data
                            data_path = os.path.join(os.getcwd(), "data", f"{account_id}.json")
                        info(f"设置中的数据目录不存在，使用默认数据目录保存草稿数量: {data_path}")
                else:
                    # 使用默认路径
                    if hasattr(self, 'cookie_path') and self.cookie_path:
                        # 如果cookie_path是目录，直接使用其父目录下的data
                        if os.path.isdir(self.cookie_path):
                            data_path = os.path.join(os.path.dirname(self.cookie_path), "data", f"{account_id}.json")
                        # 如果cookie_path是文件，使用其所在目录的父目录下的data
                        elif os.path.isfile(self.cookie_path):
                            data_path = os.path.join(os.path.dirname(os.path.dirname(self.cookie_path)), "data", f"{account_id}.json")
                        else:
                            # 如果cookie_path既不是文件也不是目录，使用当前目录下的data
                            data_path = os.path.join(os.getcwd(), "data", f"{account_id}.json")
                    else:
                        # 如果没有cookie_path，使用当前目录下的data
                        data_path = os.path.join(os.getcwd(), "data", f"{account_id}.json")
                    info(f"无法获取设置中的数据目录，使用默认数据目录保存草稿数量: {data_path}")

            # 如果数据文件存在，更新草稿数量
            if os.path.exists(data_path):
                try:
                    # 读取数据文件
                    with open(data_path, 'r', encoding='utf-8') as f:
                        account_data = json.load(f)

                    # 更新草稿数量
                    account_data["draft_count"] = draft_count

                    # 写回数据文件
                    with open(data_path, 'w', encoding='utf-8') as f:
                        json.dump(account_data, f, ensure_ascii=False, indent=2)

                    info(f"已更新账号 {account_id} 的数据文件中的草稿数量: {draft_count}")
                    return True
                except Exception as e:
                    error(f"更新数据文件中的草稿数量时出错: {str(e)}")
                    return False
            else:
                # 如果数据文件不存在，创建一个新的
                try:
                    # 创建数据目录
                    os.makedirs(os.path.dirname(data_path), exist_ok=True)

                    # 创建基本数据
                    account_data = {
                        "account_id": account_id,
                        "draft_count": draft_count,
                        "status": "正常"
                    }

                    # 写入数据文件
                    with open(data_path, 'w', encoding='utf-8') as f:
                        json.dump(account_data, f, ensure_ascii=False, indent=2)

                    info(f"已创建账号 {account_id} 的数据文件并设置草稿数量: {draft_count}")
                    return True
                except Exception as e:
                    error(f"创建数据文件时出错: {str(e)}")
                    return False
        except Exception as e:
            error(f"更新数据文件中的草稿数量时出错: {str(e)}")
            return False

    def on_table_cell_double_clicked(self, row, column):
        """处理表格单元格双击事件 - 已禁用数据加载功能

        Args:
            row: 行索引
            column: 列索引
        """
        try:
            # 记录被点击的列
            info(f"双击表格单元格: 行={row}, 列={column}")

            # 根据用户要求，双击账号列表不再加载数据
            # 只记录日志，不执行任何操作
            debug("双击账号列表不加载数据，功能已禁用")

        except Exception as e:
            error(f"处理表格双击事件时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())

    def on_data_updated(self, account_id, account_data=None):
        """处理数据更新信号，更新表格中的账号数据

        Args:
            account_id: 账号ID
            account_data: 账号数据字典，如果为None则从文件读取
                          注意：account_data可能是dict类型，也可能是其他类型
        """
        try:
            # 记录调试信息
            debug(f"收到数据更新信号，账号: {account_id}, 是否有数据: {account_data is not None}")

            # 确保账号ID不为空
            if not account_id:
                error("收到数据更新信号，但账号ID为空")
                return

            # 记录更详细的调试信息
            debug(f"账号ID类型: {type(account_id)}, 值: {account_id}")

            if account_data:
                # 确保account_data是字典类型
                if isinstance(account_data, dict):
                    debug(f"账号数据字段: {list(account_data.keys())}")
                    # 确保账号ID正确
                    account_data["account_id"] = account_id
                else:
                    debug(f"账号数据不是字典类型，而是 {type(account_data)}")
                    # 尝试将非字典类型转换为字典
                    try:
                        if isinstance(account_data, str):
                            # 尝试解析JSON字符串
                            import json
                            account_data = json.loads(account_data)
                            debug(f"已将JSON字符串转换为字典，字段: {list(account_data.keys())}")
                        else:
                            # 如果不是字符串，尝试转换为字典
                            account_data = dict(account_data)
                            debug(f"已将对象转换为字典，字段: {list(account_data.keys())}")

                        # 确保账号ID正确
                        account_data["account_id"] = account_id
                    except Exception as convert_err:
                        error(f"转换账号数据为字典时出错: {str(convert_err)}")
                        # 如果转换失败，则创建一个基本数据对象
                        account_data = {
                            "account_id": account_id,
                            "username": account_id,
                            "status": "数据格式错误"
                        }
            else:
                # 如果没有数据，尝试从文件读取
                debug(f"没有传入数据，尝试从文件读取账号 {account_id} 的数据")

            # 保存数据到文件
            try:
                self.save_account_data_to_file(account_id, account_data)
            except Exception as save_err:
                error(f"保存账号 {account_id} 数据到文件时出错: {str(save_err)}")

            # 调用更新方法，它会自动查找行索引
            self.update_account_data_in_table(account_id, account_data)

        except Exception as e:
            error(f"更新表格数据时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())

    def load_single_account_data(self, row):
        """按需加载单个账号的详细数据 - 延迟加载模式下使用

        Args:
            row: 表格行索引
        """
        try:
            # 获取账号ID
            account_item = self.table.item(row, 1)  # 账号列
            if not account_item:
                error(f"行 {row} 没有账号ID")
                return

            account_id = account_item.text()

            # 检查是否有账号加载器
            if not hasattr(self, 'account_loader'):
                error("账号加载器不存在")
                return

            # 加载账号数据
            account_data = self.account_loader.load_account_data(account_id)

            if account_data:
                # 更新表格中的账号数据
                self.update_account_data_in_table(account_id, account_data)
                info(f"已加载账号 {account_id} 的详细数据")
            else:
                warning(f"无法加载账号 {account_id} 的详细数据")

        except Exception as e:
            error(f"加载单个账号数据时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())



    def update_account_data_in_table(self, account_id, account_data=None):
        """根据保存的数据文件或传入的数据更新表格中的账号数据

        Args:
            account_id: 账号ID
            account_data: 账号数据字典，如果为None则从文件读取
        """
        try:
            # 记录日志，帮助调试
            debug(f"更新账号 {account_id} 的表格数据，是否有数据: {account_data is not None}")

            # 安全检查account_id
            if not account_id or not isinstance(account_id, str):
                error(f"账号ID无效: {account_id}")
                return

            if account_data:
                # 检查account_data的类型，确保它是字典
                if isinstance(account_data, dict):
                    debug(f"账号数据字段: {list(account_data.keys())}")
                else:
                    debug(f"账号数据类型错误，期望dict，实际: {type(account_data)}, 值: {account_data}")
                    # 如果不是字典，尝试解析为JSON
                    if isinstance(account_data, str):
                        try:
                            import json
                            account_data = json.loads(account_data)
                            debug(f"成功将字符串解析为字典，字段: {list(account_data.keys())}")
                        except Exception as parse_e:
                            error(f"无法将字符串解析为JSON: {str(parse_e)}")
                            return
                    else:
                        error(f"账号数据类型不支持: {type(account_data)}")
                        return
        except Exception as e:
            error(f"更新账号数据时出错: {str(e)}")
            return

        # 记录表格结构信息
        column_count = self.table.columnCount()
        row_count = self.table.rowCount()
        debug(f"表格结构: {row_count}行 x {column_count}列")

        # 记录表头信息
        header_items = []
        for col in range(column_count):
            header_item = self.table.horizontalHeaderItem(col)
            if header_item:
                header_items.append(header_item.text())
            else:
                header_items.append(f"列{col}")
        debug(f"表格列标题: {header_items}")

        try:
            # 查找账号对应的行索引
            row = -1

            # 记录所有行的前几列数据，帮助调试
            debug(f"开始查找账号 {account_id} 对应的行索引")
            for i in range(min(row_count, 10)):  # 只记录前10行，避免日志过多
                row_data = []
                for j in range(min(column_count, 5)):  # 只记录前5列
                    item = self.table.item(i, j)
                    row_data.append(item.text() if item else "None")
                debug(f"行 {i} 数据: {row_data}")

            # 尝试多种方式匹配账号ID
            for i in range(self.table.rowCount()):
                # 检查账号ID列（索引1）
                item = self.table.item(i, 1)
                if item and item.text().strip().lower() == account_id.strip().lower():
                    row = i
                    debug(f"在列1找到账号 {account_id} 对应的行索引: {row}")
                    break

                # 检查账号ID列（索引2）
                item = self.table.item(i, 2)
                if item and item.text().strip().lower() == account_id.strip().lower():
                    row = i
                    debug(f"在列2找到账号 {account_id} 对应的行索引: {row}")
                    break

                # 尝试部分匹配（账号ID可能包含在单元格文本中）
                for col in range(min(5, column_count)):  # 只检查前5列
                    item = self.table.item(i, col)
                    if item and account_id.strip().lower() in item.text().strip().lower():
                        row = i
                        debug(f"在列{col}找到包含账号 {account_id} 的行索引: {row}")
                        break

            if row == -1:
                debug(f"未找到账号 {account_id} 对应的行索引，尝试记录所有行的账号ID")
                # 记录所有行的账号ID，帮助调试
                for i in range(self.table.rowCount()):
                    row_data = []
                    for j in range(min(column_count, 5)):  # 只记录前5列
                        item = self.table.item(i, j)
                        row_data.append(item.text() if item else "None")
                    debug(f"行 {i} 数据: {row_data}")
                return

            # 如果没有传入数据，则从文件读取
            if account_data is None:
                # 构建可能的数据文件路径列表
                possible_paths = []

                # 1. 首先尝试使用data_dir属性（如果存在）
                if hasattr(self, 'data_dir') and self.data_dir:
                    possible_paths.append(os.path.join(self.data_dir, f"{account_id}.json"))

                # 2. 尝试从主窗口的设置标签页获取数据目录
                main_window = self.window()
                if hasattr(main_window, 'setting_tab') and hasattr(main_window.setting_tab, 'data_path'):
                    setting_data_path = main_window.setting_tab.data_path.text()
                    if setting_data_path:
                        possible_paths.append(os.path.join(setting_data_path, f"{account_id}.json"))

                # 3. 使用cookie_path相对路径
                if hasattr(self, 'cookie_path') and self.cookie_path:
                    # 如果cookie_path是目录
                    if os.path.isdir(self.cookie_path):
                        possible_paths.append(os.path.join(os.path.dirname(self.cookie_path), "data", f"{account_id}.json"))
                    # 如果cookie_path是文件
                    elif os.path.isfile(self.cookie_path):
                        possible_paths.append(os.path.join(os.path.dirname(os.path.dirname(self.cookie_path)), "data", f"{account_id}.json"))

                # 4. 使用当前目录下的data目录
                possible_paths.append(os.path.join(os.getcwd(), "data", f"{account_id}.json"))

                # 5. 使用绝对路径
                for path in list(possible_paths):  # 使用list创建副本避免在迭代时修改列表
                    possible_paths.append(os.path.abspath(path))

                # 6. 使用固定目录
                possible_paths.extend([
                    os.path.join("E:\\软件共享\\data", f"{account_id}.json"),
                    os.path.join("E:\\ruanjianggongxiang\\data", f"{account_id}.json"),
                    os.path.join("E:\\toutiaoyuanma1\\data", f"{account_id}.json"),
                    os.path.join("E:\\toutiaoyuanma1\\tou2\\data", f"{account_id}.json"),
                    os.path.join(os.path.dirname(os.getcwd()), "data", f"{account_id}.json"),
                    os.path.join(os.path.dirname(os.path.dirname(os.getcwd())), "data", f"{account_id}.json")
                ])

                # 去除重复路径
                possible_paths = list(set(possible_paths))

                # 查找第一个存在的数据文件
                data_path = None
                for path in possible_paths:
                    if os.path.exists(path):
                        data_path = path
                        debug(f"找到账号 {account_id} 的数据文件: {path}")
                        break

                # 如果所有路径都不存在，则退出
                if data_path is None:
                    debug(f"未找到账号 {account_id} 的数据文件")
                    return

                # 读取数据文件
                try:
                    # 尝试使用不同的编码方式读取文件
                    encodings = ['utf-8', 'gbk', 'gb2312', 'gb18030', 'latin1']
                    account_data = None

                    for encoding in encodings:
                        try:
                            with open(data_path, 'r', encoding=encoding) as f:
                                account_data = json.load(f)
                            debug(f"成功使用 {encoding} 编码读取数据文件: {data_path}")

                            # 检查数据是否包含乱码
                            has_garbled = False
                            for key, value in account_data.items():
                                if isinstance(value, str) and any(ord(c) > 127 for c in value):
                                    if '鍏?' in value or '姝' in value:
                                        has_garbled = True
                                        break

                            # 如果有乱码，继续尝试其他编码
                            if has_garbled:
                                debug(f"数据包含乱码，尝试其他编码")
                                account_data = None
                                continue

                            # 如果没有乱码，跳出循环
                            break
                        except Exception as enc_e:
                            debug(f"使用 {encoding} 编码读取数据文件失败: {str(enc_e)}")
                            continue

                    # 如果所有编码都失败，尝试使用二进制模式读取并修复
                    if account_data is None:
                        try:
                            with open(data_path, 'rb') as f:
                                content = f.read()

                            # 使用编码检测函数
                            detected_encoding = detect_file_encoding(content)
                            debug(f"检测到文件编码: {detected_encoding}")

                            # 使用检测到的编码解码
                            if detected_encoding:
                                try:
                                    decoded_content = content.decode(detected_encoding)
                                    account_data = json.loads(decoded_content)
                                    debug(f"使用编码 {detected_encoding} 成功读取数据文件")
                                except Exception as dec_e:
                                    error(f"使用编码 {detected_encoding} 解码失败: {str(dec_e)}")
                                    # 如果解码失败，尝试其他常见编码
                                    for fallback_encoding in ['utf-8', 'gbk', 'gb2312']:
                                        try:
                                            decoded_content = content.decode(fallback_encoding)
                                            account_data = json.loads(decoded_content)
                                            debug(f"使用备用编码 {fallback_encoding} 成功读取数据文件")
                                            break
                                        except Exception:
                                            continue
                        except ImportError:
                            error("chardet 模块不可用，无法检测文件编码")
                        except Exception as bin_e:
                            error(f"二进制模式读取文件失败: {str(bin_e)}")

                    # 如果仍然无法读取，返回
                    if account_data is None:
                        error(f"无法读取数据文件: {data_path}")
                        return

                    # 尝试修复并重新保存文件
                    try:
                        with open(data_path, 'w', encoding='utf-8') as f:
                            json.dump(account_data, f, ensure_ascii=False, indent=4)
                        debug(f"已修复并重新保存数据文件: {data_path}")
                    except Exception as save_e:
                        warning(f"重新保存数据文件失败: {str(save_e)}")

                except Exception as e:
                    error(f"读取数据文件失败: {str(e)}")
                    return

            # 更新表格中的各列数据 - 优化版本，减少UI更新
            # 创建所有需要更新的单元格项目，然后一次性设置
            items_to_update = {}

            # 记录日志，帮助调试
            debug(f"更新表格数据，账号: {account_id}, 数据字段: {list(account_data.keys()) if account_data and isinstance(account_data, dict) else 'None'}")

            # 只更新昵称列(0列)，不更新账号ID列(1列)
            if account_data and "username" in account_data and account_data["username"]:
                username = account_data["username"]

                # 昵称列(0列)
                item = QTableWidgetItem(username)
                item.setTextAlignment(Qt.AlignCenter)
                items_to_update[0] = item

                # 备注列(2列)
                # 注意：根据截图分析，备注列可能是第3列(索引2)，而不是第2列(索引1)
                # 尝试从表头确定备注列
                remark_column = None
                for col in range(column_count):
                    header_item = self.table.horizontalHeaderItem(col)
                    if header_item and "备注" in header_item.text():
                        remark_column = col
                        debug(f"找到备注列，索引为 {col}")
                        break

                # 如果找到备注列，则更新备注
                if remark_column is not None:
                    item = QTableWidgetItem(username)
                    item.setTextAlignment(Qt.AlignCenter)
                    items_to_update[remark_column] = item
                    debug(f"将在备注列(索引 {remark_column})更新昵称: {username}")
                else:
                    debug("未找到备注列，跳过备注更新")

            # 状态(3列)
            if account_data and "status" in account_data and account_data["status"]:
                status = account_data["status"]
                status_item = QTableWidgetItem(status)
                status_item.setTextAlignment(Qt.AlignCenter)
                status_item.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))

                # 根据状态设置颜色
                if status == "正常":
                    status_item.setBackground(QColor(200, 255, 200))  # 浅绿色
                    status_item.setForeground(QColor(0, 100, 0))      # 深绿色文字
                elif status == "采集中" or status == "正在采集":
                    status_item.setBackground(QColor(200, 200, 255))  # 浅蓝色
                    status_item.setForeground(QColor(0, 0, 150))      # 深蓝色文字
                elif status in ["采集失败", "登录失败"]:
                    status_item.setBackground(QColor(255, 200, 200))  # 浅红色
                    status_item.setForeground(QColor(180, 0, 0))      # 深红色文字
                elif status == "存稿中...":
                    status_item.setBackground(QColor(255, 255, 200))  # 浅黄色
                    status_item.setForeground(QColor(153, 102, 0))    # 棕色文字
                elif status == "存稿完成":
                    status_item.setBackground(QColor(200, 255, 200))  # 浅绿色
                    status_item.setForeground(QColor(0, 100, 0))      # 深绿色文字
                elif status == "待验证":
                    status_item.setBackground(QColor(255, 230, 200))  # 浅橙色
                    status_item.setForeground(QColor(153, 76, 0))     # 深橙色文字

                items_to_update[3] = status_item

            # 定义数据字段到列索引的映射
            # 根据截图分析，表格结构可能与之前的映射不一致
            # 尝试根据表头动态确定列索引
            field_to_column = {}

            # 默认映射（如果无法从表头确定）
            default_mapping = {
                "credit_score": 3,
                "draft_count": 4,
                "yesterday_fans": 5,
                "total_fans": 6,
                "total_play_count": 7,
                "yesterday_play_count": 8,
                "total_income": 9,
                "yesterday_income": 10,
                "seven_days_income": 11,
                "pending_withdraw": 12,
                "register_days": 13,
                "is_verified": 14,
                "total_withdraw": 15,
                "last_withdraw_date": 16,
                "last_withdraw_amount": 17
            }

            # 表头关键词映射
            header_keywords = {
                "信用分": "credit_score",
                "草稿": "draft_count",
                "昨日粉丝": "yesterday_fans",
                "总粉丝": "total_fans",
                "累计播放": "total_play_count",
                "昨日播放": "yesterday_play_count",
                "累计收益": "total_income",
                "昨日收益": "yesterday_income",
                "七天收益": "seven_days_income",
                "待提现": "pending_withdraw",
                "注册天数": "register_days",
                "实名状态": "is_verified",
                "总提现": "total_withdraw",
                "提现日期": "last_withdraw_date",
                "提现金额": "last_withdraw_amount"
            }

            # 尝试从表头确定列索引
            for col in range(column_count):
                header_item = self.table.horizontalHeaderItem(col)
                if header_item:
                    header_text = header_item.text()
                    # 检查表头文本是否包含关键词
                    for keyword, field in header_keywords.items():
                        if keyword in header_text:
                            field_to_column[field] = col
                            debug(f"从表头确定字段 {field} 对应列索引 {col} (表头: {header_text})")

            # 对于未能从表头确定的字段，使用默认映射
            for field, col in default_mapping.items():
                if field not in field_to_column:
                    field_to_column[field] = col
                    debug(f"使用默认映射: 字段 {field} -> 列索引 {col}")

            debug(f"最终字段到列索引映射: {field_to_column}")

            # 批量处理其他数据字段
            if account_data:
                for field, column in field_to_column.items():
                    if field in account_data and account_data[field] is not None:
                        # 特殊处理实名状态字段
                        if field == "is_verified":
                            # 将布尔值转换为"已认证"或"未认证"
                            is_verified = account_data[field]
                            if isinstance(is_verified, bool):
                                status_text = "已认证" if is_verified else "未认证"
                            elif isinstance(is_verified, str):
                                # 如果是字符串，尝试转换为布尔值
                                status_text = "已认证" if is_verified.lower() in ["true", "1", "yes", "已认证"] else "未认证"
                            else:
                                status_text = "未知"

                            item = QTableWidgetItem(status_text)
                            item.setTextAlignment(Qt.AlignCenter)

                            # 设置颜色：已认证为绿色，未认证为红色
                            if status_text == "已认证":
                                item.setForeground(QColor(0, 128, 0))  # 绿色
                            else:
                                item.setForeground(QColor(255, 0, 0))  # 红色

                            items_to_update[column] = item
                        else:
                            # 处理其他普通字段
                            item = QTableWidgetItem(str(account_data[field]))
                            item.setTextAlignment(Qt.AlignCenter)
                            items_to_update[column] = item

            # 一次性更新所有单元格
            for column, item in items_to_update.items():
                self.table.setItem(row, column, item)

        except Exception as e:
            error(f"更新账号 {account_id} 的表格数据时出错: {str(e)}")

    def on_log_message(self, message, level):
        """处理日志消息信号"""
        # 根据日志级别记录日志
        if level == "ERROR":
            error(message)
        elif level == "WARNING":
            warning(message)
        elif level == "SUCCESS":
            success(message)
        else:
            info(message)

    def on_error_message(self, title, message):
        """处理错误消息信号"""
        error(f"{title}: {message}")
        # 可以选择是否显示弹窗
        # QMessageBox.critical(self, title, message)

    def on_table_update(self, row, status_text, color):
        """处理表格更新信号

        Args:
            row: 行索引
            status_text: 状态文本
            color: 状态颜色
        """
        try:
            # 在主线程中安全地更新UI
            status_item = QTableWidgetItem(status_text)
            status_item.setForeground(QBrush(color))
            status_item.setTextAlignment(Qt.AlignCenter)
            self.table.setItem(row, 2, status_item)  # 状态列

            # 强制刷新表格
            self.table.viewport().update()
        except Exception as e:
            error(f"更新表格状态时出错: {str(e)}")

    def update_account_table_status(self, row, status_text, bg_color, text_color):
        """更新账号表格状态显示

        Args:
            row: 行索引
            status_text: 状态文本
            bg_color: 背景颜色 (QColor)
            text_color: 文字颜色 (QColor)
        """
        try:
            # 确保行索引有效
            if row < 0 or row >= self.table.rowCount():
                warning(f"更新表格状态时行索引无效: {row}，表格行数: {self.table.rowCount()}")
                return

            # 创建状态项
            status_item = QTableWidgetItem(status_text)
            status_item.setTextAlignment(Qt.AlignCenter)
            status_item.setBackground(bg_color)
            status_item.setForeground(text_color)
            status_item.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))

            # 更新状态列（第2列，索引2）
            self.table.setItem(row, 2, status_item)

            # 强制刷新表格显示
            self.table.viewport().update()

            # 不再记录状态更新日志，保持日志简洁

        except Exception as e:
            error(f"更新账号表格状态时出错: {str(e)}")

    def update_account_status_by_id(self, account_id, status_text, status_type="info"):
        """根据账号ID更新状态显示

        Args:
            account_id: 账号ID
            status_text: 状态文本
            status_type: 状态类型 ("waiting", "processing", "success", "failed", "login_failed", "banned")
        """
        try:
            # 查找账号对应的行
            row = self._find_account_row_by_id(account_id)
            if row is not None:
                # 根据状态类型设置颜色
                color_map = {
                    "waiting": (QColor(255, 230, 200), QColor(153, 76, 0)),      # 浅橙色背景，深橙色文字
                    "processing": (QColor(200, 230, 255), QColor(0, 76, 153)),   # 浅蓝色背景，深蓝色文字
                    "success": (QColor(200, 255, 200), QColor(0, 128, 0)),       # 浅绿色背景，深绿色文字
                    "failed": (QColor(255, 200, 200), QColor(153, 0, 0)),        # 浅红色背景，深红色文字
                    "login_failed": (QColor(255, 180, 180), QColor(128, 0, 0)),  # 更深的红色，表示登录失败
                    "banned": (QColor(255, 165, 0), QColor(139, 69, 19))         # 橙色背景，深橙色文字，表示禁言
                }

                bg_color, text_color = color_map.get(status_type, color_map["waiting"])

                # 更新状态显示
                self.update_account_table_status(row, status_text, bg_color, text_color)
            else:
                warning(f"未找到账号 {account_id} 对应的表格行")

        except Exception as e:
            error(f"根据账号ID更新状态时出错: {str(e)}")

    def update_realname_status_by_id(self, account_id, is_verified, save_data=True):
        """根据账号ID更新实名认证状态

        Args:
            account_id: 账号ID
            is_verified: 是否已实名认证 (bool)
            save_data: 是否保存数据到文件 (bool)
        """
        try:
            # 查找账号对应的行
            row = self._find_account_row_by_id(account_id)
            if row is not None:
                # 确定实名状态文本和颜色
                if is_verified:
                    status_text = "已实名"
                    text_color = QColor(0, 150, 0)  # 绿色
                else:
                    status_text = "未实名"
                    text_color = QColor(200, 0, 0)  # 红色

                # 更新实名状态列（第14列，索引14）
                realname_item = QTableWidgetItem(status_text)
                realname_item.setTextAlignment(Qt.AlignCenter)
                realname_item.setForeground(text_color)
                realname_item.setFont(QFont("Microsoft YaHei", 9, QFont.Bold))
                self.table.setItem(row, 14, realname_item)

                # 强制刷新表格显示
                self.table.viewport().update()

                # 保存数据到文件
                if save_data:
                    self._save_realname_status_to_file(account_id, is_verified)

                info(f"✅ 已更新账号 {account_id} 的实名状态为: {status_text}")

            else:
                warning(f"未找到账号 {account_id} 对应的表格行")

        except Exception as e:
            error(f"更新账号 {account_id} 实名状态时出错: {str(e)}")

    def _save_realname_status_to_file(self, account_id, is_verified):
        """保存实名状态到账号数据文件"""
        try:
            # 加载现有账号数据
            account_data = self._load_account_data(account_id)
            if account_data is None:
                account_data = {}

            # 更新实名状态
            account_data["verification_status"] = "已实名" if is_verified else "未实名"
            account_data["is_verified"] = is_verified

            # 保存到文件
            self._save_account_data(account_id, account_data)

        except Exception as e:
            error(f"保存账号 {account_id} 实名状态到文件时出错: {str(e)}")

    def _load_account_data(self, account_id):
        """加载账号数据"""
        try:
            import json
            import os

            data_file = os.path.join("data", f"{account_id}.json")
            if os.path.exists(data_file):
                with open(data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            return None
        except Exception as e:
            debug(f"加载账号 {account_id} 数据时出错: {str(e)}")
            return None

    def _save_account_data(self, account_id, account_data):
        """保存账号数据"""
        try:
            import json
            import os

            # 确保data目录存在
            os.makedirs("data", exist_ok=True)

            data_file = os.path.join("data", f"{account_id}.json")
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(account_data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            error(f"保存账号 {account_id} 数据时出错: {str(e)}")

    def _find_account_row_by_id(self, account_id):
        """根据账号ID查找表格行索引

        Args:
            account_id: 账号ID

        Returns:
            int: 行索引，如果未找到返回None
        """
        try:
            for row in range(self.table.rowCount()):
                # 检查账号列（第1列，索引1）
                account_item = self.table.item(row, 1)
                if account_item and account_item.text().strip() == account_id:
                    return row
            return None
        except Exception as e:
            debug(f"查找账号行索引出错: {str(e)}")
            return None

    def update_total_earnings_and_signal(self):
        """计算并更新所有账号的7天总收益"""
        try:
            # 先更新总收益
            self.update_total_earnings()

            # 确保表格已经完全刷新
            if hasattr(self, 'table') and self.table:
                # 强制刷新表格
                try:
                    self.table.viewport().update()
                    # 处理挂起的事件，确保UI响应
                    QApplication.processEvents()
                except Exception as e:
                    warning(f"刷新表格视口时出错: {str(e)}")
            else:
                warning("表格未初始化，无法刷新表格")
        except Exception as e:
            error(f"更新总收益时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())

    def update_total_earnings(self):
        """计算并更新所有账号的7天总收益"""
        try:
            # 确保表格已初始化
            if not hasattr(self, 'table') or not self.table:
                return

            total_7day_income = 0.0
            total_yesterday_income = 0.0
            valid_income_count = 0
            total_rows = self.table.rowCount()

            # 检查列数是否足够
            column_count = self.table.columnCount()
            info(f"开始计算收益统计，表格行数: {total_rows}, 列数: {column_count}")

            # 遍历所有行，计算7天总收益和昨日收益
            for row in range(total_rows):  # 统计所有账号的收益

                # 获取七天收益列（索引11）的数据
                seven_day_income_item = self.table.item(row, 11)
                if seven_day_income_item and seven_day_income_item.text():
                    try:
                        # 处理可能的格式，如 "¥5.23" 或 "5.23"
                        income_str = seven_day_income_item.text().replace("¥", "").replace(",", "").strip()
                        if income_str and income_str != "--" and income_str != "":
                            income_value = float(income_str)
                            total_7day_income += income_value
                            valid_income_count += 1
                    except ValueError:
                        # 静默处理解析错误，避免日志过多
                        pass

                # 获取昨日收益列（索引10）的数据
                yesterday_income_item = self.table.item(row, 10)
                if yesterday_income_item and yesterday_income_item.text():
                    try:
                        # 处理可能的格式，如 "¥5.23" 或 "5.23" 或 "+5.23" 或 "-5.23"
                        income_str = yesterday_income_item.text().replace("¥", "").replace(",", "").strip()
                        if income_str and income_str != "--" and income_str != "":
                            # 移除可能的正负号前缀，但保留数值的正负性
                            if income_str.startswith('+'):
                                income_str = income_str[1:]
                            income_value = float(income_str)
                            total_yesterday_income += income_value
                    except ValueError:
                        # 静默处理解析错误，避免日志过多
                        pass

            # 输出统计结果
            info(f"收益统计完成 - 统计了 {valid_income_count} 个账号")
            info(f"七天总收益: ¥{total_7day_income:.2f}")
            info(f"昨日总收益: ¥{total_yesterday_income:.2f}")

            # 更新主窗口中的收益统计组件
            main_window = self.window()
            if hasattr(main_window, 'earnings_stats'):
                info(f"更新7天总收益统计: ¥{total_7day_income:.2f}")
                main_window.earnings_stats.update_earnings(total_7day_income)

                # 更新第二个收益统计组件显示昨日收益
                if hasattr(main_window, 'earnings_stats_2'):
                    info(f"更新昨日总收益统计: ¥{total_yesterday_income:.2f}")
                    main_window.earnings_stats_2.update_earnings(total_yesterday_income)
            else:
                warning("收益统计组件未初始化")

        except Exception as e:
            error(f"计算7天总收益时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())

    def on_all_threads_finished(self):
        """所有线程完成时的处理"""
        info("所有任务已完成")

        # 停止定时器，防止自动重新启动数据采集
        if hasattr(self, 'data_collection_timer') and self.data_collection_timer:
            info("停止数据采集定时器，防止自动重新启动")
            self.data_collection_timer.stop()
            self.data_collection_timer = None

        # 停止UI更新定时器
        if hasattr(self, 'ui_update_timer') and self.ui_update_timer is not None:
            try:
                if isinstance(self.ui_update_timer, QTimer):
                    self.ui_update_timer.stop()
                    info("已停止UI更新定时器")
                elif hasattr(self.ui_update_timer, 'stop'):
                    self.ui_update_timer.stop()
                    info("已停止UI更新定时器")
            except Exception as timer_err:
                error(f"停止UI更新定时器时出错: {str(timer_err)}")

            # 删除UI更新定时器
            try:
                if hasattr(self.ui_update_timer, 'deleteLater'):
                    # 不调用deleteLater()，避免Qt Fatal错误
                    # self.ui_update_timer.deleteLater()
                    info("跳过UI更新定时器的deleteLater()调用")
                self.ui_update_timer = None
                info("已清理UI更新定时器引用")
            except Exception as timer_del_err:
                error(f"删除UI更新定时器时出错: {str(timer_del_err)}")

            # 强制执行垃圾回收
            try:
                import gc
                gc.collect()
                info("已执行垃圾回收")
            except Exception as gc_err:
                error(f"执行垃圾回收时出错: {str(gc_err)}")

        # 清除数据采集器引用
        if hasattr(self, 'data_collector'):
            # 断开信号连接
            try:
                if hasattr(self.data_collector, 'disconnect'):
                    self.data_collector.disconnect()
            except Exception as e:
                error(f"断开数据采集器信号连接时出错: {str(e)}")

            # 删除对象
            try:
                if hasattr(self.data_collector, 'deleteLater'):
                    # 不调用deleteLater()，避免Qt Fatal错误
                    # self.data_collector.deleteLater()
                    info("跳过数据采集器的deleteLater()调用")
            except Exception as e:
                error(f"清理数据采集器对象时出错: {str(e)}")

            self.data_collector = None
            info("已清理数据采集器对象")

        # 执行垃圾回收和内存优化
        try:
            # 尝试使用高级内存管理器
            try:
                from app.utils.memory_manager import force_gc, get_memory_usage
                # 执行强制垃圾回收
                collected = force_gc()
                info(f"已执行高级垃圾回收，回收对象: {collected}")

                # 获取内存使用情况
                memory_info = get_memory_usage()
                if memory_info:
                    memory_mb, available_mb, total_mb = memory_info
                    info(f"当前内存使用: {memory_mb:.2f}MB, 系统可用: {available_mb:.2f}MB/{total_mb:.2f}MB")

                    # 如果内存使用仍然很高，尝试清理Chrome进程
                    if memory_mb > 1500:  # 如果内存使用超过1.5GB
                        try:
                            from app.utils.cleanup_chrome import cleanup_chrome_processes
                            killed_count, success = cleanup_chrome_processes(force=True)
                            if success and killed_count > 0:
                                info(f"由于内存使用较高，已清理 {killed_count} 个Chrome进程")
                        except Exception as chrome_err:
                            error(f"清理Chrome进程时出错: {str(chrome_err)}")
            except ImportError:
                # 如果高级内存管理器不可用，使用基本垃圾回收
                import gc
                gc.collect()
                info("已执行基本垃圾回收")
        except Exception as e:
            error(f"执行垃圾回收时出错: {str(e)}")

        # 恢复按钮状态
        self.update_button_states(is_running=False)

        # 刷新表格数据
        try:
            # 遍历所有行，更新数据
            for row in range(self.table.rowCount()):
                account_item = self.table.item(row, 1)  # 账号ID列
                if account_item:
                    account_id = account_item.text()
                    self.update_account_data_in_table(row, account_id)
            info("已刷新表格数据")
        except Exception as e:
            error(f"刷新表格数据时出错: {str(e)}")

        # 计算所有账号7天总收益
        try:
            # 使用统一的收益计算方法
            self.update_total_earnings()
        except Exception as e:
            error(f"计算7天总收益时出错: {str(e)}")
            import traceback
            error(traceback.format_exc())

        QMessageBox.information(self, "完成", "所有任务已完成")

    def clear_collected_data(self):
        """清空数据功能已移除"""
        QMessageBox.information(self, "功能已移除", "数据采集和数据保存功能已被移除")

    def get_selected_accounts(self):
        """获取选中的账号列表，用于养号等功能

        Returns:
            list: 包含选中账号信息的字典列表
        """
        selected_accounts = []

        try:
            # 如果表格不存在，返回空列表
            if not hasattr(self, 'table') or not self.table:
                return []

            rows = self.table.rowCount()

            for row in range(rows):
                # 获取复选框状态
                checkbox_item = self.table.cellWidget(row, 0)
                if checkbox_item and isinstance(checkbox_item, QCheckBox) and checkbox_item.isChecked():
                    # 获取账号信息
                    account_name = ""
                    if self.table.item(row, 0):  # 昵称列
                        account_name = self.table.item(row, 0).text()

                    account_id = ""
                    if self.table.item(row, 1):  # 账号ID列
                        account_id = self.table.item(row, 1).text()

                    cookie_file = ""
                    if row < len(self.cookie_files):
                        cookie_file = self.cookie_files[row]

                    # 创建账号信息字典
                    account_info = {
                        'row': row,
                        'account_id': account_id,
                        'account_name': account_name,
                        'cookie_file': cookie_file
                    }

                    selected_accounts.append(account_info)

            return selected_accounts

        except Exception as e:
            error(f"获取选中账号时出错: {str(e)}")
            return []

    def get_all_accounts(self):
        """获取所有账号列表，用于批量养号

        Returns:
            list: 包含所有账号信息的字典列表
        """
        all_accounts = []

        try:
            # 如果表格不存在，返回空列表
            if not hasattr(self, 'table') or not self.table:
                return []

            rows = self.table.rowCount()

            for row in range(rows):
                # 获取账号信息
                account_name = ""
                if self.table.item(row, 0):  # 昵称列
                    account_name = self.table.item(row, 0).text()

                account_id = ""
                if self.table.item(row, 1):  # 账号ID列
                    account_id = self.table.item(row, 1).text()

                cookie_file = ""
                if row < len(self.cookie_files):
                    cookie_file = self.cookie_files[row]

                # 创建账号信息字典
                account_info = {
                    'row': row,
                    'account_id': account_id,
                    'account_name': account_name,
                    'cookie_file': cookie_file
                }

                all_accounts.append(account_info)

            return all_accounts

        except Exception as e:
            error(f"获取所有账号时出错: {str(e)}")
            return []

    def export_to_excel(self):
        """导出账号数据到Excel"""
        try:
            # 检查是否有数据
            if self.table.rowCount() == 0:
                QMessageBox.warning(self, "导出失败", "没有可导出的数据")
                return

            # 提示用户选择保存位置
            # 获取当前日期作为文件名
            from datetime import datetime
            current_date = datetime.now().strftime("%Y%m%d")
            default_filename = f"{current_date}收益数据.xlsx"

            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出Excel", default_filename, "Excel文件 (*.xlsx)"
            )

            if not file_path:
                return  # 用户取消了选择

            # 使用进度对话框
            from app.widgets.progress_dialog import ProgressDialog
            progress_dialog = ProgressDialog("导出数据", self, cancelable=False)
            progress_dialog.update_progress(0, "正在准备导出数据...", "初始化中")
            progress_dialog.show()

            # 不再直接调用processEvents，避免递归事件处理

            # 使用openpyxl导出数据
            from openpyxl import Workbook
            from openpyxl.styles import Font, Alignment, PatternFill, Border, Side
            from openpyxl.utils import get_column_letter

            # 创建工作簿和工作表
            wb = Workbook()
            ws = wb.active
            ws.title = "账号数据"

            # 设置表头
            headers = []
            for col in range(self.table.columnCount() - 1):  # 减1是因为最后一列是操作按钮
                header_item = self.table.horizontalHeaderItem(col)
                if header_item:
                    headers.append(header_item.text())
                else:
                    headers.append(f"列 {col+1}")

            # 写入表头
            for col_idx, header in enumerate(headers, 1):
                cell = ws.cell(row=1, column=col_idx, value=header)
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="DDEBF7", end_color="DDEBF7", fill_type="solid")
                cell.alignment = Alignment(horizontal="center", vertical="center")
                cell.border = Border(
                    left=Side(style='thin'),
                    right=Side(style='thin'),
                    top=Side(style='thin'),
                    bottom=Side(style='thin')
                )

            # 收集数据并计算统计信息
            data = []
            total_yesterday_income = 0.0
            high_income_accounts = 0  # 昨日收益超过5元的账号数
            withdrawable_accounts = 0  # 待提现的账号数
            total_7day_income = 0.0

            # 确定收益列的索引
            yesterday_income_col = -1
            withdrawable_col = -1
            seven_day_income_col = -1

            for col in range(self.table.columnCount()):
                header_item = self.table.horizontalHeaderItem(col)
                if header_item:
                    header_text = header_item.text()
                    if "昨日收益" in header_text:
                        yesterday_income_col = col
                    elif "待提现" in header_text:
                        withdrawable_col = col
                    elif "七天收益" in header_text or "7天收益" in header_text:
                        seven_day_income_col = col

            # 写入数据行并收集统计信息
            for row in range(self.table.rowCount()):
                row_data = []
                for col in range(self.table.columnCount() - 1):  # 减1是因为最后一列是操作按钮
                    item = self.table.item(row, col)
                    cell_value = item.text() if item else ""
                    row_data.append(cell_value)

                    # 收集统计数据
                    if col == yesterday_income_col and cell_value:
                        try:
                            # 处理可能的格式，如 "¥5.23" 或 "5.23"
                            income_str = cell_value.replace("¥", "").replace(",", "").strip()
                            income_value = float(income_str)
                            total_yesterday_income += income_value
                            if income_value > 5.0:
                                high_income_accounts += 1
                        except ValueError:
                            pass

                    if col == withdrawable_col and cell_value:
                        try:
                            # 如果有非零待提现金额
                            withdraw_str = cell_value.replace("¥", "").replace(",", "").strip()
                            if withdraw_str and float(withdraw_str) > 0:
                                withdrawable_accounts += 1
                        except ValueError:
                            pass

                    if col == seven_day_income_col and cell_value:
                        try:
                            # 处理七天收益
                            income_str = cell_value.replace("¥", "").replace(",", "").strip()
                            income_value = float(income_str)
                            total_7day_income += income_value
                        except ValueError:
                            pass

                data.append(row_data)

                # 更新进度条
                progress_percent = int((row + 1) / self.table.rowCount() * 70)
                progress_dialog.update_progress(
                    progress_percent,
                    "正在处理数据...",
                    f"处理第 {row+1}/{self.table.rowCount()} 行"
                )

            # 写入数据
            for row_idx, row_data in enumerate(data, 2):
                for col_idx, cell_value in enumerate(row_data, 1):
                    ws.cell(row=row_idx, column=col_idx, value=cell_value)

            # 自动调整列宽
            for col_idx in range(1, len(headers) + 1):
                column_letter = get_column_letter(col_idx)
                ws.column_dimensions[column_letter].width = 15

            progress_dialog.update_progress(
                80,
                "正在调整列宽...",
                "即将完成"
            )

            # 创建统计信息工作表
            stats_ws = wb.create_sheet(title="收益统计")

            # 设置统计表的样式
            title_font = Font(bold=True, size=12)
            header_font = Font(bold=True)
            header_fill = PatternFill(start_color="DDEBF7", end_color="DDEBF7", fill_type="solid")
            data_fill = PatternFill(start_color="EBF1DE", end_color="EBF1DE", fill_type="solid")

            # 添加标题
            stats_ws.merge_cells('A1:D1')
            stats_ws['A1'] = f"账号收益统计 - 导出日期: {datetime.now().strftime('%Y-%m-%d')}"
            stats_ws['A1'].font = title_font
            stats_ws['A1'].alignment = Alignment(horizontal="center")

            # 统计数据行
            stats_data = [
                ["总账号数", self.table.rowCount(), "", ""],
                ["昨日总收益", f"¥{total_yesterday_income:.2f}", "", ""],
                ["昨日收益>5元的账号数", high_income_accounts, "", ""],
                ["待提现账号数", withdrawable_accounts, "", ""],
                ["七天总收益", f"¥{total_7day_income:.2f}", "", ""]
            ]

            # 写入统计数据
            for row_idx, row_data in enumerate(stats_data, 3):
                for col_idx, cell_value in enumerate(row_data, 1):
                    cell = stats_ws.cell(row=row_idx, column=col_idx, value=cell_value)
                    if col_idx == 1:
                        cell.font = header_font
                        cell.fill = header_fill
                    else:
                        cell.fill = data_fill

            # 调整统计表列宽
            stats_ws.column_dimensions['A'].width = 25
            stats_ws.column_dimensions['B'].width = 20

            progress_dialog.update_progress(
                90,
                "正在完成统计...",
                "准备保存文件"
            )

            # 保存文件
            wb.save(file_path)

            # 更新进度对话框为完成状态
            progress_dialog.update_progress(
                100,
                "导出完成",
                f"成功导出数据到: {file_path}"
            )

            # 显示成功消息
            success(f"成功导出数据到: {file_path}")

            # 短暂延迟后关闭进度对话框
            QTimer.singleShot(1500, progress_dialog.accept)

            # 显示成功消息
            QMessageBox.information(self, "导出成功", f"成功导出数据到:\n{file_path}")

        except Exception as e:
            error(f"导出数据到Excel失败: {str(e)}")
            QMessageBox.critical(self, "导出失败", f"导出失败: {str(e)}")

            # 更新进度对话框显示错误信息
            if 'progress_dialog' in locals() and progress_dialog.isVisible():
                progress_dialog.update_progress(
                    100,
                    "导出失败",
                    f"错误: {str(e)}"
                )
                # 3秒后关闭对话框
                QTimer.singleShot(3000, progress_dialog.accept)

            # 在异常情况下提供更详细的错误信息
            import traceback
            error(traceback.format_exc())

# 数据采集设置对话框已移除

class BatchSettingsDialog(QDialog):
    """批量存稿设置对话框"""
    settingsConfirmed = pyqtSignal(bool, int, int, bool, bool)  # 信号：无头模式, 并发数, 存稿次数, 视频公平分配, 清理缓存

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("批量存稿设置")
        self.setMinimumWidth(400)
        self.setMinimumHeight(280)  # 增加高度，适应新添加的控件
        self.setWindowFlags(self.windowFlags() & ~Qt.WindowContextHelpButtonHint)

        # 设置窗口样式
        self.setStyleSheet("""
            QDialog {
                background-color: #F5F5F5;
                border-radius: 10px;
            }
            QLabel {
                color: #333333;
                font-size: 13px;
                font-weight: bold;
            }
            QCheckBox {
                color: #333333;
                font-size: 13px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border: 1px solid #CCCCCC;
                border-radius: 3px;
                background-color: #FFFFFF;
            }
            QCheckBox::indicator:checked {
                background-color: #006666;
                border: 1px solid #006666;
                image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>');
            }
            QCheckBox::indicator:hover {
                border: 1px solid #006666;
            }
            QSpinBox {
                border: 1px solid #CCCCCC;
                border-radius: 5px;
                padding: 5px;
                background-color: #FFFFFF;
                min-height: 25px;
                font-size: 13px;
            }
            QSpinBox::up-button, QSpinBox::down-button {
                background-color: #EEEEEE;
                width: 20px;
                border-radius: 3px;
            }
            QSpinBox:hover {
                border: 1px solid #006666;
            }
            QPushButton {
                background-color: #006666;
                color: white;
                border-radius: 5px;
                padding: 8px 20px;
                font-weight: bold;
                min-width: 100px;
                min-height: 30px;
                font-size: 13px;
            }
            QPushButton:hover {
                background-color: #007777;
            }
            QPushButton#cancelButton {
                background-color: #AAAAAA;
            }
            QPushButton#cancelButton:hover {
                background-color: #999999;
            }
            QFrame#separator {
                background-color: #DDDDDD;
                max-height: 1px;
            }
        """)

        # 创建布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 添加标题
        title_label = QLabel("设置批量存稿参数")
        title_label.setStyleSheet("font-size: 16px; margin-bottom: 10px;")
        layout.addWidget(title_label)

        # 添加内存推荐说明
        memory_info = QLabel("【系统资源建议】\n• 8GB内存系统：建议8-12个并发\n• 16GB内存系统：建议12-20个并发\n• 32GB内存系统：建议20-30个并发")
        memory_info.setStyleSheet("background-color: #f0f7ff; padding: 8px; border-radius: 4px; border: 1px solid #d0e0ff; color: #0066cc; font-size: 11pt;")
        layout.addWidget(memory_info)

        # 添加分割线
        separator = QFrame()
        separator.setObjectName("separator")
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator)

        # 调试模式设置
        debug_layout = QHBoxLayout()
        debug_layout.setContentsMargins(0, 10, 0, 10)

        debug_icon = QLabel()
        debug_icon.setPixmap(self.style().standardIcon(QStyle.SP_ComputerIcon).pixmap(24, 24))
        debug_layout.addWidget(debug_icon)

        self.debug_checkbox = QCheckBox("启用调试模式 (显示浏览器窗口，需要密码验证)")
        self.debug_checkbox.setToolTip("启用后将显示浏览器窗口，方便调试。需要密码验证。默认为无头模式。")
        self.debug_checkbox.setChecked(False)  # 默认不选择调试模式
        debug_layout.addWidget(self.debug_checkbox)
        debug_layout.addStretch()

        layout.addLayout(debug_layout)

        # 并发数设置
        concurrent_layout = QHBoxLayout()
        concurrent_layout.setContentsMargins(0, 10, 0, 10)

        concurrent_icon = QLabel()
        concurrent_icon.setPixmap(self.style().standardIcon(QStyle.SP_BrowserReload).pixmap(24, 24))
        concurrent_layout.addWidget(concurrent_icon)

        concurrent_label = QLabel("并发数量:")
        concurrent_layout.addWidget(concurrent_label)

        self.concurrent_spinbox = QSpinBox()
        self.concurrent_spinbox.setMinimum(1)
        self.concurrent_spinbox.setMaximum(30)
        self.concurrent_spinbox.setValue(16)  # 默认设置为16，适合大多数16GB内存的系统
        self.concurrent_spinbox.setToolTip("同时处理的账号数量\n推荐并发数：\n对于8GB内存的系统：8-12个并发\n对于16GB内存的系统：12-20个并发\n对于32GB或更高内存的系统：可以尝试20-30个并发")
        self.concurrent_spinbox.setMinimumWidth(100)
        concurrent_layout.addWidget(self.concurrent_spinbox)

        # 添加推荐并发数说明标签
        concurrent_info = QLabel("推荐并发数：8GB内存(8-12)，16GB内存(12-20)，32GB内存(20-30)")
        concurrent_info.setStyleSheet("color: #666666; font-size: 9pt;")
        concurrent_layout.addWidget(concurrent_info)
        concurrent_layout.addStretch()

        layout.addLayout(concurrent_layout)

        # 存稿次数设置 - 新增控件
        draft_count_layout = QHBoxLayout()
        draft_count_layout.setContentsMargins(0, 10, 0, 10)

        draft_count_icon = QLabel()
        draft_count_icon.setPixmap(self.style().standardIcon(QStyle.SP_FileDialogDetailedView).pixmap(24, 24))
        draft_count_layout.addWidget(draft_count_icon)

        draft_count_label = QLabel("存稿次数:")
        draft_count_layout.addWidget(draft_count_label)

        self.draft_count_spinbox = QSpinBox()
        self.draft_count_spinbox.setMinimum(1)
        self.draft_count_spinbox.setMaximum(100)
        self.draft_count_spinbox.setValue(1)
        self.draft_count_spinbox.setToolTip("每个账号连续存稿的次数")
        self.draft_count_spinbox.setMinimumWidth(100)
        draft_count_layout.addWidget(self.draft_count_spinbox)
        draft_count_layout.addStretch()

        layout.addLayout(draft_count_layout)

        # 添加提示文本
        info_label = QLabel("提示: 存稿次数表示每个账号连续执行存稿的次数，第二次及以后将直接跳到视频上传界面")
        info_label.setStyleSheet("color: #777777; font-size: 12px; font-weight: normal;")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # 账号循环功能已移除

        # 循环次数提示文本已移除

        # 视频分配设置 - 保留但简化
        video_allocation_layout = QHBoxLayout()
        video_allocation_layout.setContentsMargins(0, 10, 0, 10)

        video_allocation_icon = QLabel()
        video_allocation_icon.setPixmap(self.style().standardIcon(QStyle.SP_FileDialogDetailedView).pixmap(24, 24))
        video_allocation_layout.addWidget(video_allocation_icon)

        self.video_allocation_checkbox = QCheckBox("启用视频公平分配")
        self.video_allocation_checkbox.setToolTip("启用后系统将智能分配视频，确保不重复使用，直到视频池耗尽")
        self.video_allocation_checkbox.setChecked(True)  # 默认选中
        video_allocation_layout.addWidget(self.video_allocation_checkbox)
        video_allocation_layout.addStretch()

        layout.addLayout(video_allocation_layout)

        # 清理缓存设置 - 新增
        cache_cleanup_layout = QHBoxLayout()
        cache_cleanup_layout.setContentsMargins(0, 10, 0, 10)

        cache_cleanup_icon = QLabel()
        cache_cleanup_icon.setPixmap(self.style().standardIcon(QStyle.SP_TrashIcon).pixmap(24, 24))
        cache_cleanup_layout.addWidget(cache_cleanup_icon)

        self.cache_cleanup_checkbox = QCheckBox("开始前清理浏览器缓存")
        self.cache_cleanup_checkbox.setToolTip("启用后会在任务开始前清理Chrome浏览器临时文件和缓存，可减少C盘占用")
        self.cache_cleanup_checkbox.setChecked(False)  # 默认不选中
        cache_cleanup_layout.addWidget(self.cache_cleanup_checkbox)
        cache_cleanup_layout.addStretch()

        layout.addLayout(cache_cleanup_layout)

        # 添加清理缓存的提示文本
        cache_info_label = QLabel("提示: 不勾选此选项时，系统会在任务完成后自动清理缓存。清理缓存有助于减少C盘空间占用")
        cache_info_label.setStyleSheet("color: #777777; font-size: 12px; font-weight: normal;")
        cache_info_label.setWordWrap(True)
        layout.addWidget(cache_info_label)

        # 保存设置选项 - 新增
        save_settings_layout = QHBoxLayout()
        save_settings_layout.setContentsMargins(0, 10, 0, 10)

        save_settings_icon = QLabel()
        save_settings_icon.setPixmap(self.style().standardIcon(QStyle.SP_DialogSaveButton).pixmap(24, 24))
        save_settings_layout.addWidget(save_settings_icon)

        self.save_settings_checkbox = QCheckBox("保存设置")
        self.save_settings_checkbox.setToolTip("保存当前设置供下次使用")
        self.save_settings_checkbox.setChecked(True)  # 默认选中
        save_settings_layout.addWidget(self.save_settings_checkbox)
        save_settings_layout.addStretch()

        layout.addLayout(save_settings_layout)

        # 添加分割线
        separator2 = QFrame()
        separator2.setObjectName("separator")
        separator2.setFrameShape(QFrame.HLine)
        separator2.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator2)

        # 按钮
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 10, 0, 0)
        button_layout.setSpacing(15)
        button_layout.addStretch()

        self.cancel_button = QPushButton("取消")
        self.cancel_button.setObjectName("cancelButton")
        self.cancel_button.setIcon(self.style().standardIcon(QStyle.SP_DialogCancelButton))
        self.cancel_button.clicked.connect(self.reject)

        self.ok_button = QPushButton("确定")
        self.ok_button.setIcon(self.style().standardIcon(QStyle.SP_DialogOkButton))
        self.ok_button.clicked.connect(self.on_ok_clicked)

        button_layout.addWidget(self.cancel_button)
        button_layout.addWidget(self.ok_button)

        layout.addLayout(button_layout)

        # 加载保存的设置
        self.load_saved_settings()

    def get_settings(self):
        """获取设置值"""
        # 默认为无头模式，只有勾选了调试模式才为非无头模式
        headless_mode = not self.debug_checkbox.isChecked()
        return headless_mode, self.concurrent_spinbox.value(), self.draft_count_spinbox.value(), self.video_allocation_checkbox.isChecked(), self.cache_cleanup_checkbox.isChecked()

    def on_ok_clicked(self):
        """确定按钮点击处理"""
        # 如果选择了保存设置，则保存当前设置
        if self.save_settings_checkbox.isChecked():
            self.save_current_settings()

        # 接受对话框
        self.accept()

    def save_current_settings(self):
        """保存当前设置到文件"""
        try:
            settings = {
                "headless_mode": not self.debug_checkbox.isChecked(),  # 默认为无头模式，只有勾选了调试模式才为非无头模式
                "debug_mode": self.debug_checkbox.isChecked(),  # 保存调试模式设置
                "concurrent_count": self.concurrent_spinbox.value(),
                "draft_count": self.draft_count_spinbox.value(),
                "video_allocation": self.video_allocation_checkbox.isChecked(),
                "cache_cleanup": self.cache_cleanup_checkbox.isChecked()
            }

            # 确保settings.json存在
            if not os.path.exists("settings.json"):
                with open("settings.json", 'w', encoding='utf-8') as f:
                    json.dump({}, f, ensure_ascii=False, indent=4)

            # 读取现有设置
            with open("settings.json", 'r', encoding='utf-8') as f:
                all_settings = json.load(f)

            # 更新批量存稿设置
            all_settings["batch_cunggao_settings"] = settings

            # 保存回文件
            with open("settings.json", 'w', encoding='utf-8') as f:
                json.dump(all_settings, f, ensure_ascii=False, indent=4)

        except Exception as e:
            print(f"保存批量存稿设置失败: {str(e)}")

    def load_saved_settings(self):
        """加载保存的设置"""
        try:
            if os.path.exists("settings.json"):
                with open("settings.json", 'r', encoding='utf-8') as f:
                    all_settings = json.load(f)

                if "batch_cunggao_settings" in all_settings:
                    settings = all_settings["batch_cunggao_settings"]

                    # 应用保存的设置
                    if "debug_mode" in settings:
                        self.debug_checkbox.setChecked(settings["debug_mode"])
                    elif "headless_mode" in settings:
                        # 兼容旧版本设置，headless_mode 为 true 时，debug_mode 为 false
                        self.debug_checkbox.setChecked(not settings["headless_mode"])

                    if "concurrent_count" in settings:
                        self.concurrent_spinbox.setValue(settings["concurrent_count"])

                    if "draft_count" in settings:
                        self.draft_count_spinbox.setValue(settings["draft_count"])

                    if "video_allocation" in settings:
                        self.video_allocation_checkbox.setChecked(settings["video_allocation"])

                    if "cache_cleanup" in settings:
                        self.cache_cleanup_checkbox.setChecked(settings["cache_cleanup"])

        except Exception as e:
            print(f"加载批量存稿设置失败: {str(e)}")

class SingleSettingsDialog(QDialog):
    """单个账号存稿设置对话框"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("存稿设置")
        self.resize(400, 250)

        # 应用样式
        self.setStyleSheet("""
            QDialog {
                background-color: #F5F5F5;
                border: 1px solid #DDD;
            }
            QLabel {
                font-size: 13px;
                color: #333;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #CCC;
                border-radius: 5px;
                margin-top: 1.5ex;
                padding-top: 1.5ex;
                background-color: #FFF;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                subcontrol-position: top center;
                padding: 0 5px;
                background-color: #FFF;
            }
            QPushButton {
                background-color: #2980B9;
                color: white;
                border-radius: 4px;
                padding: 8px 15px;
                font-weight: bold;
                min-width: 100px;
                min-height: 30px;
            }
            QPushButton:hover {
                background-color: #3498DB;
            }
            QPushButton[text="取消"] {
                background-color: #95A5A6;
            }
            QPushButton[text="取消"]:hover {
                background-color: #7F8C8D;
            }
            QSpinBox {
                padding: 5px;
                border: 1px solid #CCC;
                border-radius: 4px;
            }
        """)

        # 主布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)

        # 调试模式选项
        debug_group = QGroupBox("浏览器模式")
        debug_layout = QVBoxLayout(debug_group)

        self.debug_checkbox = QCheckBox("启用调试模式 (显示浏览器窗口，需要密码验证)")
        self.debug_checkbox.setChecked(False)  # 默认不选择调试模式
        self.debug_checkbox.setToolTip("启用后将显示浏览器窗口，方便调试。需要密码验证。")

        debug_layout.addWidget(self.debug_checkbox)

        layout.addWidget(debug_group)

        # 存稿次数设置
        count_group = QGroupBox("存稿次数")
        count_layout = QHBoxLayout(count_group)

        count_label = QLabel("每个账号存稿次数:")
        self.count_spinbox = QSpinBox()
        self.count_spinbox.setMinimum(1)
        self.count_spinbox.setMaximum(50)
        self.count_spinbox.setValue(1)
        self.count_spinbox.setToolTip("设置每个账号执行存稿的次数")

        count_layout.addWidget(count_label)
        count_layout.addWidget(self.count_spinbox)

        layout.addWidget(count_group)

        # 按钮
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 10, 0, 0)
        button_layout.setSpacing(15)

        button_layout.addStretch()

        cancel_button = QPushButton("取消")
        cancel_button.clicked.connect(self.reject)

        ok_button = QPushButton("确定")
        ok_button.clicked.connect(self.accept)

        button_layout.addWidget(cancel_button)
        button_layout.addWidget(ok_button)

        layout.addLayout(button_layout)
        layout.addStretch()

    def get_settings(self):
        """获取设置

        Returns:
            tuple: (headless_mode, draft_count)
        """
        # 默认为无头模式，只有勾选了调试模式才为非无头模式
        headless_mode = not self.debug_checkbox.isChecked()
        draft_count = self.count_spinbox.value()

        return headless_mode, draft_count

    def load_saved_accounts_data_async(self):
        """异步加载保存的账号数据，避免阻塞UI"""
        try:
            # 检查是否已经在加载中，避免重复加载
            if hasattr(self, 'data_loading') and self.data_loading:
                info("数据已在加载中，跳过重复加载")
                return

            info("开始异步加载保存的账号数据...")
            self.data_loading = True  # 设置加载标志

            # 创建工作线程
            from PyQt5.QtCore import QThread, QObject, pyqtSignal

            class DataLoader(QObject):
                finished = pyqtSignal()
                error = pyqtSignal(str)
                progress = pyqtSignal(str)

                def __init__(self, account_tab):
                    super().__init__()
                    self.account_tab = account_tab

                def load_data(self):
                    try:
                        self.progress.emit("正在加载账号数据...")
                        self.account_tab.load_saved_accounts_data()
                        self.progress.emit("账号数据加载完成")
                        self.finished.emit()
                    except Exception as e:
                        self.error.emit(str(e))
                    finally:
                        # 清除加载标志
                        self.account_tab.data_loading = False

            # 创建线程和工作对象
            self.data_thread = QThread()
            self.data_loader = DataLoader(self)
            self.data_loader.moveToThread(self.data_thread)

            # 连接信号
            self.data_thread.started.connect(self.data_loader.load_data)
            self.data_loader.finished.connect(self.data_thread.quit)
            # 不连接deleteLater，避免Qt Fatal错误
            # self.data_loader.finished.connect(self.data_loader.deleteLater)
            # self.data_thread.finished.connect(self.data_thread.deleteLater)

            self.data_loader.progress.connect(lambda msg: info(f"数据加载进度: {msg}"))
            self.data_loader.error.connect(lambda err: error(f"异步数据加载错误: {err}"))

            # 启动线程
            self.data_thread.start()

        except Exception as e:
            error(f"启动异步数据加载时出错: {str(e)}")
            self.data_loading = False  # 清除加载标志

    def force_refresh_account_data_async(self):
        """异步强制刷新账号数据，避免阻塞UI - 仅在手动点击时使用（增强版本，防止闪退）"""
        try:
            # 检查是否已经在刷新中，避免重复刷新
            if hasattr(self, 'refresh_loading') and self.refresh_loading:
                info("数据已在刷新中，跳过重复刷新")
                QMessageBox.information(self, "提示", "数据已在刷新中，请等待完成")
                return

            # 检查是否正在进行其他加载操作
            if hasattr(self, 'is_loading') and self.is_loading:
                warning("账号正在加载中，无法进行异步强制刷新")
                QMessageBox.information(self, "提示", "账号正在加载中，请等待完成后再试")
                return

            # 检查表格是否已初始化
            if not hasattr(self, 'table') or not self.table:
                warning("表格未初始化，无法进行异步强制刷新")
                QMessageBox.warning(self, "错误", "表格未初始化，无法进行刷新")
                return

            info("开始异步强制刷新账号数据...")
            self.refresh_loading = True  # 设置刷新标志

            # 禁用强制刷新按钮
            if hasattr(self, 'force_refresh_btn'):
                self.force_refresh_btn.setEnabled(False)
                self.force_refresh_btn.setText("异步刷新中...")

            # 创建工作线程
            from PyQt5.QtCore import QThread, QObject, pyqtSignal

            class RefreshWorker(QObject):
                finished = pyqtSignal()
                error = pyqtSignal(str)
                progress = pyqtSignal(str)

                def __init__(self, account_tab):
                    super().__init__()
                    self.account_tab = account_tab

                def refresh_data(self):
                    try:
                        self.progress.emit("正在强制刷新账号数据...")
                        self.account_tab.force_refresh_account_data()
                        self.progress.emit("强制刷新完成")
                        self.finished.emit()
                    except Exception as e:
                        self.error.emit(str(e))
                    finally:
                        # 清除刷新标志
                        self.account_tab.refresh_loading = False

            # 创建线程和工作对象
            self.refresh_thread = QThread()
            self.refresh_worker = RefreshWorker(self)
            self.refresh_worker.moveToThread(self.refresh_thread)

            # 连接信号
            self.refresh_thread.started.connect(self.refresh_worker.refresh_data)
            self.refresh_worker.finished.connect(self.refresh_thread.quit)
            # 不连接deleteLater，避免Qt Fatal错误
            # self.refresh_worker.finished.connect(self.refresh_worker.deleteLater)
            # self.refresh_thread.finished.connect(self.refresh_thread.deleteLater)

            self.refresh_worker.progress.connect(lambda msg: info(f"刷新进度: {msg}"))
            self.refresh_worker.error.connect(lambda err: error(f"异步刷新错误: {err}"))

            # 连接完成信号，重新启用按钮
            self.refresh_worker.finished.connect(self._on_async_refresh_finished)
            self.refresh_worker.error.connect(self._on_async_refresh_error)

            # 启动线程
            self.refresh_thread.start()

        except Exception as e:
            error(f"启动异步强制刷新时出错: {str(e)}")
            self.refresh_loading = False  # 清除刷新标志

            # 重新启用按钮
            if hasattr(self, 'force_refresh_btn'):
                self.force_refresh_btn.setEnabled(True)
                self.force_refresh_btn.setText("🔄 强制刷新")

    def _on_async_refresh_finished(self):
        """异步刷新完成处理"""
        try:
            info("异步强制刷新完成")
            self.refresh_loading = False

            # 重新启用按钮
            if hasattr(self, 'force_refresh_btn'):
                self.force_refresh_btn.setEnabled(True)
                self.force_refresh_btn.setText("🔄 强制刷新")

        except Exception as e:
            error(f"处理异步刷新完成时出错: {str(e)}")

    def _on_async_refresh_error(self, error_msg):
        """异步刷新错误处理"""
        try:
            error(f"异步强制刷新失败: {error_msg}")
            self.refresh_loading = False

            # 重新启用按钮
            if hasattr(self, 'force_refresh_btn'):
                self.force_refresh_btn.setEnabled(True)
                self.force_refresh_btn.setText("🔄 强制刷新")

            # 显示错误消息
            QMessageBox.critical(self, "刷新失败", f"强制刷新失败: {error_msg}")

        except Exception as e:
            error(f"处理异步刷新错误时出错: {str(e)}")

    def load_saved_accounts_data_once(self):
        """只加载一次保存的账号数据，避免重复加载"""
        try:
            # 检查是否已经加载过
            if hasattr(self, 'data_loaded_once') and self.data_loaded_once:
                info("账号数据已加载过，跳过重复加载")
                return

            info("开始加载保存的账号数据（仅一次）...")
            self.data_loaded_once = True  # 设置已加载标志

            # 调用原始的数据加载方法
            self.load_saved_accounts_data()

        except Exception as e:
            error(f"单次数据加载时出错: {str(e)}")
            self.data_loaded_once = False  # 重置标志，允许重试

    def on_cell_changed(self, row, column):
        """处理表格单元格内容改变事件

        Args:
            row: 行索引
            column: 列索引
        """
        try:
            # 只处理账号列（第1列，索引1）的编辑
            if column == 1:
                self.handle_account_name_change(row, column)
            # 可以在这里添加其他列的编辑处理

        except Exception as e:
            error(f"处理单元格编辑时出错: {str(e)}")
            QMessageBox.warning(self, "编辑失败", f"处理单元格编辑时出错: {str(e)}")

    def handle_account_name_change(self, row, column):
        """处理账号名称修改

        Args:
            row: 行索引
            column: 列索引
        """
        try:
            # 获取新的账号名称
            new_account_item = self.table.item(row, column)
            if not new_account_item:
                warning("无法获取新的账号名称")
                return

            new_account_name = new_account_item.text().strip()
            if not new_account_name:
                warning("账号名称不能为空")
                QMessageBox.warning(self, "编辑失败", "账号名称不能为空")
                return

            # 验证账号名称格式（可选）
            if not self.validate_account_name(new_account_name):
                return

            # 获取原始的cookie文件路径
            if row >= len(self.cookie_files):
                warning(f"行索引 {row} 超出cookie文件列表范围")
                return

            old_cookie_file = self.cookie_files[row]
            old_account_name = os.path.splitext(os.path.basename(old_cookie_file))[0]

            # 如果名称没有变化，直接返回
            if new_account_name == old_account_name:
                return

            # 检查新名称是否已存在
            new_cookie_file = os.path.join(os.path.dirname(old_cookie_file), f"{new_account_name}.json")
            if os.path.exists(new_cookie_file):
                warning(f"账号名称 '{new_account_name}' 已存在")
                QMessageBox.warning(self, "编辑失败", f"账号名称 '{new_account_name}' 已存在")
                # 恢复原始名称
                new_account_item.setText(old_account_name)
                return

            # 执行文件重命名
            success = self.rename_account_files(old_cookie_file, new_cookie_file, old_account_name, new_account_name)

            if success:
                # 更新内存中的文件路径
                self.cookie_files[row] = new_cookie_file

                # 更新昵称列（第0列）
                nickname_item = self.table.item(row, 0)
                if nickname_item:
                    nickname_item.setText(new_account_name)

                success(f"成功将账号 '{old_account_name}' 重命名为 '{new_account_name}'")
                info(f"账号文件重命名成功: {old_cookie_file} -> {new_cookie_file}")
            else:
                # 重命名失败，恢复原始名称
                new_account_item.setText(old_account_name)

        except Exception as e:
            error(f"处理账号名称修改时出错: {str(e)}")
            QMessageBox.critical(self, "编辑失败", f"处理账号名称修改时出错: {str(e)}")

    def validate_account_name(self, account_name):
        """验证账号名称格式

        Args:
            account_name: 账号名称

        Returns:
            bool: 是否有效
        """
        try:
            # 检查是否为空
            if not account_name or not account_name.strip():
                QMessageBox.warning(self, "编辑失败", "账号名称不能为空")
                return False

            # 检查长度
            if len(account_name) > 50:
                QMessageBox.warning(self, "编辑失败", "账号名称长度不能超过50个字符")
                return False

            # 检查非法字符
            invalid_chars = ['/', '\\', ':', '*', '?', '"', '<', '>', '|']
            for char in invalid_chars:
                if char in account_name:
                    QMessageBox.warning(self, "编辑失败", f"账号名称不能包含字符: {char}")
                    return False

            return True

        except Exception as e:
            error(f"验证账号名称时出错: {str(e)}")
            return False

    def rename_account_files(self, old_cookie_file, new_cookie_file, old_account_name, new_account_name):
        """重命名账号相关的所有文件

        Args:
            old_cookie_file: 原始cookie文件路径
            new_cookie_file: 新的cookie文件路径
            old_account_name: 原始账号名称
            new_account_name: 新的账号名称

        Returns:
            bool: 是否成功
        """
        try:
            # 1. 重命名cookie文件
            if os.path.exists(old_cookie_file):
                os.rename(old_cookie_file, new_cookie_file)
                info(f"Cookie文件重命名成功: {old_cookie_file} -> {new_cookie_file}")

            # 2. 重命名相关的数据文件（如果存在）
            try:
                data_dir = self.get_data_directory()
                old_data_file = os.path.join(data_dir, f"{old_account_name}.json")
                new_data_file = os.path.join(data_dir, f"{new_account_name}.json")

                if os.path.exists(old_data_file):
                    os.rename(old_data_file, new_data_file)
                    info(f"数据文件重命名成功: {old_data_file} -> {new_data_file}")
            except Exception as e:
                warning(f"重命名数据文件时出错: {str(e)}")

            # 3. 检查并重命名其他可能的相关文件
            try:
                cookie_dir = os.path.dirname(old_cookie_file)

                # 检查是否有.txt格式的cookie文件
                old_txt_file = os.path.join(cookie_dir, f"{old_account_name}.txt")
                new_txt_file = os.path.join(cookie_dir, f"{new_account_name}.txt")

                if os.path.exists(old_txt_file):
                    os.rename(old_txt_file, new_txt_file)
                    info(f"TXT文件重命名成功: {old_txt_file} -> {new_txt_file}")

            except Exception as e:
                warning(f"重命名TXT文件时出错: {str(e)}")

            return True

        except Exception as e:
            error(f"重命名账号文件时出错: {str(e)}")
            QMessageBox.critical(self, "重命名失败", f"重命名账号文件时出错: {str(e)}")

            # 尝试回滚已经重命名的文件
            try:
                if os.path.exists(new_cookie_file) and not os.path.exists(old_cookie_file):
                    os.rename(new_cookie_file, old_cookie_file)
                    warning("已回滚Cookie文件重命名")
            except Exception as rollback_error:
                error(f"回滚文件重命名时出错: {str(rollback_error)}")

            return False

    def on_cell_double_clicked_for_edit(self, row, column):
        """处理单元格双击事件，控制编辑权限

        Args:
            row: 行索引
            column: 列索引
        """
        try:
            # 只允许编辑账号列（第1列，索引1）
            if column == 1:
                # 获取当前单元格项
                item = self.table.item(row, column)
                if item:
                    # 设置单元格为可编辑
                    item.setFlags(item.flags() | Qt.ItemIsEditable)
                    info(f"允许编辑账号列，行: {row}")
                else:
                    warning(f"无法获取单元格项，行: {row}, 列: {column}")
            else:
                # 其他列不允许编辑
                item = self.table.item(row, column)
                if item:
                    # 移除编辑标志
                    item.setFlags(item.flags() & ~Qt.ItemIsEditable)
                    info(f"禁止编辑非账号列，行: {row}, 列: {column}")

        except Exception as e:
            error(f"处理单元格双击编辑权限时出错: {str(e)}")

    def setup_table_edit_permissions(self):
        """设置表格的编辑权限"""
        try:
            for row in range(self.table.rowCount()):
                for column in range(self.table.columnCount()):
                    item = self.table.item(row, column)
                    if item:
                        if column == 1:  # 账号列可编辑
                            item.setFlags(item.flags() | Qt.ItemIsEditable)
                        else:  # 其他列不可编辑
                            item.setFlags(item.flags() & ~Qt.ItemIsEditable)

        except Exception as e:
            error(f"设置表格编辑权限时出错: {str(e)}")

