#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
头条账号登录工具 - 通过cookie登录头条媒体平台
重构版本：专注于登录验证，不包含页面跳转功能
"""

import os
import json
import time
import traceback
import random
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from app.utils.logger import logger, info, success, error, warning, debug

# 尝试导入浏览器代理管理器
try:
    from app.utils.browser_proxy import BrowserProxyManager
except ImportError:
    BrowserProxyManager = None

class BrowserInstanceManager:
    """浏览器实例管理器，防止浏览器被意外关闭"""

    _instance = None
    _active_browsers = {}

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(BrowserInstanceManager, cls).__new__(cls)
        return cls._instance

    def add_browser(self, account_id, driver):
        """添加浏览器实例到管理器"""
        try:
            self._active_browsers[account_id] = driver
            info(f"账号 {account_id} 的浏览器实例已添加到管理器，当前管理 {len(self._active_browsers)} 个浏览器")
        except Exception as e:
            warning(f"添加浏览器实例失败: {str(e)}")

    def remove_browser(self, account_id):
        """从管理器中移除浏览器实例"""
        try:
            if account_id in self._active_browsers:
                # 释放九宫格窗口位置
                try:
                    from app.utils.browser_window_layout_manager import release_browser_window_position
                    release_browser_window_position(account_id)
                except Exception as layout_error:
                    warning(f"释放窗口位置时出错: {str(layout_error)}")

                del self._active_browsers[account_id]
                info(f"账号 {account_id} 的浏览器实例已从管理器移除，当前管理 {len(self._active_browsers)} 个浏览器")
        except Exception as e:
            warning(f"移除浏览器实例失败: {str(e)}")

    def get_browser(self, account_id):
        """获取指定账号的浏览器实例"""
        return self._active_browsers.get(account_id)

    def cleanup_all(self):
        """清理所有浏览器实例"""
        try:
            for account_id, driver in list(self._active_browsers.items()):
                try:
                    driver.quit()
                except Exception:
                    pass
            self._active_browsers.clear()
            info("所有浏览器实例已清理")
        except Exception as e:
            warning(f"清理浏览器实例失败: {str(e)}")


def login_with_cookie(cookie_file, use_fingerprint=False, fingerprint_settings=None,
                     mobile_mode=False, disable_js=False, clear_cookies=True,
                     navigate_to_upload=False):
    """
    使用Cookie登录头条媒体平台（使用统一登录管理器）

    专注于登录验证功能，可选择是否导航到上传页面
    默认登录成功后停留在头条媒体平台首页

    Args:
        cookie_file (str): Cookie文件路径
        use_fingerprint (bool): 是否使用指纹浏览器
        fingerprint_settings (dict): 指纹浏览器设置
        mobile_mode (bool): 是否启用移动端模式
        disable_js (bool): 是否禁用JavaScript
        clear_cookies (bool): 是否清除现有Cookie
        navigate_to_upload (bool): 是否导航到视频上传页面

    Returns:
        tuple: (success_flag, message)
    """
    driver = None
    
    try:
        info("🚀 开始Cookie登录流程（重构版本）")
        
        # 1. 提取账号ID
        account_id = os.path.splitext(os.path.basename(cookie_file))[0]

        # 2. 配置浏览器
        chrome_options = _create_chrome_options(
            use_fingerprint, fingerprint_settings,
            mobile_mode, disable_js, clear_cookies
        )

        # 3. 创建浏览器实例
        from app.utils.chromedriver_manager import create_chrome_driver
        driver = create_chrome_driver(chrome_options)

        # 4. 使用统一登录管理器执行登录
        from app.utils.unified_login_manager import get_login_manager

        login_manager = get_login_manager()
        success_flag, message = login_manager.login_account(driver, cookie_file, account_id)

        if success_flag:
            # 5. 检测实名认证状态
            try:
                from app.utils.realname_detector import detect_realname_status
                is_verified, detection_message = detect_realname_status(driver, account_id)

                # 更新账号表格中的实名状态
                try:
                    # 尝试通过全局变量获取主窗口
                    import app.main_window
                    if hasattr(app.main_window, '_main_window_instance') and app.main_window._main_window_instance:
                        main_window = app.main_window._main_window_instance
                        if hasattr(main_window, 'account_tab'):
                            main_window.account_tab.update_realname_status_by_id(account_id, is_verified)
                            info(f"📋 已更新账号 {account_id} 实名状态: {'已实名' if is_verified else '未实名'}")
                except Exception as e:
                    warning(f"更新账号表格实名状态失败: {str(e)}")

                # 记录检测结果
                if is_verified:
                    info(f"✅ 账号 {account_id} 实名认证状态: 已实名")
                else:
                    warning(f"⚠️ 账号 {account_id} 实名认证状态: 未实名 - {detection_message}")

            except Exception as e:
                warning(f"检测实名状态时出错: {str(e)}")

            # 6. 可选：导航到视频上传页面
            final_message = f"账号 {account_id} 登录成功"
            if navigate_to_upload:
                upload_result = _navigate_to_upload_page(driver, account_id)
                if upload_result:
                    final_message += "，已导航到视频上传页面"
                else:
                    final_message += "，但导航到上传页面失败"
            else:
                final_message += "，已停留在头条媒体平台首页"

            # 应用九宫格窗口布局
            try:
                from app.utils.browser_window_layout_manager import apply_browser_window_layout
                layout_applied = apply_browser_window_layout(driver, account_id)
                if layout_applied:
                    final_message += "，已应用九宫格窗口布局"
                else:
                    debug("未应用九宫格窗口布局（可能已禁用或无可用位置）")
            except Exception as layout_error:
                warning(f"应用九宫格窗口布局时出错: {str(layout_error)}")

            # 添加到浏览器管理器
            browser_manager = BrowserInstanceManager()
            browser_manager.add_browser(account_id, driver)

            success(f"✅ {final_message}")
            return True, final_message
        else:
            error(f"❌ 登录失败: {message}")
            if driver:
                driver.quit()
            return False, message
            
    except Exception as e:
        error(f"登录过程中出错: {str(e)}")
        error(traceback.format_exc())
        if driver:
            driver.quit()
        return False, f"登录过程中出错: {str(e)}"


def _validate_cookie_file(cookie_file):
    """验证Cookie文件是否存在且有效"""
    try:
        if not os.path.exists(cookie_file):
            error(f"Cookie文件不存在: {cookie_file}")
            return False
        
        if os.path.getsize(cookie_file) == 0:
            error(f"Cookie文件为空: {cookie_file}")
            return False
        
        info(f"✅ Cookie文件验证通过: {cookie_file}")
        return True
        
    except Exception as e:
        error(f"验证Cookie文件时出错: {str(e)}")
        return False


def _read_cookie_data(cookie_file):
    """读取Cookie数据"""
    try:
        with open(cookie_file, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        
        if not content:
            error("Cookie文件内容为空")
            return None
        
        # 尝试解析JSON格式
        try:
            cookie_data = json.loads(content)
            info("✅ 成功解析JSON格式Cookie")
            return cookie_data
        except json.JSONDecodeError:
            # 尝试解析文本格式
            if "=" in content:
                info("✅ 检测到文本格式Cookie")
                return {"text_format": content}
            else:
                error("无法识别的Cookie格式")
                return None
                
    except Exception as e:
        error(f"读取Cookie数据时出错: {str(e)}")
        return None


def _extract_account_info(cookie_data):
    """从Cookie数据中提取账号信息"""
    try:
        account_id = "unknown"
        account_remark = "未知账号"
        
        if isinstance(cookie_data, dict):
            # 尝试从不同字段提取账号信息
            account_id = cookie_data.get('account_id', 
                         cookie_data.get('uid', 
                         cookie_data.get('user_id', 'unknown')))
            account_remark = cookie_data.get('account_remark', 
                            cookie_data.get('nickname', 
                            cookie_data.get('username', '未知账号')))
        
        info(f"📋 账号信息: {account_remark}({account_id})")
        return str(account_id), str(account_remark)
        
    except Exception as e:
        warning(f"提取账号信息失败: {str(e)}")
        return "unknown", "未知账号"


def _create_chrome_options(use_fingerprint=False, fingerprint_settings=None, 
                          mobile_mode=False, disable_js=False, clear_cookies=True):
    """创建Chrome浏览器选项"""
    try:
        info("🔧 配置Chrome浏览器选项...")
        
        # 根据参数决定是否使用指纹浏览器
        if use_fingerprint and fingerprint_settings and BrowserProxyManager is not None:
            info("使用指纹浏览器配置")
            
            # 创建指纹浏览器设置
            proxy_fingerprint_settings = {
                'enable_fingerprint': True,
                'enable_random_ua': fingerprint_settings.get('enable_random_ua', True),
                'device_type': fingerprint_settings.get('device_type', '自动随机'),
                'browser_type': fingerprint_settings.get('browser_type', '自动随机'),
                'resolution_type': fingerprint_settings.get('resolution_type', '随机分辨率'),
                'pixel_ratio': fingerprint_settings.get('pixel_ratio', '随机'),
                'browser_language': fingerprint_settings.get('browser_language', '中文'),
                'randomize_canvas': fingerprint_settings.get('randomize_canvas', True),
                'disable_webrtc': fingerprint_settings.get('disable_webrtc', True)
            }
            
            # 创建代理管理器并配置Chrome选项
            proxy_manager = BrowserProxyManager(proxy_fingerprint_settings)
            chrome_options = proxy_manager.configure_chrome_options(headless=False)
            
            if mobile_mode:
                info("已启用移动端模拟模式")
            info(f"使用随机User-Agent: {proxy_manager.get_random_user_agent()}")
            
        else:
            # 使用与批量存稿完全相同的标准浏览器配置
            info("使用与批量存稿相同的标准浏览器配置")
            chrome_options = Options()

            # 完全照搬批量存稿的Chrome参数
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--disable-extensions")

            # 设置独立的用户数据目录（使用软件目录缓存）
            try:
                from app.utils.cache_manager import get_cache_manager
                cache_manager = get_cache_manager()
                import time
                custom_user_dir = cache_manager.get_user_data_dir(f"single_{int(time.time())}")
                chrome_options.add_argument(f"--user-data-dir={custom_user_dir}")
                info(f"使用软件目录缓存: {custom_user_dir}")
            except Exception as e:
                # 备用方案：使用临时目录
                import tempfile
                import os
                import time
                custom_user_dir = os.path.join(tempfile.gettempdir(), f"chrome_user_data_single_{int(time.time())}")
                chrome_options.add_argument(f"--user-data-dir={custom_user_dir}")
                info(f"使用临时目录（备用）: {custom_user_dir}")

            # 移动模式设置（简化版）
            if mobile_mode:
                info("启用移动端模拟")
                mobile_emulation = {
                    "deviceMetrics": {
                        "width": 375,
                        "height": 812,
                        "pixelRatio": 3.0
                    },
                    "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1"
                }
                chrome_options.add_experimental_option("mobileEmulation", mobile_emulation)

            # 应用禁用JavaScript设置
            if disable_js:
                info("已禁用JavaScript")
                chrome_options.add_experimental_option("prefs", {"profile.managed_default_content_settings.javascript": 2})
        
        info("✅ Chrome浏览器选项配置完成")
        return chrome_options

    except Exception as e:
        error(f"配置Chrome选项时出错: {str(e)}")
        # 返回基本配置
        return Options()


def _perform_cookie_login(driver, cookie_data, account_id, account_remark):
    """执行Cookie登录操作"""
    try:
        info(f"🔐 开始为账号 {account_id} 执行Cookie登录...")

        # 根据用户设置决定是否清除Cookie和缓存
        info("清除浏览器Cookie和缓存...")
        try:
            # 清除所有Cookie
            driver.delete_all_cookies()

            # 打开空白页面
            driver.get("about:blank")

            # 清除缓存和存储
            driver.execute_script("""
                try {
                    // 清除localStorage
                    if (window.localStorage) {
                        localStorage.clear();
                    }

                    // 清除sessionStorage
                    if (window.sessionStorage) {
                        sessionStorage.clear();
                    }

                    console.log('浏览器缓存清理完成');
                } catch (e) {
                    console.error('清除缓存失败:', e);
                }
            """)

            info("浏览器Cookie和缓存清理完成")
        except Exception as e:
            warning(f"清除浏览器Cookie和缓存时出错: {str(e)}")

        # 打开头条媒体平台
        info("正在打开头条媒体平台...")
        driver.get("https://mp.toutiao.com/profile_v4/index")

        # 等待页面加载
        try:
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
        except Exception:
            warning("页面加载超时，继续执行...")

        # 加载Cookie到浏览器
        return _load_cookies_to_browser(driver, cookie_data)

    except Exception as e:
        error(f"执行Cookie登录时出错: {str(e)}")
        return False


def _load_cookies_to_browser(driver, cookie_data):
    """将Cookie数据加载到浏览器"""
    try:
        info("📥 开始加载Cookie到浏览器...")

        if isinstance(cookie_data, dict) and "text_format" in cookie_data:
            # 处理文本格式Cookie
            cookie_content = cookie_data["text_format"]
            info("处理文本格式Cookie")

            if "=" in cookie_content:
                for line in cookie_content.split("\n"):
                    if "=" in line:
                        try:
                            name, value = line.split("=", 1)
                            driver.add_cookie({
                                'name': name.strip(),
                                'value': value.strip(),
                                'domain': '.toutiao.com'
                            })
                        except Exception as e:
                            debug(f"添加文本cookie失败: {str(e)}")
            else:
                error("无法解析的文本格式Cookie")
                return False

        elif isinstance(cookie_data, list):
            # 处理列表格式Cookie
            info("处理列表格式Cookie")
            for cookie in cookie_data:
                try:
                    if isinstance(cookie, dict):
                        # 确保域名正确
                        if 'domain' not in cookie or not cookie['domain']:
                            cookie['domain'] = '.toutiao.com'
                        driver.add_cookie(cookie)
                except Exception as e:
                    debug(f"添加列表cookie出错: {str(e)[:100]}")

        elif isinstance(cookie_data, dict):
            # 处理字典格式Cookie
            if "cookies" in cookie_data:
                cookies_field = cookie_data["cookies"]

                if isinstance(cookies_field, list):
                    info("处理嵌套列表格式Cookie")
                    for cookie in cookies_field:
                        try:
                            if isinstance(cookie, dict):
                                if 'domain' not in cookie or not cookie['domain']:
                                    cookie['domain'] = '.toutiao.com'
                                driver.add_cookie(cookie)
                        except Exception as e:
                            debug(f"添加嵌套cookie出错: {str(e)[:100]}")

                elif isinstance(cookies_field, dict):
                    info("处理嵌套字典格式Cookie")
                    for name, value in cookies_field.items():
                        cookie = {
                            'name': name,
                            'value': value,
                            'domain': '.toutiao.com'
                        }
                        try:
                            driver.add_cookie(cookie)
                        except Exception as e:
                            debug(f"添加cookie {name} 失败: {str(e)}")
            else:
                # 直接使用字典键值对
                info("处理普通字典格式Cookie")
                for name, value in cookie_data.items():
                    if name not in ['account_id', 'account_remark', 'text_format']:
                        cookie = {
                            'name': name,
                            'value': value,
                            'domain': '.toutiao.com'
                        }
                        try:
                            driver.add_cookie(cookie)
                        except Exception as e:
                            debug(f"添加cookie {name} 失败: {str(e)}")
        else:
            error("无法识别的Cookie格式")
            return False

        # 刷新页面应用Cookie
        info("刷新页面应用Cookie...")
        driver.refresh()

        # 等待页面加载
        try:
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
        except Exception:
            warning("页面刷新后加载超时...")

        info("✅ Cookie加载完成")
        return True

    except Exception as e:
        error(f"加载Cookie时出错: {str(e)}")
        return False


def _verify_login_status(driver, account_id):
    """验证登录状态"""
    try:
        info(f"🔍 验证账号 {account_id} 的登录状态...")

        # 等待页面加载完成
        try:
            WebDriverWait(driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
        except Exception:
            warning("页面加载超时，继续验证...")

        # 检查登录状态的多种方式
        login_indicators = [
            # 1. 检查URL是否包含登录后的特征
            lambda: "profile_v4" in driver.current_url and "login" not in driver.current_url.lower(),

            # 2. 检查页面标题
            lambda: "头条号" in driver.title or "创作者" in driver.title,

            # 3. 检查是否存在用户信息元素
            lambda: _check_user_elements_present(driver),

            # 4. 检查Cookie中的关键登录信息
            lambda: _check_login_cookies(driver),
        ]

        # 执行验证检查
        success_count = 0
        for i, check in enumerate(login_indicators, 1):
            try:
                if check():
                    success_count += 1
                    debug(f"✅ 登录验证 {i} 通过")
                else:
                    debug(f"❌ 登录验证 {i} 失败")
            except Exception as e:
                debug(f"⚠️ 登录验证 {i} 异常: {str(e)}")

        # 判断登录是否成功（至少通过2个验证）
        is_logged_in = success_count >= 2

        if is_logged_in:
            success(f"✅ 登录状态验证成功 ({success_count}/4 项通过)")
        else:
            warning(f"❌ 登录状态验证失败 ({success_count}/4 项通过)")

        return is_logged_in

    except Exception as e:
        error(f"验证登录状态时出错: {str(e)}")
        return False


def _check_user_elements_present(driver):
    """检查页面中是否存在用户相关元素"""
    try:
        # 常见的用户信息元素选择器
        user_selectors = [
            "[class*='user']",
            "[class*='avatar']",
            "[class*='profile']",
            "[class*='account']",
            "img[src*='avatar']",
            ".header-user",
            ".user-info",
            ".profile-info"
        ]

        for selector in user_selectors:
            try:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                if elements:
                    debug(f"找到用户元素: {selector}")
                    return True
            except Exception:
                continue

        return False

    except Exception:
        return False


def _check_login_cookies(driver):
    """检查关键的登录Cookie是否存在"""
    try:
        cookies = driver.get_cookies()
        cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}

        # 关键的登录Cookie
        required_cookies = ['sessionid', 'sid_guard', 'sid_tt', 'uid_tt']

        found_cookies = 0
        for cookie_name in required_cookies:
            if cookie_name in cookie_dict and cookie_dict[cookie_name]:
                found_cookies += 1
                debug(f"✅ 找到关键Cookie: {cookie_name}")
            else:
                debug(f"❌ 缺少关键Cookie: {cookie_name}")

        # 至少需要3个关键Cookie
        return found_cookies >= 3

    except Exception:
        return False


def _navigate_to_upload_page(driver, account_id):
    """导航到视频上传页面并处理可能的网络错误"""
    try:
        info(f"🔄 正在为账号 {account_id} 导航到视频上传页面...")

        # 多重策略尝试导航
        max_retries = 3
        for attempt in range(max_retries):
            try:
                if attempt > 0:
                    info(f"第 {attempt + 1} 次尝试导航...")

                # 策略1: 先尝试直接导航
                if attempt == 0:
                    driver.get("https://mp.toutiao.com/profile_v4/xigua/upload-video")
                # 策略2: 通过首页再导航
                elif attempt == 1:
                    info("尝试通过首页导航...")
                    driver.get("https://mp.toutiao.com/profile_v4/index")
                    time.sleep(2)
                    driver.get("https://mp.toutiao.com/profile_v4/xigua/upload-video")
                # 策略3: 添加额外的请求头
                else:
                    info("尝试使用JavaScript导航...")
                    driver.execute_script("window.location.href = 'https://mp.toutiao.com/profile_v4/xigua/upload-video';")

                # 等待页面加载
                time.sleep(5)

                # 等待页面元素加载完成
                try:
                    WebDriverWait(driver, 20).until(
                        EC.presence_of_element_located((By.TAG_NAME, "body"))
                    )
                except Exception:
                    warning("页面加载超时，但继续执行...")

                # 检查是否出现网络错误
                network_error_detected = False
                try:
                    # 检查页面是否包含网络错误信息
                    page_text = driver.execute_script("return document.body.innerText;")
                    if "网络错误" in page_text or "加载数据失败" in page_text or "网络异常" in page_text:
                        warning(f"检测到网络错误，第 {attempt + 1} 次尝试失败")
                        network_error_detected = True

                        # 如果不是最后一次尝试，继续重试
                        if attempt < max_retries - 1:
                            warning("等待后重试...")
                            time.sleep(3)
                            continue
                        else:
                            error("所有导航策略都失败，网络错误持续存在")
                            return False
                    else:
                        info("页面加载正常，未检测到网络错误")

                except Exception as e:
                    warning(f"检查网络错误时出现异常: {str(e)}")

                # 如果没有网络错误，验证URL
                if not network_error_detected:
                    current_url = driver.current_url
                    if "upload-video" in current_url:
                        info(f"✅ 成功导航到视频上传页面: {current_url}")
                        return True
                    elif "mp.toutiao.com" in current_url:
                        warning(f"导航到头条平台但不是上传页面: {current_url}")
                        # 如果在头条平台但不是上传页面，尝试再次导航
                        if attempt < max_retries - 1:
                            continue
                        else:
                            warning("无法到达上传页面，但已在头条平台")
                            return False
                    else:
                        warning(f"导航后URL不正确: {current_url}")
                        if attempt < max_retries - 1:
                            continue
                        else:
                            return False

            except Exception as e:
                warning(f"第 {attempt + 1} 次导航尝试失败: {str(e)}")
                if attempt < max_retries - 1:
                    time.sleep(2)
                    continue
                else:
                    error(f"所有导航尝试都失败: {str(e)}")
                    return False

        return False

    except Exception as e:
        error(f"导航到视频上传页面失败: {str(e)}")
        return False


def _batch_style_login(driver, cookie_file, account_id):
    """
    完全照搬批量存稿的登录逻辑 - 确保100%成功
    """
    try:
        info(f"🔑 使用批量存稿完整策略登录账号 {account_id}")

        # 完全照搬批量存稿的逻辑
        if not os.path.exists(cookie_file):
            error(f"❌ 账号Cookie文件不存在")
            return False

        # 使用与批量存稿相同的URL - 直接访问头条媒体平台
        info("🌐 正在打开头条媒体平台...")
        driver.get("https://mp.toutiao.com/profile_v4/index")
        time.sleep(2)

        # 读取Cookie文件内容
        with open(cookie_file, 'r', encoding='utf-8') as f:
            cookie_content = f.read().strip()

        if not cookie_content:
            error("❌ 账号Cookie为空")
            return False

        info(f"🔑 正在加载Cookie文件，大小: {len(cookie_content)} 字节")

        # 使用与批量存稿相同的Cookie加载策略
        cookies_added = 0
        try:
            cookies = json.loads(cookie_content)

            # 检查是否为加密文件
            if isinstance(cookies, dict) and cookies.get("encrypted", False):
                info("🔐 检测到加密Cookie文件，开始解密...")
                try:
                    from app.utils.cookie_encryption import get_cookie_encryptor
                    encryptor = get_cookie_encryptor()

                    # 检查加密功能是否可用
                    if not encryptor.available:
                        error("❌ cryptography库未安装，无法解密Cookie文件")
                        error("请运行: pip install cryptography==42.0.5")
                        return False

                    # 解密Cookie数据
                    encrypted_data = cookies.get("data", "")
                    if not encrypted_data:
                        error("❌ 加密文件中没有找到数据")
                        return False

                    cookies = encryptor.decrypt_cookie_data(encrypted_data)
                    info("✅ Cookie文件解密成功")

                except Exception as decrypt_error:
                    error(f"❌ 解密Cookie文件失败: {str(decrypt_error)}")
                    return False

            # 关键Cookie列表（与批量存稿保持一致）
            essential_cookies = ['passport_csrf_token', 'sessionid', 'sid_guard', 'sid_tt', 'uid_tt']

            # 处理不同的JSON格式
            if isinstance(cookies, dict):
                # 检查是否有cookies字段（完整格式）
                if 'cookies' in cookies:
                    cookies_to_add = cookies['cookies']

                    if isinstance(cookies_to_add, dict):
                        # cookies字段是字典格式 - 使用优化的加载策略
                        for name, value in cookies_to_add.items():
                            # 只添加关键Cookie或者总数少于5个时添加所有Cookie
                            if len(cookies_to_add) <= 5 or name in essential_cookies:
                                cookie_dict = {
                                    'name': name,
                                    'value': str(value),
                                    'domain': '.toutiao.com'
                                }
                                try:
                                    driver.add_cookie(cookie_dict)
                                    cookies_added += 1
                                except Exception:
                                    # 简化错误处理，减少日志输出
                                    pass
                    elif isinstance(cookies_to_add, list):
                        # cookies字段是列表格式 - 使用优化的加载策略
                        for cookie in cookies_to_add:
                            if "name" in cookie and "value" in cookie:
                                # 只添加关键Cookie或者总数少于5个时添加所有Cookie
                                if len(cookies_to_add) <= 5 or cookie["name"] in essential_cookies:
                                    try:
                                        # 确保域名正确
                                        if 'domain' not in cookie or not cookie['domain']:
                                            cookie['domain'] = '.toutiao.com'
                                        driver.add_cookie(cookie)
                                        cookies_added += 1
                                    except Exception:
                                        # 简化错误处理，减少日志输出
                                        pass
                else:
                    # 直接是字典格式的cookies - 使用优化的加载策略
                    for name, value in cookies.items():
                        # 只添加关键Cookie或者总数少于5个时添加所有Cookie
                        if len(cookies) <= 5 or name in essential_cookies:
                            cookie_dict = {
                                'name': name,
                                'value': str(value),
                                'domain': '.toutiao.com'
                            }
                            try:
                                driver.add_cookie(cookie_dict)
                                cookies_added += 1
                            except Exception:
                                # 简化错误处理，减少日志输出
                                pass

            # 如果是列表格式，使用优化的加载策略
            elif isinstance(cookies, list):
                for cookie in cookies:
                    if "name" in cookie and "value" in cookie:
                        # 只添加关键Cookie或者总数少于5个时添加所有Cookie
                        if len(cookies) <= 5 or cookie["name"] in essential_cookies:
                            try:
                                # 确保域名正确
                                if 'domain' not in cookie or not cookie['domain']:
                                    cookie['domain'] = '.toutiao.com'
                                driver.add_cookie(cookie)
                                cookies_added += 1
                            except Exception:
                                # 简化错误处理，减少日志输出
                                pass

        except json.JSONDecodeError:
            # 如果不是JSON格式，尝试按行解析（文本格式）
            info("🔄 尝试解析文本格式Cookie...")

            # 处理分号分隔的格式
            if ';' in cookie_content:
                pairs = cookie_content.split(';')
            else:
                pairs = cookie_content.split('\n')

            for pair in pairs:
                if '=' in pair:
                    try:
                        name, value = pair.split('=', 1)
                        cookie_dict = {
                            'name': name.strip(),
                            'value': value.strip(),
                            'domain': '.toutiao.com',
                            'path': '/'
                        }
                        driver.add_cookie(cookie_dict)
                        cookies_added += 1
                    except Exception as e:
                        debug(f"添加文本cookie失败: {str(e)}")
                        continue

        if cookies_added == 0:
            error("❌ 未能添加任何Cookie")
            return False

        info(f"✅ 已添加 {cookies_added} 个Cookie")

        # 刷新页面验证登录
        info("🔄 刷新页面验证登录状态...")
        driver.refresh()

        # 使用固定的操作间隔时间
        time.sleep(3)

        # 使用与批量存稿相同的验证逻辑
        try:
            login_success = False
            max_wait_time = 20  # 减少到20秒，但增加检查频率
            start_time = time.time()
            check_interval = 0.5  # 每0.5秒检查一次

            # 尝试多种方式验证登录成功（与批量存稿保持一致）
            while time.time() - start_time < max_wait_time:
                try:
                    # 优先检查最可靠的方法：检查页面元素
                    try:
                        # 尝试查找头条后台特有的元素（使用更精确的选择器）
                        elements = driver.find_elements("css selector", ".byte-menu-item-title")
                        if elements and len(elements) > 0:
                            login_success = True
                            info("✅ 找到头条后台特有元素")
                            break
                    except Exception:
                        pass

                    # 方法2: 检查URL
                    if "/profile_v4/index" in driver.current_url and "login" not in driver.current_url:
                        # 再次确认是否有登录按钮
                        try:
                            from selenium.webdriver.common.by import By
                            login_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), '登录') or contains(text(), '登陆')]")
                            if not login_buttons or len(login_buttons) == 0:
                                login_success = True
                                info("✅ URL验证成功，未发现登录按钮")
                                break
                        except Exception:
                            login_success = True
                            info("✅ URL验证成功")
                            break

                    # 方法3: 检查是否存在登录按钮（如果存在，说明未登录）
                    try:
                        from selenium.webdriver.common.by import By
                        login_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), '登录') or contains(text(), '登陆')]")
                        if login_buttons and len(login_buttons) > 0:
                            # 如果已经过了5秒还看到登录按钮，可能是Cookie无效
                            if time.time() - start_time > 5:
                                error("❌ 检测到登录按钮，Cookie可能已过期")
                                break
                    except Exception:
                        pass

                except Exception:
                    pass

                # 减少等待时间，增加检查频率
                time.sleep(check_interval)

            if login_success:
                info("✅ Cookie登录验证成功")
                return True
            else:
                error("❌ 账号Cookie过期")
                return False

        except Exception as e:
            error(f"登录验证过程出错: {str(e)}")
            error(f"❌ 登录验证异常: {str(e)}")
            return False

    except Exception as e:
        error(f"登录过程中出错: {str(e)}")
        return False


if __name__ == "__main__":
    # 测试代码
    cookie_file = "path/to/your/cookie.txt"
    success_flag, message = login_with_cookie(cookie_file)
    print(f"结果: {success_flag}")
    print(f"消息: {message}")
