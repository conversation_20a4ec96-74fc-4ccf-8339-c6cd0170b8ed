[44064:32688:0802/215927.876:INFO:components\enterprise\browser\controller\chrome_browser_cloud_management_controller.cc:202] No machine level policy manager exists.
[34780:27812:0802/215928.064:INFO:third_party\webrtc\rtc_base\cpu_info.cc:57] Available number of cores: 16
[58888:27220:0802/215928.064:INFO:third_party\webrtc\rtc_base\cpu_info.cc:57] Available number of cores: 16
[5972:22296:0802/215928.217:VERBOSE1:components\viz\service\main\viz_main_impl.cc:86] VizNullHypothesis is disabled (not a warning)
[26320:21452:0802/215928.386:INFO:third_party\webrtc\rtc_base\cpu_info.cc:57] Available number of cores: 16
[42584:67976:0802/215928.396:INFO:third_party\webrtc\rtc_base\cpu_info.cc:57] Available number of cores: 16
[5972:22296:0802/215929.154:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0802F0054180000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[44064:32688:0802/215929.248:INFO:CONSOLE:3] "[GroupMarkerNotSet(crbug.com/242999)!:A0802F0054180000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (3)
[44064:32688:0802/215929.386:INFO:CONSOLE:1] "TypeError: Cannot read properties of undefined (reading 'network')", source: https://lf-content-ecology.toutiaostatic.com/obj/tt-content-ecology-fe/pgcfe/mp/web/resource/vendors~a20d0b72_2864a4eef8f54aa1.js (1)
[5972:22296:0802/215930.235:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0802F0054180000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[44064:32688:0802/215930.282:INFO:CONSOLE:3] "[GroupMarkerNotSet(crbug.com/242999)!:A0802F0054180000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (3)
[44064:32688:0802/215930.450:INFO:CONSOLE:1] "TypeError: Cannot read properties of undefined (reading 'network')", source: https://lf-content-ecology.toutiaostatic.com/obj/tt-content-ecology-fe/pgcfe/mp/web/resource/vendors~a20d0b72_2864a4eef8f54aa1.js (1)
[5972:22296:0802/215931.060:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0E02F0054180000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[44064:32688:0802/215931.068:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0E02F0054180000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (0)
