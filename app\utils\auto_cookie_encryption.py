#!/usr/bin/env python3
"""
自动Cookie加密处理模块
实现新添加账号的Cookie文件自动AES-256加密功能
"""

import os
import json
import shutil
from typing import Tuple, Optional, Dict, Any
from app.utils.logger import info, warning, error, debug, success
from app.utils.cookie_encryption import get_cookie_encryptor


class AutoCookieEncryption:
    """自动Cookie加密处理器"""
    
    def __init__(self):
        """初始化自动加密处理器"""
        self.encryptor = get_cookie_encryptor()
        self.backup_enabled = False  # 根据需求，不保留备份
        
    def detect_cookie_format(self, file_path: str) -> str:
        """检测Cookie文件格式
        
        Args:
            file_path: Cookie文件路径
            
        Returns:
            str: 文件格式类型 ('encrypted', 'json', 'text', 'unknown')
        """
        try:
            if not os.path.exists(file_path):
                return 'unknown'
                
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                
            if not content:
                return 'unknown'
                
            # 尝试解析JSON格式
            try:
                data = json.loads(content)
                if isinstance(data, dict):
                    # 检查是否已经是加密格式
                    if data.get("encrypted", False):
                        return 'encrypted'
                    else:
                        return 'json'
                return 'unknown'
            except json.JSONDecodeError:
                # 检查是否是文本格式
                if '=' in content:
                    return 'text'
                return 'unknown'
                
        except Exception as e:
            error(f"检测Cookie文件格式时出错: {str(e)}")
            return 'unknown'
    
    def parse_cookie_content(self, file_path: str) -> Optional[Dict[str, Any]]:
        """解析Cookie文件内容
        
        Args:
            file_path: Cookie文件路径
            
        Returns:
            Optional[Dict]: 解析后的Cookie数据，失败返回None
        """
        try:
            format_type = self.detect_cookie_format(file_path)
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().strip()
                
            if format_type == 'encrypted':
                debug("检测到已加密的Cookie文件，无需重新加密")
                return None
                
            elif format_type == 'json':
                # JSON格式解析
                data = json.loads(content)
                debug("检测到JSON格式Cookie文件")
                return data
                
            elif format_type == 'text':
                # 文本格式解析
                debug("检测到文本格式Cookie文件")
                return self._parse_text_format(content)
                
            else:
                warning(f"无法识别的Cookie文件格式: {file_path}")
                return None
                
        except Exception as e:
            error(f"解析Cookie文件内容时出错: {str(e)}")
            return None
    
    def _parse_text_format(self, content: str) -> Dict[str, Any]:
        """解析文本格式Cookie
        
        Args:
            content: 文本内容
            
        Returns:
            Dict: 解析后的Cookie数据
        """
        cookie_dict = {}
        
        # 尝试不同的分隔方式
        if ';' in content:
            pairs = content.split(';')
        elif '\n' in content:
            pairs = content.split('\n')
        else:
            pairs = content.split(' ')
            
        for pair in pairs:
            pair = pair.strip()
            if not pair:
                continue
                
            if '=' in pair:
                try:
                    key, value = pair.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    
                    # 移除可能的引号
                    if value.startswith('"') and value.endswith('"'):
                        value = value[1:-1]
                    elif value.startswith("'") and value.endswith("'"):
                        value = value[1:-1]
                        
                    if key and value:
                        cookie_dict[key] = value
                except ValueError:
                    continue
        
        # 返回标准格式
        return {
            "cookies": cookie_dict
        }
    
    def auto_encrypt_cookie_file(self, file_path: str, account_id: str = None, remark: str = None) -> Tuple[bool, str]:
        """自动加密Cookie文件
        
        Args:
            file_path: Cookie文件路径
            account_id: 账号ID
            remark: 账号备注
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        try:
            # 检查加密功能是否可用
            if not self.encryptor.available:
                warning("Cookie加密功能不可用（cryptography库未安装）")
                return False, "加密功能不可用"
            
            # 检测文件格式
            format_type = self.detect_cookie_format(file_path)
            
            if format_type == 'encrypted':
                debug(f"文件已加密，无需处理: {file_path}")
                return True, "文件已加密"
            
            if format_type == 'unknown':
                warning(f"无法识别的Cookie文件格式: {file_path}")
                return False, "无法识别的文件格式"
            
            # 解析Cookie内容
            cookie_data = self.parse_cookie_content(file_path)
            if not cookie_data:
                return False, "解析Cookie内容失败"
            
            # 补充账号信息
            if account_id:
                cookie_data["accountId"] = account_id
            if remark:
                cookie_data["remark"] = remark
            
            # 创建备份（如果启用）
            if self.backup_enabled:
                backup_path = f"{file_path}.backup"
                shutil.copy2(file_path, backup_path)
                debug(f"已创建备份文件: {backup_path}")
            
            # 加密Cookie数据
            encrypted_data = self.encryptor.encrypt_cookie_data(cookie_data)
            
            # 创建加密文件结构
            encrypted_file_data = {
                "encrypted": True,
                "version": "1.0",
                "algorithm": "AES-256",
                "data": encrypted_data,
                "account_id": account_id or "",
                "remark": remark or ""
            }
            
            # 保存加密文件
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(encrypted_file_data, f, ensure_ascii=False, indent=2)
            
            debug(f"Cookie文件已自动加密: {file_path}")
            return True, "自动加密成功"
            
        except Exception as e:
            error(f"自动加密Cookie文件时出错: {str(e)}")
            return False, f"加密失败: {str(e)}"
    
    def batch_encrypt_directory(self, directory_path: str) -> Tuple[int, int]:
        """批量加密目录中的Cookie文件
        
        Args:
            directory_path: 目录路径
            
        Returns:
            Tuple[int, int]: (成功数量, 总数量)
        """
        if not os.path.exists(directory_path):
            warning(f"目录不存在: {directory_path}")
            return 0, 0
        
        success_count = 0
        total_count = 0
        
        try:
            for filename in os.listdir(directory_path):
                if filename.endswith(('.json', '.txt', '.cookie')):
                    file_path = os.path.join(directory_path, filename)
                    total_count += 1
                    
                    # 从文件名提取账号ID
                    account_id = os.path.splitext(filename)[0]
                    
                    success, message = self.auto_encrypt_cookie_file(file_path, account_id)
                    if success:
                        success_count += 1
                    else:
                        warning(f"加密文件失败 {filename}: {message}")
            
            info(f"批量加密完成: {success_count}/{total_count} 个文件成功")
            return success_count, total_count
            
        except Exception as e:
            error(f"批量加密目录时出错: {str(e)}")
            return success_count, total_count


# 全局实例
_auto_encryptor = None

def get_auto_cookie_encryptor() -> AutoCookieEncryption:
    """获取自动Cookie加密器实例"""
    global _auto_encryptor
    if _auto_encryptor is None:
        _auto_encryptor = AutoCookieEncryption()
    return _auto_encryptor


def auto_encrypt_new_cookie_file(file_path: str, account_id: str = None, remark: str = None) -> Tuple[bool, str]:
    """自动加密新添加的Cookie文件（便捷函数）
    
    Args:
        file_path: Cookie文件路径
        account_id: 账号ID
        remark: 账号备注
        
    Returns:
        Tuple[bool, str]: (是否成功, 消息)
    """
    encryptor = get_auto_cookie_encryptor()
    return encryptor.auto_encrypt_cookie_file(file_path, account_id, remark)


if __name__ == "__main__":
    # 测试代码
    test_encryptor = AutoCookieEncryption()
    print("自动Cookie加密模块测试完成")
