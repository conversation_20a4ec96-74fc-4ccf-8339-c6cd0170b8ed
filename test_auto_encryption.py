#!/usr/bin/env python3
"""
测试自动Cookie加密功能
"""

import os
import json
import tempfile
import shutil
from app.utils.auto_cookie_encryption import AutoCookieEncryption, auto_encrypt_new_cookie_file
from app.utils.cookie_encryption import get_cookie_encryptor


def create_test_cookie_files():
    """创建测试用的Cookie文件"""
    test_dir = tempfile.mkdtemp(prefix="cookie_test_")
    print(f"创建测试目录: {test_dir}")
    
    # 1. 创建JSON格式Cookie文件
    json_cookie = {
        "accountId": "test_json_account",
        "remark": "JSON格式测试账号",
        "cookies": {
            "sessionid": "abc123456",
            "csrftoken": "def789012",
            "tt_webid": "ghi345678"
        }
    }
    
    json_file = os.path.join(test_dir, "test_json_account.json")
    with open(json_file, 'w', encoding='utf-8') as f:
        json.dump(json_cookie, f, ensure_ascii=False, indent=2)
    print(f"创建JSON格式Cookie文件: {json_file}")
    
    # 2. 创建文本格式<PERSON>ie文件
    text_cookie = "sessionid=xyz789; csrftoken=uvw456; tt_webid=rst123"
    
    text_file = os.path.join(test_dir, "test_text_account.txt")
    with open(text_file, 'w', encoding='utf-8') as f:
        f.write(text_cookie)
    print(f"创建文本格式Cookie文件: {text_file}")
    
    # 3. 创建已加密的Cookie文件
    encryptor = get_cookie_encryptor()
    if encryptor.available:
        encrypted_data = encryptor.encrypt_cookie_data(json_cookie)
        encrypted_cookie = {
            "encrypted": True,
            "version": "1.0",
            "algorithm": "AES-256",
            "data": encrypted_data,
            "account_id": "test_encrypted_account",
            "remark": "已加密测试账号"
        }
        
        encrypted_file = os.path.join(test_dir, "test_encrypted_account.json")
        with open(encrypted_file, 'w', encoding='utf-8') as f:
            json.dump(encrypted_cookie, f, ensure_ascii=False, indent=2)
        print(f"创建已加密Cookie文件: {encrypted_file}")
    
    return test_dir


def test_format_detection():
    """测试格式检测功能"""
    print("\n=== 测试格式检测功能 ===")
    
    test_dir = create_test_cookie_files()
    encryptor = AutoCookieEncryption()
    
    try:
        # 测试各种格式检测
        test_files = [
            ("test_json_account.json", "json"),
            ("test_text_account.txt", "text"),
            ("test_encrypted_account.json", "encrypted")
        ]
        
        for filename, expected_format in test_files:
            file_path = os.path.join(test_dir, filename)
            if os.path.exists(file_path):
                detected_format = encryptor.detect_cookie_format(file_path)
                status = "✅" if detected_format == expected_format else "❌"
                print(f"{status} {filename}: 期望={expected_format}, 检测={detected_format}")
            else:
                print(f"⚠️ 文件不存在: {filename}")
                
    finally:
        # 清理测试目录
        shutil.rmtree(test_dir)
        print(f"清理测试目录: {test_dir}")


def test_auto_encryption():
    """测试自动加密功能"""
    print("\n=== 测试自动加密功能 ===")
    
    test_dir = create_test_cookie_files()
    
    try:
        # 测试JSON格式自动加密
        json_file = os.path.join(test_dir, "test_json_account.json")
        success, message = auto_encrypt_new_cookie_file(
            json_file, 
            "test_json_account", 
            "JSON格式测试账号"
        )
        
        status = "✅" if success else "❌"
        print(f"{status} JSON格式自动加密: {message}")
        
        # 验证加密后的文件
        if success and os.path.exists(json_file):
            with open(json_file, 'r', encoding='utf-8') as f:
                encrypted_data = json.load(f)
            
            if encrypted_data.get("encrypted", False):
                print("✅ 文件已成功加密")
                print(f"   算法: {encrypted_data.get('algorithm', 'N/A')}")
                print(f"   版本: {encrypted_data.get('version', 'N/A')}")
            else:
                print("❌ 文件加密验证失败")
        
        # 测试文本格式自动加密
        text_file = os.path.join(test_dir, "test_text_account.txt")
        success, message = auto_encrypt_new_cookie_file(
            text_file, 
            "test_text_account", 
            "文本格式测试账号"
        )
        
        status = "✅" if success else "❌"
        print(f"{status} 文本格式自动加密: {message}")
        
        # 测试已加密文件（应该跳过）
        encrypted_file = os.path.join(test_dir, "test_encrypted_account.json")
        if os.path.exists(encrypted_file):
            success, message = auto_encrypt_new_cookie_file(
                encrypted_file, 
                "test_encrypted_account", 
                "已加密测试账号"
            )
            
            status = "✅" if success and "已加密" in message else "❌"
            print(f"{status} 已加密文件处理: {message}")
            
    finally:
        # 清理测试目录
        shutil.rmtree(test_dir)
        print(f"清理测试目录: {test_dir}")


def test_batch_encryption():
    """测试批量加密功能"""
    print("\n=== 测试批量加密功能 ===")
    
    test_dir = create_test_cookie_files()
    encryptor = AutoCookieEncryption()
    
    try:
        # 执行批量加密
        success_count, total_count = encryptor.batch_encrypt_directory(test_dir)
        
        print(f"批量加密结果: {success_count}/{total_count} 个文件成功")
        
        # 验证加密结果
        for filename in os.listdir(test_dir):
            if filename.endswith(('.json', '.txt')):
                file_path = os.path.join(test_dir, filename)
                format_type = encryptor.detect_cookie_format(file_path)
                status = "✅" if format_type == "encrypted" else "❌"
                print(f"{status} {filename}: {format_type}")
                
    finally:
        # 清理测试目录
        shutil.rmtree(test_dir)
        print(f"清理测试目录: {test_dir}")


def main():
    """主测试函数"""
    print("🧪 开始测试自动Cookie加密功能")
    print("=" * 50)
    
    # 检查加密功能是否可用
    encryptor = get_cookie_encryptor()
    if not encryptor.available:
        print("❌ Cookie加密功能不可用（cryptography库未安装）")
        print("请运行: pip install cryptography==42.0.5")
        return
    
    print("✅ Cookie加密功能可用")
    
    # 运行测试
    test_format_detection()
    test_auto_encryption()
    test_batch_encryption()
    
    print("\n" + "=" * 50)
    print("🎉 自动Cookie加密功能测试完成")


if __name__ == "__main__":
    main()
