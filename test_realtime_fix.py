#!/usr/bin/env python3
"""
测试实时显示修复功能
"""

import os
import json
import tempfile
import time


def simulate_account_addition():
    """模拟账号添加过程"""
    print("🧪 模拟账号添加实时显示测试")
    print("=" * 50)
    
    # 创建临时目录模拟cookie路径
    test_dir = tempfile.mkdtemp(prefix="realtime_fix_test_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        # 模拟现有的cookie文件列表
        existing_files = []
        
        # 创建一个新的账号文件
        account_id = "test_realtime_account"
        remark = "实时显示测试账号"
        
        # 创建Cookie数据
        cookie_data = {
            "accountId": account_id,
            "remark": remark,
            "cookies": {
                "sessionid": f"test_session_{account_id}",
                "csrftoken": f"test_csrf_{account_id}",
                "tt_webid": f"test_webid_{account_id}"
            }
        }
        
        # 保存Cookie文件
        cookie_file = os.path.join(test_dir, f"{account_id}.txt")
        with open(cookie_file, 'w', encoding='utf-8') as f:
            json.dump(cookie_data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 创建Cookie文件: {cookie_file}")
        
        # 模拟自动加密
        try:
            from app.utils.auto_cookie_encryption import auto_encrypt_new_cookie_file
            encrypt_success, encrypt_message = auto_encrypt_new_cookie_file(
                cookie_file, 
                account_id, 
                remark
            )
            
            if encrypt_success:
                print(f"🔒 自动加密成功: {encrypt_message}")
            else:
                print(f"⚠️ 加密失败: {encrypt_message}")
                
        except Exception as e:
            print(f"❌ 加密过程出错: {str(e)}")
        
        # 模拟实时显示逻辑
        print("\n📋 模拟实时显示逻辑:")
        
        # 检查文件是否在列表中
        saved_path = cookie_file
        cookie_files = existing_files  # 模拟现有文件列表
        
        print(f"   - 新文件路径: {saved_path}")
        print(f"   - 现有文件列表: {cookie_files}")
        print(f"   - 文件是否已存在: {saved_path in cookie_files}")
        
        if saved_path not in cookie_files:
            print("   ✅ 文件不在列表中，应该添加到表格")
            
            # 模拟添加到列表
            cookie_files.append(saved_path)
            print(f"   ✅ 已添加到文件列表，新列表长度: {len(cookie_files)}")
            
            # 模拟表格操作
            print("   📊 模拟表格操作:")
            print("      - 增加表格行数")
            print("      - 设置昵称、账号、状态")
            print("      - 添加操作按钮")
            print("      - 强制刷新表格")
            print("      - 滚动到新行并选中")
            
        else:
            print("   ⚠️ 文件已在列表中，执行强制添加逻辑")
        
        # 验证文件内容
        print(f"\n🔍 验证文件内容:")
        if os.path.exists(cookie_file):
            with open(cookie_file, 'r', encoding='utf-8') as f:
                content = json.load(f)
                
            if content.get("encrypted", False):
                print(f"   🔐 文件已加密")
                print(f"      - 算法: {content.get('algorithm', 'N/A')}")
                print(f"      - 版本: {content.get('version', 'N/A')}")
                print(f"      - 账号ID: {content.get('account_id', 'N/A')}")
            else:
                print(f"   📄 文件为明文格式")
                print(f"      - 账号ID: {content.get('accountId', 'N/A')}")
                print(f"      - 备注: {content.get('remark', 'N/A')}")
        
        print(f"\n✅ 模拟测试完成")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {str(e)}")
        return False
        
    finally:
        # 清理测试文件
        try:
            import shutil
            shutil.rmtree(test_dir)
            print(f"🧹 清理测试目录: {test_dir}")
        except Exception as e:
            print(f"⚠️ 清理测试目录失败: {str(e)}")


def analyze_log_issue():
    """分析日志中的问题"""
    print("\n🔍 分析用户日志中的问题:")
    print("=" * 40)
    
    print("从用户提供的日志可以看出:")
    print("1. ✅ 账号添加成功: '账号 2 添加成功，文件路径: E:/软件共享/头条/头条/测试账号\\2.txt'")
    print("2. ✅ 自动加密成功: 'Cookie已自动使用AES-256算法加密保存'")
    print("3. ❌ 没有看到实时显示的日志信息")
    print("4. ✅ 用户点击'加载账号'后才看到新账号")
    
    print("\n可能的原因:")
    print("1. 🤔 on_browser_finished 回调没有被正确触发")
    print("2. 🤔 文件路径检查条件有问题")
    print("3. 🤔 实时显示代码被跳过了")
    
    print("\n解决方案:")
    print("1. ✅ 已添加详细的调试日志")
    print("2. ✅ 已添加强制添加逻辑")
    print("3. ✅ 已增强表格刷新机制")
    
    print("\n下次测试时请注意:")
    print("- 查看是否有 '检查文件是否已存在于列表中' 的日志")
    print("- 查看是否有 '文件不在列表中，准备添加到表格' 的日志")
    print("- 查看是否有 '新账号已实时添加到表格' 的日志")


def main():
    """主测试函数"""
    print("🚀 实时显示修复测试套件")
    print("=" * 60)
    
    # 检查依赖
    try:
        from app.utils.auto_cookie_encryption import auto_encrypt_new_cookie_file
        print("✅ 自动加密模块可用")
    except ImportError as e:
        print(f"⚠️ 自动加密模块不可用: {str(e)}")
    
    # 运行模拟测试
    if simulate_account_addition():
        print("\n✅ 模拟测试通过")
    else:
        print("\n❌ 模拟测试失败")
    
    # 分析日志问题
    analyze_log_issue()
    
    print("\n" + "=" * 60)
    print("📋 实时显示修复总结:")
    print("1. ✅ 添加详细调试日志")
    print("2. ✅ 增强条件判断逻辑")
    print("3. ✅ 添加强制添加分支")
    print("4. ✅ 完善表格刷新机制")
    print("5. ✅ 集成自动加密功能")
    
    print("\n💡 下次测试建议:")
    print("- 添加账号后立即查看日志")
    print("- 确认回调函数是否被触发")
    print("- 验证文件路径是否正确")


if __name__ == "__main__":
    main()
