#!/usr/bin/env python3
"""
浏览器窗口九宫格排列布局管理器
实现单独登录功能的浏览器窗口自动九宫格排列布局
"""

import os
import json
import time
from typing import Tuple, List, Dict, Optional
from PyQt5.QtWidgets import QDesktopWidget, QApplication
from app.utils.logger import info, warning, error, debug, success


class BrowserWindowLayoutManager:
    """浏览器窗口九宫格排列布局管理器"""
    
    def __init__(self):
        """初始化布局管理器"""
        self.grid_size = (3, 3)  # 3x3网格
        self.window_positions = []  # 存储窗口位置信息
        self.active_windows = {}  # 活跃窗口字典 {account_id: position_index}
        self.layout_enabled = True  # 是否启用自动排列功能
        self.window_spacing = 5  # 窗口间距（像素）
        self.min_window_size = (400, 300)  # 最小窗口尺寸
        self.config_file = "browser_layout_config.json"
        
        # 加载配置
        self.load_config()
        
        # 初始化屏幕信息
        self.update_screen_info()
    
    def load_config(self):
        """加载布局配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    
                self.layout_enabled = config.get('layout_enabled', True)
                self.window_spacing = config.get('window_spacing', 5)
                self.min_window_size = tuple(config.get('min_window_size', [400, 300]))
                
                debug(f"已加载浏览器布局配置: 启用={self.layout_enabled}, 间距={self.window_spacing}")
            else:
                debug("使用默认浏览器布局配置")
                
        except Exception as e:
            warning(f"加载浏览器布局配置失败: {str(e)}")
    
    def save_config(self):
        """保存布局配置"""
        try:
            config = {
                'layout_enabled': self.layout_enabled,
                'window_spacing': self.window_spacing,
                'min_window_size': list(self.min_window_size)
            }
            
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
                
            debug(f"已保存浏览器布局配置到: {self.config_file}")
            
        except Exception as e:
            warning(f"保存浏览器布局配置失败: {str(e)}")
    
    def update_screen_info(self):
        """更新屏幕信息"""
        try:
            # 获取主屏幕信息
            app = QApplication.instance()
            if app:
                desktop = QDesktopWidget()
                screen_rect = desktop.availableGeometry(desktop.primaryScreen())
                
                self.screen_width = screen_rect.width()
                self.screen_height = screen_rect.height()
                self.screen_x = screen_rect.x()
                self.screen_y = screen_rect.y()
                
                info(f"屏幕信息更新: {self.screen_width}x{self.screen_height} @ ({self.screen_x}, {self.screen_y})")
            else:
                # 使用默认值
                self.screen_width = 1920
                self.screen_height = 1080
                self.screen_x = 0
                self.screen_y = 0
                warning("无法获取屏幕信息，使用默认值")
            
            # 重新计算窗口位置
            self.calculate_window_positions()
            
        except Exception as e:
            error(f"更新屏幕信息时出错: {str(e)}")
            # 使用默认值
            self.screen_width = 1920
            self.screen_height = 1080
            self.screen_x = 0
            self.screen_y = 0
            self.calculate_window_positions()
    
    def calculate_window_positions(self):
        """计算九宫格窗口位置"""
        try:
            # 计算可用区域（排除间距）
            available_width = self.screen_width - (self.grid_size[0] + 1) * self.window_spacing
            available_height = self.screen_height - (self.grid_size[1] + 1) * self.window_spacing
            
            # 计算每个窗口的尺寸
            window_width = max(available_width // self.grid_size[0], self.min_window_size[0])
            window_height = max(available_height // self.grid_size[1], self.min_window_size[1])
            
            # 如果窗口尺寸超过最小值，重新调整以确保适合屏幕
            if window_width * self.grid_size[0] + (self.grid_size[0] + 1) * self.window_spacing > self.screen_width:
                window_width = (self.screen_width - (self.grid_size[0] + 1) * self.window_spacing) // self.grid_size[0]
            
            if window_height * self.grid_size[1] + (self.grid_size[1] + 1) * self.window_spacing > self.screen_height:
                window_height = (self.screen_height - (self.grid_size[1] + 1) * self.window_spacing) // self.grid_size[1]
            
            # 确保窗口尺寸不小于最小值
            window_width = max(window_width, self.min_window_size[0])
            window_height = max(window_height, self.min_window_size[1])
            
            # 生成九宫格位置
            self.window_positions = []
            for row in range(self.grid_size[1]):
                for col in range(self.grid_size[0]):
                    x = self.screen_x + self.window_spacing + col * (window_width + self.window_spacing)
                    y = self.screen_y + self.window_spacing + row * (window_height + self.window_spacing)
                    
                    position = {
                        'x': x,
                        'y': y,
                        'width': window_width,
                        'height': window_height,
                        'row': row,
                        'col': col,
                        'index': row * self.grid_size[0] + col,
                        'occupied': False
                    }
                    
                    self.window_positions.append(position)
            
            info(f"已计算九宫格窗口位置: {len(self.window_positions)}个位置, 窗口尺寸: {window_width}x{window_height}")
            
        except Exception as e:
            error(f"计算窗口位置时出错: {str(e)}")
    
    def get_next_available_position(self) -> Optional[Dict]:
        """获取下一个可用的窗口位置
        
        Returns:
            Dict: 窗口位置信息，如果没有可用位置则返回None
        """
        try:
            for position in self.window_positions:
                if not position['occupied']:
                    return position
            
            warning("没有可用的窗口位置")
            return None
            
        except Exception as e:
            error(f"获取可用窗口位置时出错: {str(e)}")
            return None
    
    def allocate_window_position(self, account_id: str) -> Optional[Dict]:
        """为账号分配窗口位置
        
        Args:
            account_id: 账号ID
            
        Returns:
            Dict: 分配的窗口位置信息，如果分配失败则返回None
        """
        try:
            if not self.layout_enabled:
                debug("浏览器窗口自动排列功能已禁用")
                return None
            
            # 检查是否已经分配过位置
            if account_id in self.active_windows:
                position_index = self.active_windows[account_id]
                if position_index < len(self.window_positions):
                    position = self.window_positions[position_index]
                    debug(f"账号 {account_id} 已有分配的位置: 位置{position_index}")
                    return position
            
            # 分配新位置
            position = self.get_next_available_position()
            if position:
                position['occupied'] = True
                self.active_windows[account_id] = position['index']
                
                info(f"为账号 {account_id} 分配窗口位置: 位置{position['index']} ({position['col']}, {position['row']})")
                return position
            else:
                warning(f"无法为账号 {account_id} 分配窗口位置：没有可用位置")
                return None
                
        except Exception as e:
            error(f"分配窗口位置时出错: {str(e)}")
            return None
    
    def release_window_position(self, account_id: str):
        """释放账号的窗口位置
        
        Args:
            account_id: 账号ID
        """
        try:
            if account_id in self.active_windows:
                position_index = self.active_windows[account_id]
                
                if position_index < len(self.window_positions):
                    self.window_positions[position_index]['occupied'] = False
                    
                del self.active_windows[account_id]
                info(f"已释放账号 {account_id} 的窗口位置: 位置{position_index}")
            else:
                debug(f"账号 {account_id} 没有分配的窗口位置")
                
        except Exception as e:
            error(f"释放窗口位置时出错: {str(e)}")
    
    def apply_window_layout(self, driver, account_id: str) -> bool:
        """应用窗口布局到浏览器
        
        Args:
            driver: WebDriver实例
            account_id: 账号ID
            
        Returns:
            bool: 是否成功应用布局
        """
        try:
            if not self.layout_enabled:
                debug("浏览器窗口自动排列功能已禁用")
                return False
            
            # 分配窗口位置
            position = self.allocate_window_position(account_id)
            if not position:
                return False
            
            # 应用窗口位置和大小
            driver.set_window_position(position['x'], position['y'])
            driver.set_window_size(position['width'], position['height'])
            
            # 等待窗口调整完成
            time.sleep(0.2)
            
            success(f"✅ 已应用九宫格布局到账号 {account_id}: 位置({position['x']}, {position['y']}) 尺寸{position['width']}x{position['height']}")
            return True
            
        except Exception as e:
            error(f"应用窗口布局时出错: {str(e)}")
            return False
    
    def get_layout_status(self) -> Dict:
        """获取布局状态信息
        
        Returns:
            Dict: 布局状态信息
        """
        try:
            occupied_count = sum(1 for pos in self.window_positions if pos['occupied'])
            available_count = len(self.window_positions) - occupied_count
            
            return {
                'enabled': self.layout_enabled,
                'total_positions': len(self.window_positions),
                'occupied_positions': occupied_count,
                'available_positions': available_count,
                'active_windows': dict(self.active_windows),
                'screen_info': {
                    'width': self.screen_width,
                    'height': self.screen_height,
                    'x': self.screen_x,
                    'y': self.screen_y
                }
            }
            
        except Exception as e:
            error(f"获取布局状态时出错: {str(e)}")
            return {}
    
    def reset_layout(self):
        """重置布局状态"""
        try:
            # 清除所有占用状态
            for position in self.window_positions:
                position['occupied'] = False
            
            # 清除活跃窗口记录
            self.active_windows.clear()
            
            info("已重置浏览器窗口布局状态")
            
        except Exception as e:
            error(f"重置布局状态时出错: {str(e)}")
    
    def set_layout_enabled(self, enabled: bool):
        """设置是否启用自动排列功能
        
        Args:
            enabled: 是否启用
        """
        self.layout_enabled = enabled
        self.save_config()
        
        if enabled:
            info("已启用浏览器窗口自动九宫格排列功能")
        else:
            info("已禁用浏览器窗口自动九宫格排列功能")


# 全局实例
_layout_manager = None

def get_browser_layout_manager() -> BrowserWindowLayoutManager:
    """获取浏览器窗口布局管理器实例"""
    global _layout_manager
    if _layout_manager is None:
        _layout_manager = BrowserWindowLayoutManager()
    return _layout_manager


def apply_browser_window_layout(driver, account_id: str) -> bool:
    """应用浏览器窗口九宫格布局（便捷函数）
    
    Args:
        driver: WebDriver实例
        account_id: 账号ID
        
    Returns:
        bool: 是否成功应用布局
    """
    layout_manager = get_browser_layout_manager()
    return layout_manager.apply_window_layout(driver, account_id)


def release_browser_window_position(account_id: str):
    """释放浏览器窗口位置（便捷函数）
    
    Args:
        account_id: 账号ID
    """
    layout_manager = get_browser_layout_manager()
    layout_manager.release_window_position(account_id)


if __name__ == "__main__":
    # 测试代码
    manager = BrowserWindowLayoutManager()
    status = manager.get_layout_status()
    print(f"布局状态: {status}")
