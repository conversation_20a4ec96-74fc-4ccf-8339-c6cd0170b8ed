"""
现代化账号标签页 - 使用卡片视图替代表格视图
"""

from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QLabel,
    QTextEdit, QMessageBox, QDialog, QFormLayout, QLineEdit,
    QGroupBox, QCheckBox, QSpinBox, QComboBox, QSizePolicy,
    QTabWidget, QScrollArea, QFrame
)
from PyQt5.QtCore import pyqtSignal, QSize, QTimer, Qt
from PyQt5.QtGui import QFont, QIcon

import os
import json
import logging

from app.components.account_cards_container import AccountCardsContainer
from app.utils.logger import logger, info, success, warning, error, debug
from app.utils.piliang_cunggao import run_batch_cunggao
from app.utils.kamidenglu import get_announcement
from app.dialogs.toutiao_spider_dialog import open_toutiao_spider_dialog

class ModernAccountTab(QWidget):
    """现代化账号标签页，使用卡片视图替代表格视图"""

    # 定义信号
    load_account_clicked = pyqtSignal()
    cookie_count_changed = pyqtSignal(int)
    status_update_signal = pyqtSignal(str)

    def __init__(self):
        """初始化账号标签页"""
        super().__init__()

        # 存储cookie文件列表
        self.cookie_files = []

        # 存储cookie文件路径
        self.cookie_path = ""

        # 存储活动线程
        self.active_threads = []

        # 初始化UI
        self.init_ui()

        # 启动定时器定期刷新公告
        self.update_announcement_timer = QTimer(self)
        self.update_announcement_timer.timeout.connect(self.update_announcement)
        self.update_announcement_timer.start(3600000)  # 每小时更新一次公告

        # 初始加载公告 - 延迟更长时间，避免与主窗口冲突
        self.announcement_init_timer = QTimer(self)
        self.announcement_init_timer.setSingleShot(True)
        self.announcement_init_timer.timeout.connect(self.update_announcement)
        self.announcement_init_timer.start(5000)

        # 延迟加载模式
        self.delay_data_loading = True

    def init_ui(self):
        """初始化UI"""
        # 主布局
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(15)

        # 顶部区域 - 公告和操作按钮
        top_widget = QWidget()
        top_widget.setObjectName("topWidget")
        top_widget.setStyleSheet("""
            #topWidget {
                background-color: white;
                border-radius: 10px;
                border: 1px solid #e0e0e0;
            }
        """)
        top_layout = QVBoxLayout(top_widget)
        top_layout.setContentsMargins(20, 20, 20, 20)
        top_layout.setSpacing(15)

        # 公告区域
        self.announcement_label = QLabel("正在加载公告...")
        self.announcement_label.setObjectName("announcementLabel")
        self.announcement_label.setStyleSheet("""
            #announcementLabel {
                background-color: #f8f9ff;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                padding: 15px;
                color: #333333;
                font-size: 14px;
            }
        """)
        self.announcement_label.setWordWrap(True)
        self.announcement_label.setMinimumHeight(80)
        top_layout.addWidget(self.announcement_label)

        # 操作按钮区域
        buttons_layout = QHBoxLayout()
        buttons_layout.setContentsMargins(0, 0, 0, 0)
        buttons_layout.setSpacing(15)

        # 搜索区域
        search_layout = QHBoxLayout()
        search_layout.setContentsMargins(0, 0, 0, 0)
        search_layout.setSpacing(10)

        search_label = QLabel("搜索:")
        search_label.setStyleSheet("font-weight: bold; font-size: 13px;")
        search_layout.addWidget(search_label)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("输入账号ID或备注进行搜索")
        self.search_input.setStyleSheet("""
            QLineEdit {
                border: 1px solid #e0e0e0;
                border-radius: 6px;
                padding: 8px 12px;
                background-color: white;
                font-size: 13px;
            }
            QLineEdit:focus {
                border: 1px solid #4a86e8;
            }
        """)
        self.search_input.textChanged.connect(self.filter_accounts)
        search_layout.addWidget(self.search_input)

        buttons_layout.addLayout(search_layout)

        # 添加弹性空间
        buttons_layout.addStretch(1)

        # 添加账号按钮
        self.add_account_btn = QPushButton("添加账号")
        self.add_account_btn.setObjectName("addAccountBtn")
        self.add_account_btn.setStyleSheet("""
            #addAccountBtn {
                background-color: #27ae60;
                color: white;
                font-weight: bold;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 13px;
            }
            #addAccountBtn:hover {
                background-color: #219653;
            }
        """)
        # 暂时不设置图标，避免文件不存在导致错误
        # self.add_account_btn.setIcon(QIcon("app/resources/icons/add.png"))
        self.add_account_btn.clicked.connect(self.add_account)
        buttons_layout.addWidget(self.add_account_btn)

        # 加载账号按钮
        self.load_account_btn = QPushButton("加载账号")
        self.load_account_btn.setObjectName("loadAccountBtn")
        self.load_account_btn.setStyleSheet("""
            #loadAccountBtn {
                background-color: #4a86e8;
                color: white;
                font-weight: bold;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 13px;
            }
            #loadAccountBtn:hover {
                background-color: #3a76d8;
            }
        """)
        # 暂时不设置图标，避免文件不存在导致错误
        # self.load_account_btn.setIcon(QIcon("app/resources/icons/refresh.png"))
        self.load_account_btn.clicked.connect(self.load_accounts)
        buttons_layout.addWidget(self.load_account_btn)

        # 批量存稿按钮
        self.batch_edit_btn = QPushButton("批量存稿")
        self.batch_edit_btn.setObjectName("batchEditBtn")
        self.batch_edit_btn.setStyleSheet("""
            #batchEditBtn {
                background-color: #9c27b0;
                color: white;
                font-weight: bold;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 13px;
            }
            #batchEditBtn:hover {
                background-color: #7b1fa2;
            }
        """)
        # 暂时不设置图标，避免文件不存在导致错误
        # self.batch_edit_btn.setIcon(QIcon("app/resources/icons/edit.png"))
        self.batch_edit_btn.clicked.connect(self.batch_edit)
        buttons_layout.addWidget(self.batch_edit_btn)

        # 视频爬虫按钮
        self.spider_btn = QPushButton("视频爬虫")
        self.spider_btn.setObjectName("spiderBtn")
        self.spider_btn.setStyleSheet("""
            #spiderBtn {
                background-color: #ff9800;
                color: white;
                font-weight: bold;
                border: none;
                border-radius: 6px;
                padding: 10px 20px;
                font-size: 13px;
            }
            #spiderBtn:hover {
                background-color: #f57c00;
            }
        """)
        # 暂时不设置图标，避免文件不存在导致错误
        # self.spider_btn.setIcon(QIcon("app/resources/icons/spider.png"))
        self.spider_btn.clicked.connect(self.open_toutiao_spider)
        buttons_layout.addWidget(self.spider_btn)

        top_layout.addLayout(buttons_layout)
        main_layout.addWidget(top_widget)

        # 账号标签页
        self.account_tabs = QTabWidget()
        self.account_tabs.setObjectName("accountTabs")
        self.account_tabs.setStyleSheet("""
            #accountTabs {
                background-color: transparent;
            }
            QTabWidget::pane {
                border: none;
                background-color: transparent;
            }
            QTabBar::tab {
                padding: 8px 20px;
                margin-right: 4px;
                border-top-left-radius: 8px;
                border-top-right-radius: 8px;
                border: 1px solid #e0e0e0;
                border-bottom: none;
                background-color: #f0f2f5;
                color: #666666;
                font-size: 13px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: white;
                color: #4a86e8;
                border-bottom: 3px solid #4a86e8;
            }
        """)

        # 所有账号标签页
        self.all_accounts_container = AccountCardsContainer()
        self.all_accounts_container.login_clicked.connect(self.login_account)
        self.all_accounts_container.edit_clicked.connect(self.edit_account)
        self.all_accounts_container.collect_clicked.connect(self.collect_account_data)
        self.all_accounts_container.delete_clicked.connect(self.delete_account)
        self.account_tabs.addTab(self.all_accounts_container, "所有账号")

        # 活跃账号标签页
        self.active_accounts_container = AccountCardsContainer()
        self.active_accounts_container.login_clicked.connect(self.login_account)
        self.active_accounts_container.edit_clicked.connect(self.edit_account)
        self.active_accounts_container.collect_clicked.connect(self.collect_account_data)
        self.active_accounts_container.delete_clicked.connect(self.delete_account)
        self.account_tabs.addTab(self.active_accounts_container, "活跃账号")

        # 失败账号标签页
        self.failed_accounts_container = AccountCardsContainer()
        self.failed_accounts_container.login_clicked.connect(self.login_account)
        self.failed_accounts_container.edit_clicked.connect(self.edit_account)
        self.failed_accounts_container.collect_clicked.connect(self.collect_account_data)
        self.failed_accounts_container.delete_clicked.connect(self.delete_account)
        self.account_tabs.addTab(self.failed_accounts_container, "失败账号")

        main_layout.addWidget(self.account_tabs)

    def update_announcement(self):
        """更新公告信息"""
        try:
            success, announcement = get_announcement()
            if success:
                self.announcement_label.setText(announcement)
                info(f"已更新公告: {announcement[:30]}...")
            else:
                self.announcement_label.setText("暂无公告")
                info(f"获取公告失败: {announcement}")
        except Exception as e:
            error(f"获取公告时出错: {str(e)}")
            self.announcement_label.setText("获取公告失败，请检查网络连接")

    def load_accounts(self):
        """加载账号"""
        # 获取设置中的cookie路径
        from app.tabs.setting_tab import SettingTab
        setting_tab = None

        # 查找主窗口中的设置标签页
        main_window = self.window()
        if hasattr(main_window, 'setting_tab'):
            setting_tab = main_window.setting_tab

        # 获取cookie路径
        if setting_tab and hasattr(setting_tab, 'cookie_path'):
            path = setting_tab.cookie_path.text()
        else:
            path = "D:/头条全自动/账号"

        # 检查路径是否存在
        if not os.path.exists(path):
            try:
                os.makedirs(path)
                info(f"创建账号目录: {path}")
            except Exception as e:
                error(f"创建账号目录失败: {str(e)}")
                QMessageBox.critical(self, "路径错误", f"账号路径不存在且无法创建: {path}")
                return

        # 保存cookie路径
        self.cookie_path = path

        # 清空当前列表
        self.cookie_files = []

        # 清空卡片容器
        self.all_accounts_container.clear()
        self.active_accounts_container.clear()
        self.failed_accounts_container.clear()

        # 加载cookie文件
        try:
            # 获取所有json和txt文件
            all_files = [f for f in os.listdir(path) if f.endswith((".json", ".txt"))]

            # 如果没有文件，显示提示
            if not all_files:
                QMessageBox.information(self, "提示", f"账号目录 {path} 中没有找到账号文件")
                return

            # 保存cookie文件列表
            self.cookie_files = [os.path.join(path, f) for f in all_files]

            # 更新账号卡片
            self.update_account_cards()

            # 发送cookie数量变化信号
            self.cookie_count_changed.emit(len(self.cookie_files))

            # 显示成功消息
            QMessageBox.information(self, "加载成功", f"成功加载 {len(self.cookie_files)} 个账号")

        except Exception as e:
            error(f"加载账号时出错: {str(e)}")
            QMessageBox.critical(self, "加载失败", f"加载账号时出错: {str(e)}")

    def update_account_cards(self):
        """更新账号卡片"""
        for cookie_file in self.cookie_files:
            try:
                # 获取文件名（不含路径）
                file_name = os.path.basename(cookie_file)
                account_id = os.path.splitext(file_name)[0]  # 移除后缀作为账号ID

                # 尝试读取cookie文件内容
                remark = ""
                try:
                    with open(cookie_file, 'r', encoding='utf-8') as f:
                        cookie_data = json.load(f)
                        if "remark" in cookie_data and cookie_data["remark"]:
                            remark = cookie_data["remark"]
                except Exception as e:
                    # 静默处理读取错误
                    debug(f"读取账号 {account_id} 的cookie文件时出错: {str(e)}")

                # 如果没有备注，使用账号ID作为备注
                if not remark:
                    remark = account_id

                # 添加到所有账号容器
                self.all_accounts_container.add_card(account_id, remark)

            except Exception as e:
                error(f"处理账号文件 {cookie_file} 时出错: {str(e)}")

    def filter_accounts(self, text):
        """根据搜索文本过滤账号

        Args:
            text: 搜索文本
        """
        # 获取搜索文本
        search_text = text.strip().lower()

        # 如果搜索文本为空，显示所有账号
        if not search_text:
            for account_id, card in self.all_accounts_container.cards.items():
                card.setVisible(True)
            return

        # 过滤账号
        for account_id, card in self.all_accounts_container.cards.items():
            # 检查账号ID和备注是否包含搜索文本
            if search_text in account_id.lower() or search_text in card.remark.lower():
                card.setVisible(True)
            else:
                card.setVisible(False)

    def add_account(self):
        """添加新账号"""
        # 创建对话框
        dialog = QDialog(self)
        dialog.setWindowTitle("添加账号")
        dialog.setMinimumWidth(400)

        # 创建布局
        layout = QVBoxLayout(dialog)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # 添加描述标签
        description = QLabel("请输入头条账号信息，点击确定后将打开浏览器进行登录")
        description.setStyleSheet("color: #666666; font-weight: normal;")
        layout.addWidget(description)

        # 表单布局
        form_layout = QFormLayout()
        form_layout.setContentsMargins(0, 10, 0, 10)
        form_layout.setSpacing(15)
        # 不设置标签对齐方式，使用默认值

        # 账号ID输入框
        account_id_input = QLineEdit()
        account_id_input.setPlaceholderText("请输入账号ID (如: ***********)")
        form_layout.addRow("账号ID:", account_id_input)

        # 备注输入框
        remark_input = QLineEdit()
        remark_input.setPlaceholderText("可选：账号备注 (如: 头条测试号)")
        form_layout.addRow("账号备注:", remark_input)

        layout.addLayout(form_layout)

        # 按钮布局
        button_layout = QHBoxLayout()
        button_layout.setContentsMargins(0, 10, 0, 0)
        button_layout.setSpacing(15)

        # 添加弹性空间
        button_layout.addStretch()

        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #e0e0e0;
                color: #333333;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #d0d0d0;
            }
        """)
        cancel_btn.clicked.connect(dialog.reject)
        button_layout.addWidget(cancel_btn)

        # 确定按钮
        ok_btn = QPushButton("确定")
        ok_btn.setStyleSheet("""
            QPushButton {
                background-color: #4a86e8;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #3a76d8;
            }
        """)
        ok_btn.clicked.connect(dialog.accept)
        button_layout.addWidget(ok_btn)

        layout.addLayout(button_layout)

        # 显示对话框
        if dialog.exec_() == QDialog.Accepted:
            # 获取用户输入
            account_id = account_id_input.text().strip()
            remark = remark_input.text().strip()

            # 验证账号ID是否为空
            if not account_id:
                warning("添加账号失败: 账号ID不能为空")
                QMessageBox.warning(self, "添加失败", "账号ID不能为空")
                return

            # 如果没有填写备注，则使用账号ID作为备注
            if not remark:
                remark = account_id

            # 以账号ID作为文件名
            cookie_filename = f"{account_id}.json"
            cookie_filepath = os.path.join(self.cookie_path, cookie_filename)

            # 打开浏览器进行登录
            self.open_browser_for_login(account_id, remark, cookie_filepath)

    def open_browser_for_login(self, account_id, remark, cookie_filepath):
        """打开浏览器进行登录

        Args:
            account_id: 账号ID
            remark: 账号备注
            cookie_filepath: Cookie文件路径
        """
        try:
            # 使用现有的添加账号功能
            from app.utils.tianjiazhanghao import open_browser_and_save_cookie

            def on_login_finished():
                # 登录完成后自动加密Cookie文件
                try:
                    from app.utils.auto_cookie_encryption import auto_encrypt_new_cookie_file

                    if os.path.exists(cookie_filepath):
                        encrypt_success, encrypt_message = auto_encrypt_new_cookie_file(
                            cookie_filepath,
                            account_id,
                            remark
                        )

                        if encrypt_success:
                            debug(f"新添加的Cookie文件已自动加密: {cookie_filepath}")
                        else:
                            debug(f"Cookie文件加密失败: {encrypt_message}")

                        # 立即添加新账号到界面
                        self.add_new_account_to_ui(account_id, remark, cookie_filepath)

                        # 重新加载账号列表
                        self.load_accounts()

                except Exception as encrypt_error:
                    warning(f"自动加密Cookie文件时出错: {str(encrypt_error)}")

            # 启动添加账号流程
            success_flag, message, saved_path = open_browser_and_save_cookie(
                account_id,
                remark,
                os.path.dirname(cookie_filepath)
            )

            if success_flag:
                on_login_finished()
                QMessageBox.information(self, "添加成功", f"账号 {account_id} 添加成功")
            else:
                QMessageBox.warning(self, "添加失败", f"添加账号失败: {message}")

        except Exception as e:
            error_msg = f"打开浏览器登录时出错: {str(e)}"
            error(error_msg)
            QMessageBox.critical(self, "登录错误", error_msg)

    def add_new_account_to_ui(self, account_id, remark, cookie_filepath):
        """立即添加新账号到UI界面

        Args:
            account_id: 账号ID
            remark: 账号备注
            cookie_filepath: Cookie文件路径
        """
        try:
            # 添加到cookie文件列表
            if cookie_filepath not in self.cookie_files:
                self.cookie_files.append(cookie_filepath)
                info(f"新账号文件已添加到列表: {cookie_filepath}")

            # 创建新的账号卡片
            from app.widgets.account_card import AccountCard
            card = AccountCard(account_id, self)
            card.set_remark(remark)
            card.set_status("待验证")

            # 立即添加到所有账号容器
            self.all_accounts_container.add_card(card)

            # 强制刷新界面
            self.all_accounts_container.update()
            QApplication.processEvents()

            info(f"新账号 {account_id} 已实时添加到界面")

        except Exception as e:
            error(f"立即添加账号到UI时出错: {str(e)}")

    def login_account(self, account_id):
        """登录账号

        Args:
            account_id: 账号ID
        """
        # 这里需要实现登录账号的逻辑
        QMessageBox.information(self, "功能未实现", f"登录账号 {account_id} 的功能尚未实现")

    def edit_account(self, account_id):
        """存稿

        Args:
            account_id: 账号ID
        """
        # 这里需要实现存稿的逻辑
        QMessageBox.information(self, "功能未实现", f"为账号 {account_id} 存稿的功能尚未实现")

    def collect_account_data(self, account_id):
        """采集账号数据

        Args:
            account_id: 账号ID
        """
        # 这里需要实现采集账号数据的逻辑
        QMessageBox.information(self, "功能未实现", f"采集账号 {account_id} 数据的功能尚未实现")

    def delete_account(self, account_id):
        """删除账号

        Args:
            account_id: 账号ID
        """
        # 确认删除
        reply = QMessageBox.question(
            self,
            "确认删除",
            f"确定要删除账号 {account_id} 吗？此操作不可恢复。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply != QMessageBox.Yes:
            return

        # 查找账号文件
        cookie_file = None
        for file in self.cookie_files:
            if os.path.splitext(os.path.basename(file))[0] == account_id:
                cookie_file = file
                break

        if not cookie_file:
            warning(f"删除账号失败: 未找到账号 {account_id} 的文件")
            QMessageBox.warning(self, "删除失败", f"未找到账号 {account_id} 的文件")
            return

        try:
            # 从内存列表中移除
            self.cookie_files.remove(cookie_file)

            # 从卡片容器中移除
            self.all_accounts_container.remove_card(account_id)
            self.active_accounts_container.remove_card(account_id)
            self.failed_accounts_container.remove_card(account_id)

            # 如果文件存在，则删除文件
            if os.path.exists(cookie_file):
                # 检查文件是否被保护（隐藏/只读），如果是则先移除保护属性
                try:
                    import ctypes
                    from ctypes import wintypes

                    # 获取文件属性
                    GetFileAttributes = ctypes.windll.kernel32.GetFileAttributesW
                    GetFileAttributes.argtypes = [wintypes.LPWSTR]
                    GetFileAttributes.restype = wintypes.DWORD

                    attrs = GetFileAttributes(cookie_file)
                    if attrs != 0xFFFFFFFF:  # 有效的属性值
                        FILE_ATTRIBUTE_HIDDEN = 0x02
                        FILE_ATTRIBUTE_READONLY = 0x01

                        # 检查是否有隐藏或只读属性
                        if (attrs & FILE_ATTRIBUTE_HIDDEN) or (attrs & FILE_ATTRIBUTE_READONLY):
                            debug(f"文件被保护，正在移除保护属性: {cookie_file}")

                            # 移除保护属性，设置为普通文件
                            SetFileAttributes = ctypes.windll.kernel32.SetFileAttributesW
                            SetFileAttributes.argtypes = [wintypes.LPWSTR, wintypes.DWORD]
                            SetFileAttributes.restype = wintypes.BOOL

                            FILE_ATTRIBUTE_NORMAL = 0x80
                            if SetFileAttributes(cookie_file, FILE_ATTRIBUTE_NORMAL):
                                debug(f"成功移除文件保护属性: {cookie_file}")
                            else:
                                warning(f"移除文件保护属性失败: {cookie_file}")
                except Exception as attr_err:
                    warning(f"处理文件属性时出错: {str(attr_err)}")

                # 删除文件
                os.remove(cookie_file)
                debug(f"已删除文件: {cookie_file}")

            # 发送cookie数量变化信号
            self.cookie_count_changed.emit(len(self.cookie_files))

            # 显示成功消息
            success(f"成功删除账号: {account_id}")

        except Exception as e:
            error(f"删除账号 {account_id} 时出错: {str(e)}")
            QMessageBox.critical(self, "删除失败", f"删除账号 {account_id} 时出错: {str(e)}")

    def batch_edit(self):
        """批量存稿"""
        # 这里需要实现批量存稿的逻辑
        QMessageBox.information(self, "功能未实现", "批量存稿功能尚未实现")

    def open_toutiao_spider(self):
        """打开头条爬虫"""
        try:
            open_toutiao_spider_dialog(self)
        except Exception as e:
            error(f"打开头条爬虫时出错: {str(e)}")
            QMessageBox.critical(self, "打开失败", f"打开头条爬虫时出错: {str(e)}")
