#!/usr/bin/env python3
"""
测试账号添加后的实时显示功能
"""

import os
import json
import tempfile
import time
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QTimer
import sys

def create_test_cookie_file(account_id, remark, cookie_dir):
    """创建测试用的Cookie文件"""
    cookie_data = {
        "accountId": account_id,
        "remark": remark,
        "cookies": {
            "sessionid": f"test_session_{account_id}",
            "csrftoken": f"test_csrf_{account_id}",
            "tt_webid": f"test_webid_{account_id}"
        }
    }
    
    cookie_file = os.path.join(cookie_dir, f"{account_id}.json")
    with open(cookie_file, 'w', encoding='utf-8') as f:
        json.dump(cookie_data, f, ensure_ascii=False, indent=2)
    
    return cookie_file


def test_realtime_display():
    """测试实时显示功能"""
    print("🧪 开始测试账号添加实时显示功能")
    print("=" * 50)
    
    # 创建临时目录
    test_dir = tempfile.mkdtemp(prefix="realtime_test_")
    print(f"创建测试目录: {test_dir}")
    
    try:
        # 模拟添加账号的过程
        test_accounts = [
            ("test_account_001", "测试账号001"),
            ("test_account_002", "测试账号002"),
            ("test_account_003", "测试账号003")
        ]
        
        print("\n📝 模拟账号添加过程:")
        
        for i, (account_id, remark) in enumerate(test_accounts, 1):
            print(f"\n{i}. 添加账号: {account_id} ({remark})")
            
            # 创建Cookie文件
            cookie_file = create_test_cookie_file(account_id, remark, test_dir)
            print(f"   ✅ 创建Cookie文件: {cookie_file}")
            
            # 模拟自动加密
            try:
                from app.utils.auto_cookie_encryption import auto_encrypt_new_cookie_file
                encrypt_success, encrypt_message = auto_encrypt_new_cookie_file(
                    cookie_file, 
                    account_id, 
                    remark
                )
                
                if encrypt_success:
                    print(f"   🔒 自动加密成功: {encrypt_message}")
                else:
                    print(f"   ⚠️ 加密失败: {encrypt_message}")
                    
            except Exception as e:
                print(f"   ❌ 加密过程出错: {str(e)}")
            
            # 验证文件存在
            if os.path.exists(cookie_file):
                print(f"   ✅ 文件验证通过")
                
                # 检查文件内容
                with open(cookie_file, 'r', encoding='utf-8') as f:
                    content = json.load(f)
                    if content.get("encrypted", False):
                        print(f"   🔐 文件已加密 (算法: {content.get('algorithm', 'N/A')})")
                    else:
                        print(f"   📄 文件为明文格式")
            else:
                print(f"   ❌ 文件不存在")
            
            # 模拟界面更新延迟
            time.sleep(0.5)
        
        print(f"\n📊 测试结果统计:")
        print(f"   - 测试目录: {test_dir}")
        print(f"   - 创建账号数: {len(test_accounts)}")
        
        # 检查所有文件
        created_files = [f for f in os.listdir(test_dir) if f.endswith('.json')]
        print(f"   - 实际文件数: {len(created_files)}")
        
        for filename in created_files:
            file_path = os.path.join(test_dir, filename)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = json.load(f)
                    account_id = content.get("account_id", "未知")
                    is_encrypted = content.get("encrypted", False)
                    status = "已加密" if is_encrypted else "明文"
                    print(f"   - {filename}: {account_id} ({status})")
            except Exception as e:
                print(f"   - {filename}: 读取失败 ({str(e)})")
        
        print("\n🎉 实时显示功能测试完成")
        
        # 提供清理选项
        print(f"\n💡 测试文件保存在: {test_dir}")
        print("   如需清理，请手动删除该目录")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {str(e)}")
        return False


def test_ui_integration():
    """测试UI集成功能（需要在主应用中运行）"""
    print("\n🖥️ UI集成测试说明:")
    print("=" * 30)
    print("1. 启动主应用程序")
    print("2. 进入账号管理标签页")
    print("3. 点击'添加账号'按钮")
    print("4. 输入测试账号信息")
    print("5. 观察账号是否实时显示在列表中")
    print("\n预期结果:")
    print("- 账号添加成功后立即显示在表格中")
    print("- 新行会自动滚动到可见区域")
    print("- 新行会被自动选中")
    print("- Cookie文件会自动加密")
    print("- 界面无需手动刷新")


def main():
    """主测试函数"""
    print("🚀 账号添加实时显示功能测试套件")
    print("=" * 60)
    
    # 检查依赖
    try:
        from app.utils.auto_cookie_encryption import auto_encrypt_new_cookie_file
        print("✅ 自动加密模块可用")
    except ImportError as e:
        print(f"⚠️ 自动加密模块不可用: {str(e)}")
    
    # 运行文件系统测试
    if test_realtime_display():
        print("\n✅ 文件系统测试通过")
    else:
        print("\n❌ 文件系统测试失败")
    
    # 显示UI测试说明
    test_ui_integration()
    
    print("\n" + "=" * 60)
    print("📋 实时显示功能改进总结:")
    print("1. ✅ 增强表格刷新机制")
    print("2. ✅ 添加立即UI更新")
    print("3. ✅ 自动滚动到新行")
    print("4. ✅ 自动选中新行")
    print("5. ✅ 集成自动加密")
    print("6. ✅ 强制界面重绘")
    print("7. ✅ 处理事件队列")


if __name__ == "__main__":
    main()
