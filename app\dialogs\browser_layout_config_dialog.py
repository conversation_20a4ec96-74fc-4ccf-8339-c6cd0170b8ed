#!/usr/bin/env python3
"""
浏览器九宫格布局配置对话框
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, 
                            QCheckBox, QSpinBox, QPushButton, QGroupBox,
                            QGridLayout, QFrame, QMessageBox, QTextEdit,
                            QSizePolicy, QSpacerItem)
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor, QPen
from app.utils.logger import info, warning, error, debug, success


class BrowserLayoutConfigDialog(QDialog):
    """浏览器九宫格布局配置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("浏览器窗口九宫格布局配置")
        self.setFixedSize(600, 500)
        self.setModal(True)
        
        # 加载布局管理器
        try:
            from app.utils.browser_window_layout_manager import get_browser_layout_manager
            self.layout_manager = get_browser_layout_manager()
        except Exception as e:
            error(f"加载布局管理器失败: {str(e)}")
            self.layout_manager = None
        
        self.init_ui()
        self.load_current_settings()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)
        
        # 标题
        title_label = QLabel("浏览器窗口九宫格自动排列布局配置")
        title_label.setFont(QFont("Microsoft YaHei", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
        layout.addWidget(title_label)
        
        # 功能说明
        desc_label = QLabel("当执行单独登录操作时，系统可自动将浏览器窗口按3x3网格排列，便于同时管理多个账号。")
        desc_label.setWordWrap(True)
        desc_label.setStyleSheet("color: #7f8c8d; font-size: 12px; margin-bottom: 15px;")
        layout.addWidget(desc_label)
        
        # 基本设置组
        basic_group = QGroupBox("基本设置")
        basic_group.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        basic_layout = QVBoxLayout(basic_group)
        
        # 启用/禁用开关
        self.enable_checkbox = QCheckBox("启用浏览器窗口自动九宫格排列")
        self.enable_checkbox.setFont(QFont("Microsoft YaHei", 10))
        self.enable_checkbox.setStyleSheet("QCheckBox { color: #2c3e50; }")
        basic_layout.addWidget(self.enable_checkbox)
        
        # 窗口间距设置
        spacing_layout = QHBoxLayout()
        spacing_label = QLabel("窗口间距:")
        spacing_label.setFont(QFont("Microsoft YaHei", 9))
        spacing_layout.addWidget(spacing_label)
        
        self.spacing_spinbox = QSpinBox()
        self.spacing_spinbox.setRange(0, 50)
        self.spacing_spinbox.setSuffix(" 像素")
        self.spacing_spinbox.setFont(QFont("Microsoft YaHei", 9))
        spacing_layout.addWidget(self.spacing_spinbox)
        
        spacing_layout.addStretch()
        basic_layout.addLayout(spacing_layout)
        
        layout.addWidget(basic_group)
        
        # 布局预览组
        preview_group = QGroupBox("布局预览")
        preview_group.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        preview_layout = QVBoxLayout(preview_group)
        
        # 创建九宫格预览
        self.preview_widget = LayoutPreviewWidget()
        preview_layout.addWidget(self.preview_widget)
        
        layout.addWidget(preview_group)
        
        # 状态信息组
        status_group = QGroupBox("当前状态")
        status_group.setFont(QFont("Microsoft YaHei", 10, QFont.Bold))
        status_layout = QVBoxLayout(status_group)
        
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(80)
        self.status_text.setReadOnly(True)
        self.status_text.setFont(QFont("Consolas", 9))
        self.status_text.setStyleSheet("""
            QTextEdit {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                padding: 8px;
            }
        """)
        status_layout.addWidget(self.status_text)
        
        layout.addWidget(status_group)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        
        # 重置布局按钮
        reset_btn = QPushButton("重置布局")
        reset_btn.setFont(QFont("Microsoft YaHei", 9))
        reset_btn.setStyleSheet("""
            QPushButton {
                background-color: #f39c12;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #e67e22;
            }
        """)
        reset_btn.clicked.connect(self.reset_layout)
        button_layout.addWidget(reset_btn)
        
        button_layout.addStretch()
        
        # 取消按钮
        cancel_btn = QPushButton("取消")
        cancel_btn.setFont(QFont("Microsoft YaHei", 9))
        cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        cancel_btn.clicked.connect(self.reject)
        button_layout.addWidget(cancel_btn)
        
        # 确定按钮
        ok_btn = QPushButton("确定")
        ok_btn.setFont(QFont("Microsoft YaHei", 9))
        ok_btn.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2ecc71;
            }
        """)
        ok_btn.clicked.connect(self.accept_settings)
        button_layout.addWidget(ok_btn)
        
        layout.addLayout(button_layout)
        
        # 连接信号
        self.enable_checkbox.toggled.connect(self.update_preview)
        self.spacing_spinbox.valueChanged.connect(self.update_preview)
    
    def load_current_settings(self):
        """加载当前设置"""
        try:
            if self.layout_manager:
                status = self.layout_manager.get_layout_status()
                
                # 设置控件值
                self.enable_checkbox.setChecked(status.get('enabled', True))
                self.spacing_spinbox.setValue(self.layout_manager.window_spacing)
                
                # 更新状态显示
                self.update_status_display(status)
                
                # 更新预览
                self.update_preview()
            else:
                self.enable_checkbox.setChecked(False)
                self.spacing_spinbox.setValue(5)
                self.status_text.setText("布局管理器加载失败")
                
        except Exception as e:
            error(f"加载当前设置时出错: {str(e)}")
    
    def update_status_display(self, status):
        """更新状态显示"""
        try:
            if not status:
                self.status_text.setText("无法获取状态信息")
                return
            
            screen_info = status.get('screen_info', {})
            status_text = f"""屏幕分辨率: {screen_info.get('width', 'N/A')}x{screen_info.get('height', 'N/A')}
总位置数: {status.get('total_positions', 0)}
已占用位置: {status.get('occupied_positions', 0)}
可用位置: {status.get('available_positions', 0)}
活跃窗口: {len(status.get('active_windows', {}))}"""
            
            self.status_text.setText(status_text)
            
        except Exception as e:
            error(f"更新状态显示时出错: {str(e)}")
    
    def update_preview(self):
        """更新布局预览"""
        try:
            enabled = self.enable_checkbox.isChecked()
            spacing = self.spacing_spinbox.value()
            
            self.preview_widget.update_layout(enabled, spacing)
            
        except Exception as e:
            error(f"更新预览时出错: {str(e)}")
    
    def reset_layout(self):
        """重置布局"""
        try:
            if self.layout_manager:
                reply = QMessageBox.question(
                    self, "确认重置", 
                    "确定要重置所有窗口位置吗？这将清除当前所有窗口的位置分配。",
                    QMessageBox.Yes | QMessageBox.No,
                    QMessageBox.No
                )
                
                if reply == QMessageBox.Yes:
                    self.layout_manager.reset_layout()
                    success("布局已重置")
                    
                    # 重新加载状态
                    status = self.layout_manager.get_layout_status()
                    self.update_status_display(status)
            else:
                QMessageBox.warning(self, "错误", "布局管理器不可用")
                
        except Exception as e:
            error(f"重置布局时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"重置布局失败: {str(e)}")
    
    def accept_settings(self):
        """接受设置"""
        try:
            if self.layout_manager:
                # 应用设置
                enabled = self.enable_checkbox.isChecked()
                spacing = self.spacing_spinbox.value()
                
                self.layout_manager.set_layout_enabled(enabled)
                self.layout_manager.window_spacing = spacing
                self.layout_manager.save_config()
                
                # 重新计算窗口位置
                self.layout_manager.calculate_window_positions()
                
                success("九宫格布局配置已保存")
                self.accept()
            else:
                QMessageBox.warning(self, "错误", "布局管理器不可用")
                
        except Exception as e:
            error(f"保存设置时出错: {str(e)}")
            QMessageBox.critical(self, "错误", f"保存设置失败: {str(e)}")


class LayoutPreviewWidget(QFrame):
    """布局预览小部件"""
    
    def __init__(self):
        super().__init__()
        self.setFixedSize(300, 200)
        self.setStyleSheet("border: 1px solid #dee2e6; background-color: white;")
        self.enabled = True
        self.spacing = 5
    
    def update_layout(self, enabled, spacing):
        """更新布局参数"""
        self.enabled = enabled
        self.spacing = spacing
        self.update()
    
    def paintEvent(self, event):
        """绘制预览"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        if not self.enabled:
            # 禁用状态
            painter.fillRect(self.rect(), QColor(240, 240, 240))
            painter.setPen(QPen(QColor(150, 150, 150), 2))
            painter.drawText(self.rect(), Qt.AlignCenter, "九宫格布局已禁用")
            return
        
        # 计算网格
        grid_width = self.width() - 20
        grid_height = self.height() - 20
        start_x = 10
        start_y = 10
        
        # 计算每个格子的尺寸
        cell_width = (grid_width - 4 * self.spacing) // 3
        cell_height = (grid_height - 4 * self.spacing) // 3
        
        # 绘制九宫格
        for row in range(3):
            for col in range(3):
                x = start_x + self.spacing + col * (cell_width + self.spacing)
                y = start_y + self.spacing + row * (cell_height + self.spacing)
                
                # 绘制窗口矩形
                rect = painter.drawRect(x, y, cell_width, cell_height)
                painter.fillRect(x, y, cell_width, cell_height, QColor(52, 152, 219, 100))
                painter.setPen(QPen(QColor(52, 152, 219), 2))
                painter.drawRect(x, y, cell_width, cell_height)
                
                # 绘制位置编号
                painter.setPen(QPen(QColor(44, 62, 80), 1))
                painter.drawText(x, y, cell_width, cell_height, Qt.AlignCenter, str(row * 3 + col + 1))
