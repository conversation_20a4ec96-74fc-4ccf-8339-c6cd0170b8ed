[65792:40316:0802/220825.211:INFO:components\enterprise\browser\controller\chrome_browser_cloud_management_controller.cc:202] No machine level policy manager exists.
[49464:70496:0802/220825.388:INFO:third_party\webrtc\rtc_base\cpu_info.cc:57] Available number of cores: 16
[68256:17452:0802/220825.388:INFO:third_party\webrtc\rtc_base\cpu_info.cc:57] Available number of cores: 16
[8004:23588:0802/220825.463:VERBOSE1:components\viz\service\main\viz_main_impl.cc:86] VizNullHypothesis is disabled (not a warning)
[1276:52420:0802/220825.694:INFO:third_party\webrtc\rtc_base\cpu_info.cc:57] Available number of cores: 16
[42624:33644:0802/220825.774:INFO:third_party\webrtc\rtc_base\cpu_info.cc:57] Available number of cores: 16
[65792:40316:0802/220826.491:INFO:CONSOLE:1] "TypeError: Cannot read properties of undefined (reading 'network')", source: https://lf-content-ecology.toutiaostatic.com/obj/tt-content-ecology-fe/pgcfe/mp/web/resource/vendors~a20d0b72_2864a4eef8f54aa1.js (1)
[8004:23588:0802/220826.616:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0802C00C4770000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[65792:40316:0802/220826.722:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0802C00C4770000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (0)
[8004:23588:0802/220827.500:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0802C00C4770000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[65792:40316:0802/220827.545:INFO:CONSOLE:3] "[GroupMarkerNotSet(crbug.com/242999)!:A0802C00C4770000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (3)
[65792:40316:0802/220827.771:INFO:CONSOLE:1] "TypeError: Cannot read properties of undefined (reading 'network')", source: https://lf-content-ecology.toutiaostatic.com/obj/tt-content-ecology-fe/pgcfe/mp/web/resource/vendors~a20d0b72_2864a4eef8f54aa1.js (1)
[8004:23588:0802/220828.226:ERROR:gpu\command_buffer\service\gles2_cmd_decoder_passthrough.cc:1095] [GroupMarkerNotSet(crbug.com/242999)!:A0B02C00C4770000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.
[65792:40316:0802/220828.287:INFO:CONSOLE:0] "[GroupMarkerNotSet(crbug.com/242999)!:A0B02C00C4770000]Automatic fallback to software WebGL has been deprecated. Please use the --enable-unsafe-swiftshader (about:flags#enable-unsafe-swiftshader) flag to opt in to lower security guarantees for trusted content.", source: https://mp.toutiao.com/profile_v4/index (0)
