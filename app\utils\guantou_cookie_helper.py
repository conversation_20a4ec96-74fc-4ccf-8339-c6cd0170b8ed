#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
罐头素材网cookie添加工具 - 自动打开浏览器并保存cookie
"""

import os
import json
import time
import traceback
import socket
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
# 使用PyQt5对话框替代tkinter
try:
    from PyQt5.QtWidgets import QMessageBox, QApplication
    PYQT5_AVAILABLE = True
except ImportError:
    # 如果PyQt5不可用，尝试导入tkinter作为备选
    try:
        import tkinter as tk
        from tkinter import messagebox
        PYQT5_AVAILABLE = False
    except ImportError:
        # 如果两者都不可用，设置标志
        PYQT5_AVAILABLE = None
import logging

# 配置日志
logger = logging.getLogger(__name__)

def show_confirmation_dialog(title, message):
    """显示确认对话框，优先使用PyQt5，备选tkinter"""
    try:
        if PYQT5_AVAILABLE:
            # 使用PyQt5对话框
            app = QApplication.instance()
            if app is None:
                # 如果没有QApplication实例，创建一个临时的
                app = QApplication([])
                temp_app = True
            else:
                temp_app = False

            reply = QMessageBox.question(
                None,
                title,
                message,
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )

            result = reply == QMessageBox.Yes

            # 如果创建了临时应用，清理它
            if temp_app:
                app.quit()

            return result
        elif PYQT5_AVAILABLE is False:
            # 使用tkinter对话框作为备选
            root = tk.Tk()
            root.withdraw()  # 隐藏主窗口
            result = messagebox.askyesno(title, message, parent=root)
            root.destroy()
            return result
        else:
            # 如果两者都不可用，记录警告并返回True（默认确认）
            logger.warning("无法显示确认对话框，GUI库不可用，默认确认操作")
            return True
    except Exception as e:
        logger.error(f"显示确认对话框时出错: {str(e)}")
        # 出错时默认返回True（确认）
        return True

def check_network_connection():
    """检查网络连接状态"""
    try:
        # 尝试连接到罐头素材网站
        socket.create_connection(("www.czgts.cn", 443), timeout=5)
        return True, "网络连接正常"
    except OSError:
        try:
            # 尝试连接到百度
            socket.create_connection(("www.baidu.com", 80), timeout=5)
            return True, "可以连接到百度，但无法连接到罐头素材网"
        except OSError:
            return False, "网络连接异常，无法连接到互联网"

def add_guantou_cookie(account_id, remark, cookie_dir):
    """
    打开浏览器访问罐头素材网，等待用户登录，登录成功后保存cookie

    参数:
        account_id (str): 账号ID
        remark (str): 账号备注
        cookie_dir (str): cookie保存目录

    返回:
        tuple: (success, message, file_path)
            - success (bool): 是否成功
            - message (str): 成功或失败消息
            - file_path (str): 保存的文件路径
    """
    driver = None
    try:
        logger.info(f"准备为账号 {account_id} 添加罐头素材网Cookie")
        logger.debug(f"Cookie保存路径: {cookie_dir}")

        # 检查网络连接
        logger.info("检查网络连接...")
        network_ok, network_msg = check_network_connection()
        if not network_ok:
            logger.error(f"网络连接检查失败: {network_msg}")
            return False, f"添加账号失败: {network_msg}", None
        logger.info(f"网络连接检查结果: {network_msg}")

        # 确保cookie目录存在
        if not os.path.exists(cookie_dir):
            logger.info(f"Cookie目录不存在，创建目录: {cookie_dir}")
            os.makedirs(cookie_dir)

        # 生成cookie文件名
        file_path = os.path.join(cookie_dir, f"{account_id}.json")
        logger.debug(f"Cookie文件路径: {file_path}")

        # 设置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument("--window-size=960,1080")  # 设置窗口大小为半屏
        chrome_options.add_argument("--disable-infobars")  # 禁用信息栏
        chrome_options.add_argument("--disable-extensions")  # 禁用扩展
        chrome_options.add_argument("--no-proxy-server")  # 禁用代理服务器
        chrome_options.add_argument("--disable-gpu")  # 禁用GPU加速
        chrome_options.add_argument("--no-sandbox")  # 禁用沙盒模式
        chrome_options.add_argument("--disable-dev-shm-usage")  # 禁用/dev/shm使用
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])  # 避免被检测
        chrome_options.add_experimental_option("useAutomationExtension", False)  # 避免被检测

        # 使用ChromeDriver管理器创建浏览器
        try:
            logger.info("尝试使用ChromeDriver管理器创建浏览器...")
            from app.utils.chromedriver_manager import create_chrome_driver
            driver = create_chrome_driver(chrome_options)
            logger.info("已成功初始化Chrome浏览器")
        except Exception as e:
            # 如果ChromeDriver管理器失败，尝试直接创建
            logger.warning(f"ChromeDriver管理器创建失败，尝试直接创建: {str(e)}")
            try:
                driver = webdriver.Chrome(options=chrome_options)
                logger.info("已使用直接方式初始化Chrome浏览器")
            except Exception as e2:
                # 如果仍然失败，抛出异常
                logger.error(f"所有初始化Chrome浏览器的方法都失败: {str(e2)}")
                raise Exception(f"无法启动Chrome浏览器，请确保已安装Chrome浏览器并且版本兼容: {str(e2)}")

        # 设置页面加载超时时间
        driver.set_page_load_timeout(60)  # 60秒

        # 打开罐头素材网登录页面
        logger.info("正在打开罐头素材网...")

        # 最多尝试3次
        max_attempts = 3
        for attempt in range(1, max_attempts + 1):
            try:
                logger.info(f"第 {attempt} 次尝试打开罐头素材网...")

                # 先尝试打开一个简单的页面，确保浏览器正常工作
                if attempt > 1:
                    logger.info("先尝试打开百度...")
                    driver.get("https://www.baidu.com")
                    time.sleep(2)

                # 然后打开罐头素材网
                driver.get("https://www.czgts.cn/v1/hots/article")
                logger.info("成功打开罐头素材网")
                break
            except Exception as e:
                logger.warning(f"第 {attempt} 次打开罐头素材网失败: {str(e)}")
                try:
                    # 尝试停止加载
                    driver.execute_script("window.stop();")
                except Exception:
                    pass

                if attempt == max_attempts:
                    logger.error("多次尝试打开罐头素材网均失败")
                    # 尝试打开备用登录页面
                    try:
                        logger.info("尝试打开备用登录页面...")
                        driver.get("https://www.czgts.cn")
                        time.sleep(2)
                    except Exception as e2:
                        logger.warning(f"打开备用登录页面也失败: {str(e2)}")
                else:
                    # 等待一段时间后重试
                    retry_wait = 3 * attempt  # 逐渐增加等待时间
                    logger.info(f"等待 {retry_wait} 秒后重试...")
                    time.sleep(retry_wait)

        # 等待用户手动登录 (最多等待5分钟)
        logger.info("等待用户登录，最多等待5分钟...")
        login_success = False
        max_wait_time = 300  # 5分钟
        start_time = time.time()

        # 用于验证登录状态的XPath
        login_success_xpath = '/html/body/div[1]/div/div[3]/div/div/div[1]/div/div/div'

        while not login_success and (time.time() - start_time) < max_wait_time:
            # 检查是否登录成功
            try:
                # 使用提供的XPath检查是否有用户信息元素
                WebDriverWait(driver, 2).until(
                    EC.presence_of_element_located((By.XPATH, login_success_xpath))
                )
                login_success = True
                logger.info("检测到用户已登录 (XPath验证)")
                break
            except Exception as e:
                logger.debug(f"通过XPath检测登录元素失败: {str(e)}，继续等待")

            # 每2秒检查一次
            time.sleep(2)

        if not login_success:
            logger.error(f"账号 {account_id} 登录超时")
            return False, "登录超时，请重试", None

        # 创建一个确认对话框，让用户确认是否保存cookie
        logger.info("显示确认对话框...")
        confirm = show_confirmation_dialog(
            "确认保存",
            f"已成功登录账号 {account_id}\n点击'是'保存cookie，点击'否'取消"
        )

        if not confirm:
            logger.warning(f"用户取消保存账号 {account_id} 的cookie")
            return False, "用户取消保存", None

        # 获取cookies
        logger.info("获取cookies...")
        cookies = driver.get_cookies()

        # 格式化并保存cookies
        cookie_dict = {}
        for cookie in cookies:
            cookie_dict[cookie['name']] = cookie['value']

        # 添加账号信息
        cookie_data = {
            "accountId": account_id,
            "remark": remark or account_id,
            "cookies": cookie_dict
        }

        # 自动使用AES-256加密保存Cookie
        logger.info(f"保存cookies到文件: {file_path}")
        try:
            # 获取Cookie加密器
            from app.utils.cookie_encryption import get_cookie_encryptor
            encryptor = get_cookie_encryptor()

            # 检查加密功能是否可用
            if encryptor.available:
                # 加密Cookie数据
                encrypted_data = encryptor.encrypt_cookie_data(cookie_data)

                # 创建加密文件结构
                encrypted_file_data = {
                    "encrypted": True,
                    "version": "1.0",
                    "algorithm": "AES-256",
                    "data": encrypted_data,
                    "account_id": account_id,
                    "remark": remark or account_id
                }

                # 保存加密文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(encrypted_file_data, f, ensure_ascii=False, indent=2)

                logger.info(f"账号 {account_id} 添加成功, 文件路径: {file_path}")
                logger.debug("Cookie已自动使用AES-256算法加密保存")
                return True, f"账号 {account_id} 添加成功", file_path
            else:
                # 加密功能不可用，使用明文保存
                logger.warning("Cookie加密功能不可用（cryptography库未安装），使用明文保存")
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(cookie_data, f, ensure_ascii=False, indent=2)

                logger.info(f"账号 {account_id} 添加成功, 文件路径: {file_path}")
                return True, f"账号 {account_id} 添加成功", file_path

        except Exception as encrypt_error:
            # 如果加密失败，回退到明文保存
            logger.warning(f"Cookie加密失败，回退到明文保存: {str(encrypt_error)}")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(cookie_data, f, ensure_ascii=False, indent=2)

            logger.info(f"账号 {account_id} 添加成功, 文件路径: {file_path}")
            return True, f"账号 {account_id} 添加成功", file_path

    except Exception as e:
        error_msg = f"添加账号时发生错误: {str(e)}"
        logger.error(error_msg)
        logger.error(traceback.format_exc())
        return False, error_msg, None

    finally:
        # 确保关闭浏览器
        if driver:
            try:
                logger.info("关闭浏览器...")
                driver.quit()
            except Exception:
                logger.warning("关闭浏览器时出错")
                pass

if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    account_id = "test_account"
    remark = "测试账号"
    cookie_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "cookies", "material")

    success_flag, message, file_path = add_guantou_cookie(account_id, remark, cookie_dir)
    print(f"结果: {success_flag}")
    print(f"消息: {message}")
    print(f"文件路径: {file_path}")
