#!/usr/bin/env python3
"""
测试账号删除权限修复功能
"""

import os
import tempfile
import ctypes
from ctypes import wintypes

def test_file_protection_removal():
    """测试文件保护属性移除功能"""
    
    # 创建临时测试文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        f.write('{"test": "data"}')
        test_file = f.name
    
    try:
        print(f"创建测试文件: {test_file}")
        
        # 设置文件为隐藏和只读
        SetFileAttributes = ctypes.windll.kernel32.SetFileAttributesW
        SetFileAttributes.argtypes = [wintypes.LPWSTR, wintypes.DWORD]
        SetFileAttributes.restype = wintypes.BOOL
        
        FILE_ATTRIBUTE_HIDDEN = 0x02
        FILE_ATTRIBUTE_READONLY = 0x01
        protected_attrs = FILE_ATTRIBUTE_HIDDEN | FILE_ATTRIBUTE_READONLY
        
        if SetFileAttributes(test_file, protected_attrs):
            print("✅ 成功设置文件保护属性（隐藏+只读）")
        else:
            print("❌ 设置文件保护属性失败")
            return False
        
        # 验证文件属性
        GetFileAttributes = ctypes.windll.kernel32.GetFileAttributesW
        GetFileAttributes.argtypes = [wintypes.LPWSTR]
        GetFileAttributes.restype = wintypes.DWORD
        
        attrs = GetFileAttributes(test_file)
        if attrs != 0xFFFFFFFF:
            is_hidden = bool(attrs & FILE_ATTRIBUTE_HIDDEN)
            is_readonly = bool(attrs & FILE_ATTRIBUTE_READONLY)
            print(f"文件属性检查: 隐藏={is_hidden}, 只读={is_readonly}")
        
        # 尝试直接删除（应该失败）
        try:
            os.remove(test_file)
            print("❌ 意外：受保护的文件被直接删除了")
            return False
        except PermissionError:
            print("✅ 预期结果：受保护的文件无法直接删除")
        
        # 移除保护属性
        FILE_ATTRIBUTE_NORMAL = 0x80
        if SetFileAttributes(test_file, FILE_ATTRIBUTE_NORMAL):
            print("✅ 成功移除文件保护属性")
        else:
            print("❌ 移除文件保护属性失败")
            return False
        
        # 再次尝试删除（应该成功）
        try:
            os.remove(test_file)
            print("✅ 成功删除文件")
            return True
        except Exception as e:
            print(f"❌ 删除文件失败: {str(e)}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        # 清理测试文件
        try:
            if os.path.exists(test_file):
                # 确保移除保护属性后再删除
                SetFileAttributes = ctypes.windll.kernel32.SetFileAttributesW
                SetFileAttributes(test_file, 0x80)  # FILE_ATTRIBUTE_NORMAL
                os.remove(test_file)
        except:
            pass
        return False

def main():
    """主测试函数"""
    print("🧪 开始测试账号删除权限修复功能")
    print("=" * 50)
    
    if test_file_protection_removal():
        print("\n✅ 测试通过：文件保护属性移除功能正常工作")
        print("💡 现在删除账号时应该能够正常删除被保护的文件了")
    else:
        print("\n❌ 测试失败：文件保护属性移除功能存在问题")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
